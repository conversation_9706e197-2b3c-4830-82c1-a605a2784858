<script>
  import { onMount } from "svelte";
  import { createEventDispatcher } from "svelte";
  import { toast } from "../components/Toast";
  import { createValue } from "../utils/rest.js";
  import { updatePatch } from "../utils/rest.js";
  import Button from "../components/Button";
  import Select from "../components/Select";
  import TextField from "../components/TextField";

  export let visible = false;
  export let autofocus = true;
  export let autoscroll = true;
  export let adresse = undefined;
  export let allLandItem;

  let clicked = false;
  let focusOn;
  let bottomDiv;
  onMount(async () => {
    console.log(["onMount", autofocus, autoscroll]);
    if (autofocus) focusOn.focus();
    if (autoscroll) bottomDiv.scrollIntoView(false);
  });

  let showUpdate = false;
  let newAdresse = {
    anschrift: {
      strasse: undefined,
      plz: undefined,
      ort: undefined,
      land: undefined,
    },
    aktiv: true,
  };

  $: if (adresse) onChange();
  function onChange() {
    showUpdate = adresse.id;
    newAdresse = {
      id: adresse.id,
      anschrift: { ...adresse.anschrift },
      aktiv: adresse.aktiv,
      version: adresse.version,
    };
    console.log(["onChange", newAdresse]);
  }

  async function onSubmit() {
    try {
      clicked = true;
      if (showUpdate) {
        await updateAdresse();
      } else {
        await createAdresse();
      }
    } finally {
      clicked = false;
    }
  }
  async function onCancel() {
    visible = false;
  }

  const dispatch = createEventDispatcher();
  function createAdresse() {
    return createValue("/api/adresse", newAdresse)
      .then((json) => {
        console.log(["createAdresse", newAdresse, json]);
        visible = false;
        dispatch("create", json);
      })
      .catch((err) => {
        console.log(["createAdresse", newAdresse, err]);
        toast.push(err.toString());
      });
  }
  function updateAdresse() {
    return updatePatch("/api/adresse" + "/" + newAdresse.id, newAdresse)
      .then((json) => {
        console.log(["updateAdresse", newAdresse, json]);
        visible = false;
        dispatch("update", json);
      })
      .catch((err) => {
        console.log(["updateAdresse", newAdresse, err]);
        toast.push(err.toString());
      });
  }
</script>

<form on:submit|preventDefault={onSubmit} on:reset|preventDefault={onCancel}>
  <div class="flex flex-col">
    <div class="w-full">
      <TextField
        bind:this={focusOn}
        bind:value={newAdresse.anschrift.strasse}
        required
        label="Strasse"
        placeholder="Bitte eine Strasse eingeben"
      />
    </div>
    <div class="flex flex-col sm:flex-row gap-1">
      <div class="w-full sm:w-2/3">
        <div class="flex flex-row gap-1">
          <div class="w-1/4">
            <TextField
              bind:value={newAdresse.anschrift.plz}
              required
              label="Postleitzahl"
              placeholder="Bitte eine Postleitzahl eingeben"
            />
          </div>
          <div class="w-3/4">
            <TextField
              bind:value={newAdresse.anschrift.ort}
              required
              label="Ort"
              placeholder="Bitte einen Ort eingeben"
            />
          </div>
        </div>
      </div>
      <div class="w-full sm:w-1/3">
        <Select
          bind:value={newAdresse.anschrift.land}
          valueGetter={(v) => v?.value}
          allItem={allLandItem}
          required
          label="Land"
          placeholder="Bitte ein Land wählen"
        />
      </div>
    </div>
  </div>
  <div class="py-4 flex flex-row gap-1 items-baseline">
    <div class="flex-initial">
      <Button type="submit">Ok</Button>
    </div>
    <div class="flex-initial">
      <slot name="cancel">
        <Button type="reset">Abbrechen</Button>
      </slot>
    </div>
  </div>
</form>

<div class="h-0" bind:this={bottomDiv}>&nbsp;</div>

{#if import.meta.env.DEV}
  <details tabindex="-1">
    <summary>JSON</summary>
    <pre>{JSON.stringify(newAdresse, null, 2)}</pre>
  </details>
{/if}
