<script>
  import "./index.css";
  import { fly } from "svelte/transition";
  import { quadIn } from "svelte/easing";
  import { storedToken } from "./stores/auth.js";
  import Toast from "./components/Toast";
  import Router from "./pager/Router.svelte";
  import Route from "./pager/Route.svelte";
  import RouteNotFound from "./pager/RouteNotFound.svelte";
  import AppHelp from "./AppHelp.svelte";
  import AppHome from "./AppHome.svelte";
  import AppLogo from "./AppLogo.svelte";
  import AppIcon from "./AppIcon.svelte";
  import Galerie from "./Galerie.svelte";
  import <PERSON>rz<PERSON> from "./Uhrzeit.svelte";
  import Rechner from "./Rechner.svelte";
  import Enum from "./pages/Enum.svelte";
  import Ping from "./pages/Ping.svelte";
  import Adresse from "./pages/Adresse.svelte";
  import AdressePage from "./pages/AdressePage.svelte";
  import AufgabePage from "./pages/AufgabePage.svelte";
  import Einsatzplan from "./pages/Einsatzplan.svelte";
  import Gefahr from "./pages/Gefahr.svelte";
  import GefahrPage from "./pages/GefahrPage.svelte";
  import Gruppe from "./pages/Gruppe.svelte";
  import GruppePage from "./pages/GruppePage.svelte";
  import Meldung from "./pages/Meldung.svelte";
  import MeldungPage from "./pages/MeldungPage.svelte";
  import Nutzer from "./pages/Nutzer.svelte";
  import NutzerPage from "./pages/NutzerPage.svelte";
  import Ordner from "./pages/Ordner.svelte";
  import OrdnerPage from "./pages/OrdnerPage.svelte";
  import SeitePage from "./pages/SeitePage.svelte";
  import Projekt from "./pages/Projekt.svelte";
  import ProjektPage from "./pages/ProjektPage.svelte";
  import Projektplan from "./pages/Projektplan.svelte";
  import Prozess from "./pages/Prozess.svelte";
  import ProzessPage from "./pages/ProzessPage.svelte";
  import Risiko from "./pages/Risiko.svelte";
  import RisikoPage from "./pages/RisikoPage.svelte";
  import Terminplan from "./pages/Terminplan.svelte";

  const menuShowAll = $derived($storedToken.allRole.includes("VERWALTUNG"));

  let menuVisible = $state(false);
  function onMenuClicked() {
    menuVisible = false;
  }
</script>

<article class="flex flex-col h-screen">
  <header class="flex justify-between items-center bg-title-200 p-2 h-12">
    <nav class="flex flex-row text-lg text-title-600 gap-1">
      <AppIcon bind:open={menuVisible} />
      <AppLogo bind:open={menuVisible} />
    </nav>
    <nav class="flex flex-row text-lg text-title-600 gap-1">
      <a onclick={onMenuClicked} href="/help">?</a>
    </nav>
  </header>
  <main class="flex-1 overflow-y-auto">
    <Toast />
    {#if menuVisible}
      <aside
        class="w-72 h-full pointer-events-none"
        transition:fly={{
          duration: 200,
          x: -300,
          easing: quadIn,
          opacity: 1,
        }}
      >
        <nav
          class="absolute flex w-full h-full pointer-events-auto z-10 bg-white"
        >
          <div class="w-full">
            <div class="flex flex-col p-2 text-title-600 gap-1">
              <span class="text-lg text-title-900">Anwendung</span>
              <div class="flex flex-col p-4 text-title-600 gap-1">
                <a onclick={onMenuClicked} href="/ping">Ping</a>
                <a onclick={onMenuClicked} href="/projektplan">Projektplan</a>
                <a onclick={onMenuClicked} href="/einsatzplan">Einsatzplan</a>
                <a onclick={onMenuClicked} href="/terminplan">Terminplan</a>
              </div>
            </div>
            {#if menuShowAll}
              <div class="flex flex-col p-2 text-title-600 gap-1">
                <span class="text-lg text-title-900">Grunddaten</span>
                <div class="flex flex-col p-4 text-title-600 gap-1">
                  <a onclick={onMenuClicked} href="/adresse">Adresse</a>
                  <a onclick={onMenuClicked} href="/gefahr">Gefahr</a>
                  <a onclick={onMenuClicked} href="/gruppe">Gruppe</a>
                  <a onclick={onMenuClicked} href="/meldung">Meldung</a>
                  <a onclick={onMenuClicked} href="/nutzer">Nutzer</a>
                  <a onclick={onMenuClicked} href="/ordner">Ordner</a>
                  <a onclick={onMenuClicked} href="/projekt">Projekt</a>
                  <a onclick={onMenuClicked} href="/prozess">Prozess</a>
                  <a onclick={onMenuClicked} href="/risiko">Risiko</a>
                </div>
              </div>
            {/if}
            {#if menuShowAll}
              <div class="flex flex-col p-2 text-title-600 gap-1">
                <span class="text-lg text-title-900">Aufzählungen</span>
                <div class="flex flex-col p-4 text-title-600 gap-1">
                  <a onclick={onMenuClicked} href="/enum/kanal">Kanal</a>
                  <a onclick={onMenuClicked} href="/enum/land">Land</a>
                  <a onclick={onMenuClicked} href="/enum/quelle">Quelle</a>
                  <a onclick={onMenuClicked} href="/enum/sprache">Sprache</a>
                </div>
              </div>
            {/if}
            <div class="flex flex-col p-2 text-title-600 gap-1">
              <span class="text-lg text-title-900">Experimente</span>
              <div class="flex flex-col p-4 text-title-600 gap-1">
                <a onclick={onMenuClicked} href="/galerie">Galerie</a>
                <a onclick={onMenuClicked} href="/uhrzeit">Uhrzeit</a>
                <a onclick={onMenuClicked} href="/rechner">Rechner</a>
              </div>
            </div>
          </div>
        </nav>
      </aside>
    {/if}
    <Router>
      <Route path="/" component={AppHome} />
      <Route path="/home" component={AppHome} />
      <Route path="/help" component={AppHelp} />
      <Route path="/ping" component={Ping} />
      <Route path="/adresse" component={Adresse} />
      <Route path="/adresse/:adresseId" component={AdressePage} />
      <Route path="/aufgabe/:aufgabeId" component={AufgabePage} />
      <Route path="/gefahr" component={Gefahr} />
      <Route path="/gefahr/:gefahrId" component={GefahrPage} />
      <Route path="/gruppe" component={Gruppe} />
      <Route path="/gruppe/:gruppeId" component={GruppePage} />
      <Route path="/meldung" component={Meldung} />
      <Route path="/meldung/:meldungId" component={MeldungPage} />
      <Route path="/nutzer" component={Nutzer} />
      <Route path="/nutzer/:nutzerId" component={NutzerPage} />
      <Route path="/ordner" component={Ordner} />
      <Route path="/ordner/:ordnerId" component={OrdnerPage} />
      <Route path="/seite/:seiteId" component={SeitePage} />
      <Route path="/projekt" component={Projekt} />
      <Route path="/projekt/:projektId" component={ProjektPage} />
      <Route path="/prozess" component={Prozess} />
      <Route path="/prozess/:prozessId" component={ProzessPage} />
      <Route path="/risiko" component={Risiko} />
      <Route path="/risiko/:risikoId" component={RisikoPage} />
      <Route path="/projektplan" component={Projektplan} />
      <Route path="/einsatzplan" component={Einsatzplan} />
      <Route path="/terminplan" component={Terminplan} />
      <Route path="/enum/land" component={Enum} art="land" />
      <Route path="/enum/sprache" component={Enum} art="sprache" />
      <Route path="/enum/kanal" component={Enum} art="kanal" />
      <Route path="/enum/quelle" component={Enum} art="quelle" />
      <Route path="/galerie" component={Galerie} />
      <Route path="/uhrzeit" component={Uhrzeit} />
      <Route path="/rechner" component={Rechner} />
      <RouteNotFound>
        <h1>Ups!</h1>
      </RouteNotFound>
    </Router>
  </main>
  <footer class="flex justify-center bg-title-200 p-2 h-10">
    <nav class="flex flex-row text-sm text-title-600 gap-1">
      <a onclick={onMenuClicked} href="/help">Impressum</a>
    </nav>
  </footer>
</article>
