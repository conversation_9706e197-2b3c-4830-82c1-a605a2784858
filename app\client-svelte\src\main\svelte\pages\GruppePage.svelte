<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import Button from "../components/Button";
  import { toast } from "../components/Toast";
  import { loadOneValue } from "../utils/rest.js";
  import GruppeEditor from "./GruppeEditor.svelte";
  import GruppeNutzerLister from "./GruppeNutzerLister.svelte";

  let { gruppeId } = $props();

  let loading = true;
  onMount(async () => {
    try {
      loading = true;
      await reloadOneGruppe();
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  async function onChange() {
    try {
      loading = true;
      await tick();
      await reloadOneGruppe();
    } finally {
      loading = false;
    }
  }
  async function onCancel() {
    await reloadOneGruppe();
  }

  let gruppe = $state();
  function reloadOneGruppe() {
    return loadOneValue("/api/gruppe/" + gruppeId)
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadOneGruppe", msg]);
        gruppe = json;
      })
      .catch((err) => {
        console.log(["reloadOneGruppe", err]);
        gruppe = {};
        toast.push(err.toString());
      });
  }
</script>

<div class="flex flex-col gap-1 ml-2 mr-2">
  {#if gruppe}{#key gruppe}
      <h1>{gruppe.name}</h1>
      <fieldset class="p-4 border-2">
        <legend class="text-xl">Gruppe bearbeiten</legend>
        <GruppeEditor
          on:update={onChange}
          visible={true}
          autofocus={true}
          autoscroll={false}
          {gruppe}
        >
          {#snippet cancel()}
            <div>
              <Button type="reset" onclick={onCancel}>Verwerfen</Button>
            </div>
          {/snippet}
        </GruppeEditor>
      </fieldset>
      <fieldset class="p-4 border-2">
        <legend class="text-xl">Gruppenmitglieder</legend>
        <GruppeNutzerLister {gruppeId} />
      </fieldset>
    {/key}{/if}
</div>
