<script>
  import { onMount } from "svelte";
  import { createEventDispatcher } from "svelte";
  import { toast } from "../components/Toast";
  import { updatePatch } from "../utils/rest.js";
  import Button from "../components/Button";
  import Icon from "../components/Icon";
  import Select from "../components/Select";
  import TextField from "../components/TextField";

  export let visible = false;
  export let autofocus = true;
  export let autoscroll = true;
  export let projekt = undefined;
  export let allNutzerItem;

  let clicked = false;
  let focusOn;
  let bottomDiv;
  onMount(async () => {
    console.log(["onMount", autofocus, autoscroll]);
    if (autofocus) focusOn.focus();
    if (autoscroll) bottomDiv.scrollIntoView(false);
  });

  let newProjekt = {
    name: "",
    besitzerId: undefined,
    allMitgliedItem: [],
    aktiv: true,
  };
  let newMitgliedItem = {};
  let newMitgliedFocusOn;

  $: if (projekt && projekt.id) onChange();
  async function onChange() {
    newProjekt = {
      id: projekt.id,
      code: projekt.code,
      name: projekt.name,
      besitzerId: projekt.besitzerId,
      allMitgliedItem: [...projekt.allMitgliedItem],
      aktiv: projekt.aktiv,
      version: projekt.version,
    };
    newMitgliedItem = {};
    console.log(["onChange", newProjekt]);
  }

  async function onSubmit() {
    try {
      clicked = true;
      newProjekt.allMitglied = newProjekt.allMitgliedItem.map(
        (e) => "/api/nutzer/" + e.value
      );
      await updateProjekt();
    } finally {
      clicked = false;
    }
  }
  async function onCancel() {
    visible = false;
  }

  function onInsertMitglied() {
    newProjekt.allMitgliedItem = [
      ...newProjekt.allMitgliedItem,
      newMitgliedItem,
    ];
    newMitgliedItem = {};
    newMitgliedFocusOn.focus();
    console.log(["onInsertMitglied", newProjekt]);
  }
  function onRemoveMitglied(index) {
    newProjekt.allMitgliedItem = [
      ...newProjekt.allMitgliedItem.slice(0, index),
      ...newProjekt.allMitgliedItem.slice(index + 1),
    ];
    newMitgliedItem = {};
    newMitgliedFocusOn.focus();
    console.log(["onRemoveMitglied", newProjekt]);
  }

  const dispatch = createEventDispatcher();
  function updateProjekt() {
    return updatePatch("/api/projekt" + "/" + newProjekt.id, newProjekt)
      .then((json) => {
        console.log(["updateProjekt", newProjekt, json]);
        visible = false;
        dispatch("update", json);
      })
      .catch((err) => {
        console.log(["updateProjekt", newProjekt, err]);
        toast.push(err.toString());
      });
  }
</script>

<form on:submit|preventDefault={onSubmit} on:reset|preventDefault={onCancel}>
  <div class="flex flex-col">
    <div class="w-full">
      <Select
        bind:this={focusOn}
        bind:value={newProjekt.besitzerId}
        allItem={allNutzerItem}
        valueGetter={(v) => v?.value}
        label="Besitzer"
        placeholder="Bitte eine Person wählen"
      />
    </div>
    <div class="w-full">
      {#each newProjekt.allMitgliedItem as mitgliedItem, i}
        <div class="flex flex-row gap-1 items-baseline">
          <div class="grow">
            <TextField
              bind:value={mitgliedItem.text}
              disabled
              title={mitgliedItem.value}
              label={i + 1 + ". Mitglied"}
            />
          </div>
          <div class="place-self-center">
            <Icon on:click={() => onRemoveMitglied(i)} name="delete" outlined />
          </div>
        </div>
      {/each}
      <div class="flex flex-row gap-1 items-baseline">
        <div class="grow">
          <Select
            bind:this={newMitgliedFocusOn}
            bind:value={newMitgliedItem}
            allItem={allNutzerItem}
            label="Neues Mitglied"
            placeholder="Bitte eine Person wählen"
          />
        </div>
        <div class="place-self-center">
          <Icon
            on:click={() => onInsertMitglied()}
            disabled={!newMitgliedItem.value}
            name="add"
            outlined
          />
        </div>
      </div>
    </div>
  </div>
  <div class="py-4 flex flex-row gap-1 items-baseline">
    <div class="flex-initial">
      <Button type="submit">Ok</Button>
    </div>
    <div class="flex-initial">
      <slot name="cancel">
        <Button type="reset">Abbrechen</Button>
      </slot>
    </div>
  </div>
</form>

<div class="h-0" bind:this={bottomDiv}>&nbsp;</div>

{#if import.meta.env.DEV}
  <details tabindex="-1">
    <summary>JSON</summary>
    <pre>{JSON.stringify(newProjekt, null, 2)}</pre>
  </details>
{/if}
