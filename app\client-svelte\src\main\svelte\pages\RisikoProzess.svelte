<script>
  import { onMount } from "svelte";
  import { createEventDispatcher } from "svelte";
  import { toast } from "../components/Toast";
  import { updatePatch } from "../utils/rest.js";
  import Button from "../components/Button";
  import Icon from "../components/Icon";
  import Select from "../components/Select";
  import TextField from "../components/TextField";

  export let visible = false;
  export let autofocus = true;
  export let autoscroll = true;
  export let risiko;
  export let allProzessItem;

  let clicked = false;
  let bottomDiv;
  onMount(async () => {
    console.log(["onMount", autofocus, autoscroll]);
    if (autofocus) newProzessFocusOn.focus();
    if (autoscroll) bottomDiv.scrollIntoView(false);
  });

  let newRisiko = {
    titel: undefined,
    allProzessItem: [],
    aktiv: true,
  };
  let newProzessItem = {};
  let newProzessFocusOn;

  $: if (risiko && risiko.id) onChange();
  async function onChange() {
    newRisiko = { ...newRisiko, ...risiko };
    console.log(["onChange", newRisiko]);
  }

  async function onSubmit() {
    try {
      clicked = true;
      newRisiko.allProzess = newRisiko.allProzessItem.map(
        (e) => "/api/prozess/" + e.value
      );
      await updateRisiko();
    } finally {
      clicked = false;
    }
  }
  async function onCancel() {
    visible = false;
  }

  function onInsertProzess() {
    newRisiko.allProzessItem = [...newRisiko.allProzessItem, newProzessItem];
    newProzessItem = {};
    newProzessFocusOn.focus();
    console.log(["onInsertProzess", newRisiko]);
  }
  function onRemoveProzess(index) {
    newRisiko.allProzessItem = [
      ...newRisiko.allProzessItem.slice(0, index),
      ...newRisiko.allProzessItem.slice(index + 1),
    ];
    newProzessItem = {};
    newProzessFocusOn.focus();
    console.log(["onRemoveProzess", newRisiko]);
  }

  const dispatch = createEventDispatcher();
  function updateRisiko() {
    return updatePatch("/api/risiko" + "/" + newRisiko.id, newRisiko)
      .then((json) => {
        console.log(["updateRisiko", newRisiko, json]);
        visible = false;
        dispatch("update", json);
      })
      .catch((err) => {
        console.log(["updateRisiko", newRisiko, err]);
        toast.push(err.toString());
      });
  }
</script>

<form on:submit|preventDefault={onSubmit} on:reset|preventDefault={onCancel}>
  <div class="flex flex-col">
    <div class="w-full">
      {#each newRisiko.allProzessItem as prozessItem, i}
        <div class="flex flex-row gap-1 items-baseline">
          <div class="w-full">
            <TextField
              bind:value={prozessItem.text}
              disabled
              title={prozessItem.value}
              label={i + 1 + ". Prozess"}
            />
          </div>
          <div class="place-self-center">
            <Icon onclick={() => onRemoveProzess(i)} name="delete" outlined />
          </div>
        </div>
      {/each}
      <div class="flex flex-row gap-1 items-baseline">
        <div class="w-full">
          <Select
            bind:this={newProzessFocusOn}
            bind:value={newProzessItem}
            allItem={allProzessItem}
            label="Neuer Prozess"
            placeholder="Bitte einen Prozess wählen"
          />
        </div>
        <div class="place-self-center">
          <Icon
            onclick={() => onInsertProzess()}
            disabled={!newProzessItem.value}
            name="add"
            outlined
          />
        </div>
      </div>
    </div>
  </div>
  <div class="py-4 flex flex-row gap-1 items-baseline">
    <div class="flex-initial">
      <Button type="submit">Ok</Button>
    </div>
    <div class="flex-initial">
      <slot name="cancel">
        <Button type="reset">Abbrechen</Button>
      </slot>
    </div>
  </div>
</form>

<div class="h-0" bind:this={bottomDiv}>&nbsp;</div>

{#if import.meta.env.DEV}
  <details tabindex="-1">
    <summary>JSON</summary>
    <pre>{JSON.stringify(newRisiko, null, 2)}</pre>
  </details>
{/if}
