package esy.test;

import com.microsoft.playwright.APIRequest;
import com.microsoft.playwright.APIRequestContext;
import com.microsoft.playwright.APIResponse;
import com.microsoft.playwright.Playwright;
import esy.rest.RestApiMock;
import lombok.NonNull;
import org.springframework.core.io.Resource;

import java.io.IOException;
import java.time.Duration;
import java.util.function.Function;

@SuppressWarnings("java:S5960") // assertions ok
public abstract class PlaywrightDocAssertionBase implements AutoCloseable {

    private final PlaywrightDocTestEnv env;

    private final APIRequestContext context;

    protected PlaywrightDocAssertionBase(@NonNull final Playwright playwright, @NonNull final PlaywrightDocTestEnv env) {
        this.env = env;
        this.context = playwright.request().newContext(new APIRequest.NewContextOptions()
                .setBaseURL(env.getPrinterUrl())
                .setTimeout(Duration.ofMinutes(5L).toMillis()));
    }

    @Override
    public void close() {
    }

    protected RestApiMock createMock(@NonNull final byte[] payload) throws IOException {
        final var mock = new RestApiMock(env.getBackendPort(), uri -> payload);
        context.post(mock.serverURI("/jwt"));
        return mock;
    }

    protected RestApiMock createMock(@NonNull final String payload) throws IOException {
        return createMock(payload.getBytes());
    }

    protected RestApiMock createMock(@NonNull final Resource payload) throws IOException {
        return createMock(payload.getContentAsByteArray());
    }

    protected final <T> T doWithApi(@NonNull final Function<APIRequestContext, APIResponse> operation, @NonNull final Function<APIResponse, T> converter) {
        return converter.apply(operation.apply(context));
    }
}
