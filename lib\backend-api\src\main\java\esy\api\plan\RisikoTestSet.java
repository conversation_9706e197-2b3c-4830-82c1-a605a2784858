package esy.api.plan;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;

import java.time.LocalDate;
import java.util.Map;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.function.Function.identity;

@RequiredArgsConstructor
@SuppressWarnings("java:S1192") // allow duplicating literals
public class RisikoTestSet {

    private final UnaryOperator<Risiko> saver;

    @SuppressWarnings("java:S1192")
    public Map<String, Risiko> createAllRisiko(@NonNull final LocalDate datum) {
        return Stream.of(
                        Risiko.parseJson("{" +
                                "\"titel\":\"Ausfall der Stromversorgung\"," +
                                "\"text\":\"Betriebsausfall, weil wichtige Anwendungen nicht erreichbar sind.\"," +
                                "\"termin\":\"" + datum + "\"," +
                                "\"terminserie\":\"" + Terminserie.W + "\"," +
                                "\"aktiv\":\"true\"" +
                                "}"),
                        Risiko.parseJson("{" +
                                "\"titel\":\"Ausfall von Arbeitsgeräten\"," +
                                "\"text\":\"Betriebseinschränkungen, weil Beschäftigte nicht mehr arbeitsfähig sind.\"," +
                                "\"termin\":\"" + datum + "\"," +
                                "\"terminserie\":\"" + Terminserie.T + "\"," +
                                "\"aktiv\":\"true\"" +
                                "}"),
                        Risiko.parseJson("{" +
                                "\"titel\":\"Ausfall der Internet-Anbindung\"," +
                                "\"text\":\"Betriebsausfall, weil wichtige Anwendungen nicht erreichbar sind.\"," +
                                "\"termin\":\"" + datum + "\"," +
                                "\"terminserie\":\"" + Terminserie.T + "\"," +
                                "\"aktiv\":\"true\"" +
                                "}"),
                        Risiko.parseJson("{" +
                                "\"titel\":\"Infrastruktur nicht sicher\"," +
                                "\"text\":\"Schäden durch erfolgreiche Angriffe auf die Infrastruktur.\"," +
                                "\"termin\":\"" + datum.plusYears(1) + "\"," +
                                "\"terminserie\":\"" + Terminserie.J + "\"," +
                                "\"aktiv\":\"true\"" +
                                "}"))
                .map(e -> saver.apply(e.verify()))
                .collect(Collectors.toMap(Risiko::getTitel, identity()));
    }

    public void updateRisikoGefahr(@NonNull final Map<String, Risiko> allRisiko, @NonNull final Map<String, Gefahr> allGefahr) {
        allRisiko.values().stream()
                .filter(e -> e.getTitel().startsWith("Ausfall"))
                .map(e -> e.addGefahr(allGefahr.get("Feuer")))
                .map(e -> e.addGefahr(allGefahr.get("Wasser")))
                .forEach(e -> saver.apply(e).verify());
    }

    public void updateRisikoProzess(@NonNull final Map<String, Risiko> allRisiko, @NonNull final Map<String, Prozess> allProzess) {
        allRisiko.values().stream()
                .filter(e -> e.getTitel().startsWith("Ausfall"))
                .map(e -> e.addProzess(allProzess.get("Produktion")))
                .forEach(e -> saver.apply(e).verify());
    }
}
