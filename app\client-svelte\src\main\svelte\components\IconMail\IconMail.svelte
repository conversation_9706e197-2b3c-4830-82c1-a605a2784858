<script lang="ts">
  import { mail } from "../../utils/rest.js";
  import { toast } from "../Toast";
  let {
    checked = $bindable(false),
    clicked = $bindable(0),
    disabled = false,
    name = "email",
    job,
    outlined = false,
    title = undefined,
    onclick = undefined,
    onsuccess = undefined,
    onfailure = undefined,
    ...elementProps
  } = $props();

  let element;
  export function focus() {
    element?.focus();
  }

  function handleClick(_event: MouseEvent) {
    checked = !checked;
    clicked++;
    const toastId = toast.push(title, { duration: 60000 });
    const json = job instanceof Function ? job() : job;
    onclick?.(json);
    mail(json)
      .then((log) => {
        console.log(["mail", json, log]);
        toast.pop(toastId);
        onsuccess?.(json);
      })
      .catch((err) => {
        console.log(["mail", json, err]);
        toast.pop(toastId);
        toast.push(err.toString());
        onfailure?.(json);
      });
  }
</script>

<button
  type="button"
  aria-label={name}
  bind:this={element}
  {...elementProps}
  {title}
  {disabled}
  class:disabled
  class="text-xl text-white w-12 h-12 rounded-full p-2 disabled:opacity-50 hover:opacity-90 focus:ring bg-primary-500"
  class:outlined
  onclick={handleClick}
>
  <div class="flex justify-center items-center">
    <i
      {title}
      aria-hidden="true"
      class="material-icons icon text-xl select-none"
    >
      {name}
    </i>
  </div>
</button>
