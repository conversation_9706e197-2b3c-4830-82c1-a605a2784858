<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import { toast } from "../components/Toast";
  import { loadAllValue } from "../utils/rest.js";
  import { updatePatch } from "../utils/rest.js";
  import { removeValue } from "../utils/rest.js";
  import { Circle } from "svelte-loading-spinners";
  import Icon from "../components/Icon";
  import IconMail from "../components/IconMail";
  import IconOpenInTab from "../components/IconOpenInTab";
  import IconPrint from "../components/IconPrint";
  import IconSaveAsZip from "../components/IconSaveAsZip";
  import Checkbox from "../components/Checkbox";
  import TextField from "../components/TextField";
  import NutzerEditor from "./NutzerEditor.svelte";
  import NutzerGruppe from "./NutzerGruppe.svelte";
  import NutzerProfil from "./NutzerProfil.svelte";

  let allAdresseItem = [];
  let allGruppeItem = [];
  let allSpracheItem = [];
  let allKanalItem = [];
  let loading = true;
  onMount(async () => {
    try {
      loading = true;
      allAdresseItem = await loadAllValue("/api/adresse/search/findAllItem");
      console.log(["onMount", allAdresseItem]);
      allGruppeItem = await loadAllValue("/api/gruppe/search/findAllItem");
      console.log(["onMount", allGruppeItem]);
      allKanalItem = await loadAllValue("/api/enum/kanal");
      console.log(["onMount", allKanalItem]);
      allSpracheItem = await loadAllValue("/api/enum/sprache");
      console.log(["onMount", allSpracheItem]);
      await reloadAllNutzer();
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  let nutzerId = undefined;
  async function onNutzerClicked(nutzer) {
    nutzerId = nutzer.id;
  }
  async function onNutzerRemoveClicked(nutzer) {
    nutzerId = nutzer.id;
    await removeNutzer(nutzer);
  }
  let nutzerEditorCreate = false;
  async function onNutzerEditorCreateClicked() {
    nutzerEditorCreate = true;
  }
  let nutzerEditorUpdate = false;
  async function onNutzerEditorUpdateClicked(nutzer) {
    nutzerId = nutzer.id;
    nutzerEditorUpdate = true;
    nutzerGruppeUpdate = false;
    nutzerProfilUpdate = false;
  }
  let nutzerGruppeUpdate = false;
  async function onNutzerGruppeUpdateClicked(nutzer) {
    nutzerId = nutzer.id;
    nutzerEditorUpdate = false;
    nutzerGruppeUpdate = true;
    nutzerProfilUpdate = false;
  }
  let nutzerProfilUpdate = false;
  async function onNutzerProfilUpdateClicked(nutzer) {
    nutzerId = nutzer.id;
    nutzerEditorUpdate = false;
    nutzerGruppeUpdate = false;
    nutzerProfilUpdate = true;
  }
  $: nutzerEditorDisabled =
    nutzerEditorCreate ||
    nutzerEditorUpdate ||
    nutzerGruppeUpdate ||
    nutzerProfilUpdate;

  let nutzerFilter;
  function nutzerFilterParameter() {
    if (!nutzerFilter) return "";
    return "&name=" + encodeURIComponent(nutzerFilter);
  }
  function nutzerSortParameter() {
    return "?sort=name";
  }
  async function onNutzerFilterClicked() {
    try {
      loading = true;
      await tick();
      await reloadAllNutzer();
    } finally {
      loading = false;
    }
  }

  let allNutzer = [];
  function onCreateNutzer(nutzer) {
    allNutzer = allNutzer.toSpliced(0, 0, nutzer);
  }
  function onUpdateNutzer(nutzer) {
    let index = allNutzer.findIndex((e) => e.id === nutzer.id);
    if (index > -1) allNutzer = allNutzer.toSpliced(index, 1, nutzer);
  }
  function onRemoveNutzer(nutzer) {
    let index = allNutzer.findIndex((e) => e.id === nutzer.id);
    if (index > -1) allNutzer = allNutzer.toSpliced(index, 1);
  }
  function reloadAllNutzer() {
    const query = nutzerSortParameter() + nutzerFilterParameter();
    return loadAllValue("/api/nutzer" + query)
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadAllNutzer", query, msg]);
        allNutzer = json;
      })
      .catch((err) => {
        console.log(["reloadAllNutzer", query, err]);
        allNutzer = [];
        toast.push(err.toString());
      });
  }

  function updateNutzer(nutzer) {
    return updatePatch("/api/nutzer/" + nutzer.id, nutzer)
      .then((json) => {
        console.log(["updateNutzer", nutzer, json]);
        onUpdateNutzer(json);
      })
      .catch((err) => {
        console.log(["updateNutzer", nutzer, err]);
        toast.push(err.toString());
      });
  }
  function removeNutzer(nutzer) {
    const text = nutzer.name;
    const hint = text.length > 20 ? text.substring(0, 20) + "..." : text;
    if (!confirm("Nutzer '" + hint + "' wirklich löschen?")) return;
    return removeValue("/api/nutzer" + "/" + nutzer.id)
      .then((json) => {
        console.log(["onRemoveNutzer", nutzer, json]);
        onRemoveNutzer(json);
      })
      .catch((err) => {
        console.log(["onRemoveNutzer", nutzer, err]);
        toast.push(err.toString());
      });
  }

  function katalogMailJob() {
    const text = [
      "Im Anhang der E-Mail befindet sich",
      "der Katalog der registrierten Nutzer.",
    ].join("\n");
    return {
      to: "<EMAIL>",
      subject: "Katalog der Nutzer",
      html: "<p>" + text + "</p>",
      text: text,
      attachments: [
        {
          filename: "katalog.pdf",
          path: "/doc/nutzer/katalog",
        },
      ],
    };
  }

  function katalogPrintJob() {
    return {
      printer: "nowhere",
      files: [
        {
          path: "/doc/nutzer/katalog",
        },
      ],
    };
  }

  function katalogSaveJob() {
    return {
      archive: "katalog.zip",
      files: [
        {
          filename: "katalog.pdf",
          path: "/doc/nutzer/katalog",
        },
      ],
    };
  }
</script>

<div class="flex flex-row gap-1 items-baseline justify-between">
  <h1 title="Liste der Nutzer, ggfs. gefiltert, jedes Element editierbar">
    Nutzer
  </h1>
  <div class="invisible sm:visible flex flex-row gap-1 pr-4">
    <div class="w-max">
      <IconOpenInTab
        path="/doc/nutzer/katalog.html"
        disabled={!allNutzer.length}
        title="Katalog der Nutzer im Format HTML anzeigen"
        name="html"
        outlined
      />
    </div>
    <div class="w-max">
      <IconOpenInTab
        path="/doc/nutzer/katalog.adoc"
        disabled={!allNutzer.length}
        title="Katalog der Nutzer im Format Asciidoc anzeigen"
        name="library_books"
        outlined
      />
    </div>
    <div class="w-max">
      <IconOpenInTab
        path="/doc/nutzer/katalog.pdf"
        disabled={!allNutzer.length}
        title="Katalog der Nutzer im Format PDF anzeigen"
        name="picture_as_pdf"
        outlined
      />
    </div>
    <div class="w-max">
      <IconOpenInTab
        path="/doc/nutzer/katalog.xlsx"
        disabled={!allNutzer.length}
        title="Katalog der Nutzer im Format XLS anzeigen"
        name="table_rows"
        outlined
      />
    </div>
    <div class="w-max">
      <IconSaveAsZip
        job={() => katalogSaveJob()}
        disabled={!allNutzer.length}
        title="Katalog der Nutzer lokal speichern"
        name="download"
        outlined
      />
    </div>
    <div class="w-max">
      <IconMail
        job={() => katalogMailJob()}
        disabled={!allNutzer.length}
        title="Katalog der Nutzer als E-Mail senden"
        outlined
      />
    </div>
    <div class="w-max">
      <IconPrint
        job={() => katalogPrintJob()}
        disabled={!allNutzer.length}
        title="Katalog der Nutzer drucken"
        outlined
      />
    </div>
  </div>
</div>
<div class="flex flex-col gap-1 ml-2 mr-2">
  <form on:submit|preventDefault={onNutzerFilterClicked}>
    <div class="flex flex-row gap-1 items-center pr-2">
      <div class="w-full">
        <TextField
          bind:value={nutzerFilter}
          label="Filter"
          placeholder="Bitte Filterkriterien eingeben"
        />
      </div>
      <div class="w-min">
        <Icon type="submit" name="search" outlined />
      </div>
    </div>
  </form>
  {#if loading}
    <div class="h-screen flex justify-center items-center">
      <Circle size="60" unit="px" duration="1s" />
    </div>
  {:else}
    <table class="table-fixed">
      <thead class="justify-between">
        <tr class="bg-title-100">
          <th class="px-2 py-3 table-cell">
            <span class="text-title-600 text-sm">Aktiv</span>
          </th>
          <th class="px-2 py-3 text-left w-1/4 hidden sm:table-cell">
            <span class="text-title-600">Name</span>
          </th>
          <th class="px-2 py-3 text-left w-full sm:w-2/4 table-cell">
            <span class="text-title-600">E-Mail</span>
          </th>
          <th class="px-2 py-3 text-left w-1/4 hidden sm:table-cell">
            <span class="text-title-600">Sprachen</span>
          </th>
          <th class="px-2 py-3 text-right w-0 table-cell">
            <Icon
              onclick={() => onNutzerEditorCreateClicked()}
              disabled={nutzerEditorDisabled}
              title="Nutzer hinzufügen"
              name="add"
              outlined
            />
          </th>
        </tr>
      </thead>
      <tbody>
        {#if nutzerEditorCreate}
          <tr>
            <td class="px-2" colspan="5">
              <NutzerEditor
                bind:visible={nutzerEditorCreate}
                on:create={(e) => onCreateNutzer(e.detail)}
                {allAdresseItem}
                {allSpracheItem}
              />
            </td>
          </tr>
        {/if}
        {#each allNutzer as nutzer, i}
          <tr
            on:click={(e) => onNutzerClicked(nutzer)}
            title={nutzer.id}
            class:border-l-2={nutzerId === nutzer.id}
            class:bg-gray-100={i % 2 === 1}
          >
            <td class="px-2 py-3 table-cell">
              <Checkbox
                bind:checked={nutzer.aktiv}
                on:change={() => updateNutzer(nutzer)}
              />
            </td>
            <td class="px-2 py-3 text-left hidden sm:table-cell">
              <div class="underline text-blue-600">
                <a href={"/nutzer/" + nutzer.id}>{nutzer.name}</a>
              </div>
            </td>
            <td class="px-2 py-3 text-left table-cell">
              <div class="underline text-blue-600">
                <a href={"mailto:" + nutzer.mail}>{nutzer.mail}</a>
              </div>
            </td>
            <td class="px-2 py-3 text-left hidden sm:table-cell">
              <div class="flex flex-row flex-wrap gap-1">
                {#each nutzer.allSprache as sprache}
                  <span class="p-1 text-xs text-white bg-primary-500 rounded">
                    {sprache}
                  </span>
                {/each}
              </div>
            </td>
            <td class="px-2 py-3 table-cell">
              <div
                class="grid grid-cols-1 md:grid-cols-4 items-center gap-1 w-max"
              >
                <Icon
                  onclick={() => onNutzerProfilUpdateClicked(nutzer)}
                  disabled={nutzerEditorDisabled}
                  title="Nutzer bearbeiten"
                  name="face"
                  outlined
                />
                <Icon
                  onclick={() => onNutzerGruppeUpdateClicked(nutzer)}
                  disabled={nutzerEditorDisabled}
                  title="Nutzer bearbeiten"
                  name="group"
                  outlined
                />
                <Icon
                  onclick={() => onNutzerRemoveClicked(nutzer)}
                  disabled={nutzerEditorDisabled || nutzer.aktiv}
                  title="Nutzer löschen"
                  name="delete"
                  outlined
                />
                <Icon
                  onclick={() => onNutzerEditorUpdateClicked(nutzer)}
                  disabled={nutzerEditorDisabled}
                  title="Nutzer bearbeiten"
                  name="edit"
                  outlined
                />
              </div>
            </td>
          </tr>
          {#if nutzerEditorUpdate && nutzerId === nutzer.id}
            <tr>
              <td class="border-l-4 px-2" colspan="5">
                <NutzerEditor
                  bind:visible={nutzerEditorUpdate}
                  on:update={(e) => onUpdateNutzer(e.detail)}
                  {nutzer}
                  {allAdresseItem}
                  {allSpracheItem}
                />
              </td>
            </tr>
          {/if}
          {#if nutzerProfilUpdate && nutzerId === nutzer.id}
            <tr>
              <td class="border-l-4 px-2" colspan="5">
                <NutzerProfil
                  bind:visible={nutzerProfilUpdate}
                  on:update={(e) => onUpdateNutzer(e.detail)}
                  {nutzer}
                  {allKanalItem}
                />
              </td>
            </tr>
          {/if}
          {#if nutzerGruppeUpdate && nutzerId === nutzer.id}
            <tr>
              <td class="border-l-4 px-2" colspan="5">
                <NutzerGruppe
                  bind:visible={nutzerGruppeUpdate}
                  on:update={(e) => onUpdateNutzer(e.detail)}
                  {nutzer}
                  {allGruppeItem}
                />
              </td>
            </tr>
          {/if}
        {:else}
          <tr>
            <td class="px-2" colspan="5">Keine Nutzer</td>
          </tr>
        {/each}
      </tbody>
    </table>
  {/if}
</div>
