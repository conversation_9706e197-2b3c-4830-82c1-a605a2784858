<script>
  import { saveCsv } from "../../utils/rest.js";
  import Icon from "../Icon";
  import Select from "../Select";
  import TextField from "../TextField";
  import { toast } from "../Toast";
  import filterProps from "../filterProps.js";
  const props = filterProps(
    ["allItem", "disabled", "label", "nullable", "title", "url", "value"],
    $$props
  );
  export let allItem;
  export let disabled = false;
  export let label = undefined;
  export let nullable = false;
  export let placeholder = undefined;
  export let title = undefined;
  export let url;
  export let value;

  let csvMode = false;
  let csvValue;
  function onEditClicked() {
    csvMode = true;
  }
  function onSaveClicked() {
    if (csvValue && csvValue.length) {
      saveCsv(url, csvValue)
        .then((json) => {
          console.log(["onSaveClicked", csvValue, json]);
          allItem.push(json);
          value = json;
          csvMode = false;
          csvValue = undefined;
        })
        .catch((err) => {
          console.log(["onSaveClicked", csvValue, err]);
          toast.push(err.toString());
        });
    } else {
      csvMode = false;
      csvValue = undefined;
    }
  }
</script>

<div class="flex flex-row gap-1 items-baseline">
  {#if csvMode}
    <div class="w-full">
      <TextField
        {...props}
        {title}
        {disabled}
        {label}
        {placeholder}
        bind:value={csvValue}
        type="text"
        autofocus
      />
    </div>
    <div class="w-min">
      <Icon onclick={onSaveClicked} name="save" outlined />
    </div>
  {:else}
    <div class="w-full">
      <Select
        {...props}
        {title}
        {disabled}
        {nullable}
        {label}
        {allItem}
        bind:value
      />
    </div>
    <div class="w-min">
      <Icon onclick={onEditClicked} name="edit_note" outlined />
    </div>
  {/if}
</div>
