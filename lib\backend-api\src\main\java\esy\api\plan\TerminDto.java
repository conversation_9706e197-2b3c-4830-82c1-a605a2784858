package esy.api.plan;

import com.fasterxml.jackson.annotation.*;
import esy.api.wiki.Seite;
import esy.json.JsonDocument;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

import java.time.LocalDate;
import java.time.temporal.WeekFields;
import java.util.Comparator;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Stream;

/**
 * Ein {@link TerminDto Termin} repräsentiert einen Kalendertag mit Vorgängen unterschiedlicher Art.
 */
@EqualsAndHashCode
@RequiredArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder(alphabetic = true)
@JsonDocument
public class TerminDto {

    @Getter
    @JsonProperty
    @JsonFormat(pattern = "yyyy-MM-dd")
    private final LocalDate datum;

    @JsonProperty
    private final Set<VorgangDto> allVorgang = new TreeSet<>(Comparator
            .comparing(VorgangDto::getTyp)
            .thenComparing(VorgangDto::getId));

    @JsonProperty
    public String getWochentag() {
        return switch (this.datum.getDayOfWeek()) {
            case MONDAY -> "MO";
            case TUESDAY -> "DI";
            case WEDNESDAY -> "MI";
            case THURSDAY -> "DO";
            case FRIDAY -> "FR";
            case SATURDAY -> "SA";
            case SUNDAY -> "SO";
        };
    }

    @JsonProperty
    public int getTag() {
        return this.datum.getDayOfMonth();
    }

    @JsonProperty
    public int getWoche() {
        return this.datum.get(WeekFields.ISO.weekOfYear());
    }

    @JsonProperty
    public int getMonat() {
        return this.datum.getMonthValue();
    }

    @JsonProperty
    public int getJahr() {
        return this.datum.getYear();
    }

    public TerminDto add(@NonNull final Aufgabe value) {
        allVorgang.add(new VorgangDto(value));
        return this;
    }

    public TerminDto add(@NonNull final Meldung value) {
        allVorgang.add(new VorgangDto(value));
        return this;
    }

    public TerminDto add(@NonNull final Risiko value) {
        allVorgang.add(new VorgangDto(value));
        return this;
    }

    public TerminDto add(@NonNull final Seite value) {
        allVorgang.add(new VorgangDto(value));
        return this;
    }

    @JsonIgnore
    public boolean isEmpty() {
        return allVorgang.isEmpty();
    }

    @JsonIgnore
    public int size() {
        return allVorgang.size();
    }

    @JsonIgnore
    public Stream<VorgangDto> stream() {
        return allVorgang.stream();
    }
}
