<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import { toast } from "../components/Toast";
  import { loadAllValue } from "../utils/rest.js";
  import { Circle } from "svelte-loading-spinners";

  let { gruppeId } = $props();

  let loading = $state(true);
  onMount(async () => {
    try {
      loading = true;
      await tick();
      await reloadAllNutzer();
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  let allNutzer = $state([]);
  function reloadAllNutzer() {
    return loadAllValue("/api/nutzer?allGruppe.id=" + gruppeId)
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadAllNutzer", msg]);
        allNutzer = json;
      })
      .catch((err) => {
        console.log(["reloadAllNutzer", err]);
        toast.push(err.toString());
      });
  }
</script>

<div class="flex flex-col">
  {#if loading}
    <div class="h-screen flex justify-center items-center">
      <Circle size="60" unit="px" duration="1s" />
    </div>
  {:else}
    <div class="grid grid-cols-1 gap-1">
      {#each allNutzer as nutzer}
        <div class="underline text-blue-600">
          <a href={"/nutzer/" + nutzer.id}
            >{nutzer.name} &lt;{nutzer.mail}&gt;</a
          >
        </div>
      {:else}
        <span>Keine Nutzer in dieser Gruppe</span>
      {/each}
    </div>
  {/if}
</div>
