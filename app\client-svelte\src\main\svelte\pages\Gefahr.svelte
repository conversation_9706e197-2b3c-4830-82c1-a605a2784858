<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import { toast } from "../components/Toast";
  import { loadAllValue } from "../utils/rest.js";
  import { updatePatch } from "../utils/rest.js";
  import { removeValue } from "../utils/rest.js";
  import { Circle } from "svelte-loading-spinners";
  import Icon from "../components/Icon";
  import Checkbox from "../components/Checkbox";
  import TextField from "../components/TextField";
  import GefahrEditor from "./GefahrEditor.svelte";

  let loading = true;
  onMount(async () => {
    try {
      loading = true;
      await reloadAllGefahr();
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  let gefahrId = undefined;
  async function onGefahrClicked(gefahr) {
    gefahrId = gefahr.id;
  }
  async function onGefahrRemoveClicked(gefahr) {
    gefahrId = gefahr.id;
    await removeGefahr(gefahr);
  }
  let gefahrEditorCreate = false;
  async function onGefahrEditorCreateClicked() {
    gefahrEditorCreate = true;
  }
  let gefahrEditorUpdate = false;
  async function onGefahrEditorUpdateClicked(gefahr) {
    gefahrId = gefahr.id;
    gefahrEditorUpdate = true;
  }
  $: gefahrEditorDisabled = gefahrEditorCreate || gefahrEditorUpdate;

  let gefahrFilter;
  function gefahrFilterParameter() {
    if (!gefahrFilter) return "";
    return "&titel=" + encodeURIComponent(gefahrFilter);
  }
  function gefahrSortParameter() {
    return "?sort=titel";
  }
  async function onGefahrFilterClicked() {
    try {
      loading = true;
      await tick();
      await reloadAllGefahr();
    } finally {
      loading = false;
    }
  }

  let allGefahr = [];
  function onCreateGefahr(gefahr) {
    allGefahr = allGefahr.toSpliced(0, 0, gefahr);
  }
  function onUpdateGefahr(gefahr) {
    let index = allGefahr.findIndex((e) => e.id === gefahr.id);
    if (index > -1) allGefahr = allGefahr.toSpliced(index, 1, gefahr);
  }
  function onRemoveGefahr(gefahr) {
    let index = allGefahr.findIndex((e) => e.id === gefahr.id);
    if (index > -1) allGefahr = allGefahr.toSpliced(index, 1);
  }
  function reloadAllGefahr() {
    const query = gefahrSortParameter() + gefahrFilterParameter();
    return loadAllValue("/api/gefahr" + query)
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadAllGefahr", query, msg]);
        allGefahr = json;
      })
      .catch((err) => {
        console.log(["reloadAllGefahr", query, err]);
        allGefahr = [];
        toast.push(err.toString());
      });
  }

  function updateGefahr(gefahr) {
    return updatePatch("/api/gefahr/" + gefahr.id, gefahr)
      .then((json) => {
        console.log(["updateGefahr", gefahr, json]);
        onUpdateGefahr(json);
      })
      .catch((err) => {
        console.log(["updateGefahr", gefahr, err]);
        toast.push(err.toString());
      });
  }
  function removeGefahr(gefahr) {
    const text = gefahr.titel;
    const hint = text.length > 20 ? text.substring(0, 20) + "..." : text;
    if (!confirm("Gefahr '" + hint + "' wirklich löschen?")) return;
    return removeValue("/api/gefahr/" + gefahr.id)
      .then((json) => {
        console.log(["removeGefahr", gefahr, json]);
        onRemoveGefahr(json);
      })
      .catch((err) => {
        console.log(["removeGefahr", gefahr, err]);
        toast.push(err.toString());
      });
  }
</script>

<h1 title="Liste der Gefahren, ggfs. gefiltert, jedes Element editierbar">
  Gefahr
</h1>
<div class="flex flex-col gap-1 ml-2 mr-2">
  <form on:submit|preventDefault={onGefahrFilterClicked}>
    <div class="flex flex-row gap-1 items-center pr-2">
      <div class="w-full">
        <TextField
          bind:value={gefahrFilter}
          label="Filter"
          placeholder="Bitte Filterkriterien eingeben"
        />
      </div>
      <div class="w-min">
        <Icon type="submit" name="search" outlined />
      </div>
    </div>
  </form>
  {#if loading}
    <div class="h-screen flex justify-center items-center">
      <Circle size="60" unit="px" duration="1s" />
    </div>
  {:else}
    <table class="table-fixed">
      <thead class="justify-between">
        <tr class="bg-title-100">
          <th class="px-2 py-3 table-cell">
            <span class="text-title-600 text-sm">Aktiv</span>
          </th>
          <th class="px-2 py-3 text-left w-full table-cell">
            <span class="text-title-600">Titel</span>
          </th>
          <th class="px-2 py-3 text-right w-0 table-cell">
            <Icon
              onclick={() => onGefahrEditorCreateClicked()}
              disabled={gefahrEditorDisabled}
              title="Gefahr hinzufügen"
              name="add"
              outlined
            />
          </th>
        </tr>
      </thead>
      <tbody>
        {#if gefahrEditorCreate}
          <tr>
            <td class="px-2" colspan="3">
              <GefahrEditor
                bind:visible={gefahrEditorCreate}
                on:create={(e) => onCreateGefahr(e.detail)}
              />
            </td>
          </tr>
        {/if}
        {#each allGefahr as gefahr, i}
          <tr
            on:click={(e) => onGefahrClicked(gefahr)}
            title={gefahr.id}
            class:border-l-2={gefahrId === gefahr.id}
            class:bg-gray-100={i % 2 === 1}
          >
            <td class="px-2 py-3 table-cell">
              <Checkbox
                bind:checked={gefahr.aktiv}
                on:change={() => updateGefahr(gefahr)}
              />
            </td>
            <td class="px-2 py-3 text-left table-cell">
              <div class="text-sm underline text-blue-600">
                <a href={"/gefahr/" + gefahr.id}>{gefahr.titel}</a>
              </div>
            </td>
            <td class="px-2 py-3 table-cell">
              <div
                class="grid grid-cols-1 md:grid-cols-2 items-center gap-1 w-max"
              >
                <Icon
                  onclick={() => onGefahrRemoveClicked(gefahr)}
                  disabled={gefahrEditorDisabled || gefahr.aktiv}
                  title="Gefahr löschen"
                  name="delete"
                  outlined
                />
                <Icon
                  onclick={() => onGefahrEditorUpdateClicked(gefahr)}
                  disabled={gefahrEditorDisabled}
                  title="Gefahr bearbeiten"
                  name="edit"
                  outlined
                />
              </div>
            </td>
          </tr>
          {#if gefahrEditorUpdate && gefahrId === gefahr.id}
            <tr>
              <td class="border-l-4 px-2" colspan="3">
                <GefahrEditor
                  bind:visible={gefahrEditorUpdate}
                  on:update={(e) => onUpdateGefahr(e.detail)}
                  {gefahr}
                />
              </td>
            </tr>
          {/if}
        {:else}
          <tr>
            <td class="px-2" colspan="3">Keine Gefahren</td>
          </tr>
        {/each}
      </tbody>
    </table>
  {/if}
</div>
