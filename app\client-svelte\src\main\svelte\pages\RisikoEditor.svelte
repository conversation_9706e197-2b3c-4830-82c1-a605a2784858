<script>
  import { onMount } from "svelte";
  import { createEventDispatcher } from "svelte";
  import { toast } from "../components/Toast";
  import { createValue } from "../utils/rest.js";
  import { updatePatch } from "../utils/rest.js";
  import Button from "../components/Button";
  import Select from "../components/Select";
  import TextField from "../components/TextField";
  import TextArea from "../components/TextArea";

  export let visible = false;
  export let autofocus = true;
  export let autoscroll = true;
  export let risiko = undefined;
  export let allNutzerItem;
  export let allTerminserieItem;

  let focusOn;
  let bottomDiv;
  onMount(async () => {
    console.log(["onMount", autofocus, autoscroll]);
    if (autofocus) focusOn.focus();
    if (autoscroll) bottomDiv.scrollIntoView(false);
  });

  let clicked = false;
  let showUpdate;
  let newRisiko = {
    nutzerId: undefined,
    titel: "",
    text: "",
    termin: null,
    terminserie: "X",
    status: "I",
    allStatusTransition: [],
  };

  $: if (risiko) onChange();
  async function onChange() {
    showUpdate = risiko.id;
    newRisiko = {
      id: risiko.id,
      nutzerId: risiko.nutzerId,
      version: risiko.version,
      titel: risiko.titel,
      text: risiko.text,
      termin: risiko.termin,
      terminserie: risiko.terminserie,
      status: risiko.status,
      allStatusTransition: risiko.allStatusTransition,
    };
    console.log(["onChange", newRisiko]);
  }

  async function onSubmit() {
    try {
      clicked = true;
      if (showUpdate) {
        await updateRisiko();
      } else {
        await createRisiko();
      }
    } finally {
      clicked = false;
    }
  }
  async function onCancel() {
    visible = false;
  }

  const dispatch = createEventDispatcher();
  function createRisiko() {
    return createValue("/api/risiko", newRisiko)
      .then((json) => {
        console.log(["createRisiko", newRisiko, json]);
        visible = false;
        dispatch("create", json);
      })
      .catch((err) => {
        console.log(["createRisiko", newRisiko, err]);
        toast.push(err.toString());
      });
  }
  function updateRisiko() {
    if (!newRisiko.nutzerId) {
      newRisiko.nutzerId = null;
    }
    return updatePatch("/api/risiko/" + newRisiko.id, newRisiko)
      .then((json) => {
        console.log(["updateRisiko", newRisiko, json]);
        visible = false;
        dispatch("update", json);
      })
      .catch((err) => {
        console.log(["updateRisiko", newRisiko, err]);
        toast.push(err.toString());
      });
  }
</script>

<form on:submit|preventDefault={onSubmit} on:reset|preventDefault={onCancel}>
  <div class="flex flex-col">
    <div class="flex flex-col md:flex-row gap-1">
      <div class="w-full md:w-48">
        <TextField
          bind:this={focusOn}
          bind:value={newRisiko.termin}
          required
          type="date"
          label="Termin"
          placeholder="Bitte einen Termin wählen"
        />
      </div>
      <div class="w-full md:w-48">
        <Select
          bind:value={newRisiko.status}
          allItem={newRisiko.allStatusTransition}
          label="Status"
          placeholder="Bitte einen Status wählen"
        />
      </div>
      <div class="w-full md:w-1/3">
        <Select
          bind:value={newRisiko.terminserie}
          allItem={allTerminserieItem}
          valueGetter={(v) => v?.value}
          label="Wiederholung"
          placeholder="Bitte eine Terminserie wählen"
        />
      </div>
      <div class="w-full md:w-2/3">
        <Select
          bind:value={newRisiko.nutzerId}
          allItem={allNutzerItem}
          valueGetter={(v) => v?.value}
          nullable
          label="Nutzer"
          placeholder="Bitte einen Nutzer wählen"
        />
      </div>
    </div>
    <div class="w-full">
      <TextField
        bind:value={newRisiko.titel}
        required
        label="Titel"
        placeholder="Bitte einen Titel eingeben"
      />
    </div>
    <div class="w-full">
      <TextArea
        bind:value={newRisiko.text}
        required
        label="Text"
        placeholder="Bitte einen Text eingeben"
      />
    </div>
  </div>
  <div class="py-4 flex flex-row gap-1 items-baseline">
    <div class="flex-initial">
      <Button type="submit">Ok</Button>
    </div>
    <div class="flex-initial">
      <slot name="cancel">
        <Button type="reset">Abbrechen</Button>
      </slot>
    </div>
  </div>
</form>

<div class="h-0" bind:this={bottomDiv}>&nbsp;</div>

{#if import.meta.env.DEV}
  <details tabindex="-1">
    <summary>JSON</summary>
    <pre>{JSON.stringify(newRisiko, null, 2)}</pre>
  </details>
{/if}
