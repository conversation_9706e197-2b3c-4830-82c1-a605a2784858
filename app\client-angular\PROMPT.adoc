= PROMPT

== Angular

=== Setup

Create an angular client app in directory `app/client-angular`.
Use vite as build tool and bundler.
Use tailwind as css framework.

=== Home

Add a home page with a plain welcome text.
Display the name of the current logged in person from backen GET operation `/who`.
Display the backend version from backend GET operation `/version`.
Redirect to a login page if backend responds with `401`.

The signature of the backend GET operation `/who` in the spring boot server is the following:

    @GetMapping(value="/who", produces = "application/json")
    public ResponseEntity<JwtPrincipal> who(final Authentication authentication)

The signature of the backend GET operation `/version` in the spring boot server is the following:

    @GetMapping(value = "/version", produces = "application/json")
    public ResponseEntity<Version> versionJson(@Value("${oauth2.login}") final String oauth2)

The returned Version object contains the properties major (number), minor (number) and oauth2 (login provider).

=== Login

Add a login page based on the result of the backend GET operation `/version`.
Use property `oauth2` to determine the login procedure.

If property `oauth2` is empty Add a login page with a list of email addresses in a select.
Get the list of emails from a backend GET operation `/jwt/users`.
Let the user select an email address.
Call backend POST operation `/jwt` with query parameter `email` to populate the session with a valid `JWT` token.

The signature of the backend GET operation `/jwt/user` in the spring boot server is the following:

    @GetMapping(value = "/jwt/user")
    public ResponseEntity<CollectionModel<JwtPrincipal>> user()

The signature of the backend POST operation `/jwt` in the spring boot server is the following:

    @PostMapping(value = "/jwt", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public ResponseEntity<EntityModel<JwtPrincipal>> jwt(@NonNull final WebRequest request, @NonNull final HttpServletResponse response)

If property `oauth2` is not empty use the backend authorization operation `/oauth2/authorization/{oauth2}`.
`{oauth2}` is placeholder for the value from property `oauth2`.
The operation is used to initiate login and perform a redirect to the third party authorization server.

=== Logout

Add a logout page.
Call backend POST operation `/logout` to invalidate the `JWT` token.
