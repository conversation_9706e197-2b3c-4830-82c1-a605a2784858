<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.9.xsd">
    <changeSet id="1" author="robert">
        <preConditions onFail="MARK_RAN">
            <not><tableExists tableName="seite" /></not>
        </preConditions>
        <createTable tableName="seite">
            <column name="version" type="BIGINT" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="id" type="UUID">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="nutzer_id" type="UUID">
                <constraints nullable="true"/>
            </column>
            <column name="ordner_id" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="aktiv" type="BOOLEAN" defaultValue="true">
                <constraints nullable="false"/>
            </column>
            <column name="typ" type="VARCHAR(64)" defaultValue="ADOC">
                <constraints nullable="false"/>
            </column>
            <column name="uri" type="VARCHAR(512)">
                <constraints nullable="false"/>
            </column>
            <column name="titel" type="VARCHAR(512)">
                <constraints nullable="false"/>
            </column>
            <column name="text" type="VARCHAR(8196)" defaultValue="">
                <constraints nullable="false"/>
            </column>
            <column name="termin" type="DATE" defaultValueDate="2001-01-01">
                <constraints nullable="false"/>
            </column>
            <column name="sprache" type="VARCHAR(16)" defaultValue="DE">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint
                constraintName="uk_seite"
                tableName="seite"
                columnNames="ordner_id, uri"
        />
        <addForeignKeyConstraint
                constraintName="fk_seite_nutzer_id"
                baseTableName="seite"
                baseColumnNames="nutzer_id"
                referencedTableName="nutzer"
                referencedColumnNames="id"
        />
        <addForeignKeyConstraint
                constraintName="fk_seite_ordner_id"
                baseTableName="seite"
                baseColumnNames="ordner_id"
                referencedTableName="ordner"
                referencedColumnNames="id"
        />
    </changeSet>
    <changeSet id="2" author="robert">
        <addColumn tableName="seite">
            <column name="terminserie" type="VARCHAR(16)" defaultValue="X">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
</databaseChangeLog>
