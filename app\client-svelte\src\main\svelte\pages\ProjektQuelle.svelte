<script>
  import { onMount } from "svelte";
  import { createEventDispatcher } from "svelte";
  import { toast } from "../components/Toast";
  import { updatePatch } from "../utils/rest.js";
  import Button from "../components/Button";
  import Fieldset from "../components/Fieldset";
  import Icon from "../components/Icon";
  import Select from "../components/Select";
  import TextField from "../components/TextField";

  export let visible = false;
  export let autofocus = true;
  export let autoscroll = true;
  export let projekt;
  export let allQuelleItem;

  let clicked = false;
  let focusOn;
  let bottomDiv;
  onMount(async () => {
    console.log(["onMount", autofocus, autoscroll]);
    if (autofocus) focusOn.focus();
    if (autoscroll) bottomDiv.scrollIntoView(false);
  });

  let newProjekt = {
    name: "",
    allQuelle: [],
  };

  $: if (projekt && projekt.id) onChange();
  async function onChange() {
    newProjekt = { ...newProjekt, ...projekt };
    console.log(["onChange", newProjekt]);
  }

  async function onSubmit() {
    try {
      clicked = true;
      await updateProjekt();
    } finally {
      clicked = false;
    }
  }
  async function onCancel() {
    visible = false;
  }

  function onInsertQuelle() {
    newProjekt.allQuelle = [
      ...newProjekt.allQuelle,
      {
        typ: undefined,
        uri: null,
        text: "",
      },
    ];
    console.log(["onInsertQuelle", newProjekt]);
  }
  function onRemoveQuelle(index) {
    newProjekt.allQuelle = [
      ...newProjekt.allQuelle.slice(0, index),
      ...newProjekt.allQuelle.slice(index + 1),
    ];
    console.log(["onRemoveQuelle", newProjekt]);
  }

  const dispatch = createEventDispatcher();
  function updateProjekt() {
    return updatePatch("/api/projekt" + "/" + newProjekt.id, newProjekt)
      .then((json) => {
        console.log(["updateProjekt", newProjekt, json]);
        visible = false;
        dispatch("update", json);
      })
      .catch((err) => {
        console.log(["updateProjekt", newProjekt, err]);
        toast.push(err.toString());
      });
  }
</script>

<form on:submit|preventDefault={onSubmit} on:reset|preventDefault={onCancel}>
  <div class="flex flex-col gap-1">
    {#each newProjekt.allQuelle as quelle, i}
      <Fieldset>
        <a slot="title" href={quelle.uri} target="_blank" rel="noreferrer">
          {i + 1 + ". Quelle"}
        </a>
        <div class="flex flex-row gap-1 items-baseline">
          <div class="w-1/5">
            <Select
              bind:value={quelle.typ}
              allItem={allQuelleItem.map((e) => e.name)}
              required
              label="Typ"
            />
          </div>
          <div class="w-2/5">
            <TextField
              bind:value={quelle.uri}
              required
              type="url"
              label="Link"
            />
          </div>
          <div class="w-2/5">
            <TextField
              bind:value={quelle.text}
              required
              type="text"
              label="Text"
            />
          </div>
          <div class="place-self-center">
            <Icon onclick={() => onRemoveQuelle(i)} name="delete" outlined />
          </div>
        </div>
      </Fieldset>
    {/each}
    <div class="place-self-end">
      <Icon
        bind:this={focusOn}
        onclick={() => onInsertQuelle()}
        name="add"
        outlined
      />
    </div>
  </div>
  <div class="py-4 flex flex-row gap-1 items-baseline">
    <div class="flex-initial">
      <Button type="submit">Ok</Button>
    </div>
    <div class="flex-initial">
      <slot name="cancel">
        <Button type="reset">Abbrechen</Button>
      </slot>
    </div>
  </div>
</form>

<div class="h-0" bind:this={bottomDiv}>&nbsp;</div>

{#if import.meta.env.DEV}
  <details tabindex="-1">
    <summary>JSON</summary>
    <pre>{JSON.stringify(newProjekt, null, 2)}</pre>
  </details>
{/if}
