<script>
  import { onMount } from "svelte";
  import { createEventDispatcher } from "svelte";
  import { toast } from "../components/Toast";
  import { createValue } from "../utils/rest.js";
  import { updatePatch } from "../utils/rest.js";
  import Button from "../components/Button";
  import Select from "../components/Select";
  import TextField from "../components/TextField";
  import TextArea from "../components/TextArea";

  export let visible = false;
  export let autofocus = true;
  export let autoscroll = true;
  export let meldung = undefined;
  export let allNutzerItem;

  let focusOn;
  let bottomDiv;
  onMount(async () => {
    console.log(["onMount", autofocus, autoscroll]);
    if (autofocus) focusOn.focus();
    if (autoscroll) bottomDiv.scrollIntoView(false);
  });

  let clicked = false;
  let showUpdate;
  let newMeldung = {
    nutzerId: undefined,
    aktiv: true,
    titel: "",
    text: "",
    termin: null,
  };

  $: if (meldung) onChange();
  async function onChange() {
    showUpdate = meldung.id;
    newMeldung = {
      id: meldung.id,
      nutzerId: meldung.nutzerId,
      aktiv: meldung.aktiv,
      version: meldung.version,
      titel: meldung.titel,
      text: meldung.text,
      termin: meldung.termin,
    };
    console.log(["onChange", newMeldung]);
  }

  async function onSubmit() {
    try {
      clicked = true;
      if (showUpdate) {
        await updateMeldung();
      } else {
        await createMeldung();
      }
    } finally {
      clicked = false;
    }
  }
  async function onCancel() {
    visible = false;
  }

  const dispatch = createEventDispatcher();
  function createMeldung() {
    return createValue("/api/meldung", newMeldung)
      .then((json) => {
        console.log(["createMeldung", newMeldung, json]);
        visible = false;
        dispatch("create", json);
      })
      .catch((err) => {
        console.log(["createMeldung", newMeldung, err]);
        toast.push(err.toString());
      });
  }
  function updateMeldung() {
    if (!newMeldung.nutzerId) {
      newMeldung.nutzerId = null;
    }
    return updatePatch("/api/meldung/" + newMeldung.id, newMeldung)
      .then((json) => {
        console.log(["updateMeldung", newMeldung, json]);
        visible = false;
        dispatch("update", json);
      })
      .catch((err) => {
        console.log(["updateMeldung", newMeldung, err]);
        toast.push(err.toString());
      });
  }
</script>

<form on:submit|preventDefault={onSubmit} on:reset|preventDefault={onCancel}>
  <div class="flex flex-col gap-1">
    <div class="flex flex-col sm:flex-row gap-1">
      <TextField
        bind:this={focusOn}
        bind:value={newMeldung.titel}
        required
        label="Titel"
        placeholder="Bitte einen Titel eingeben"
      />
      <div class="w-full sm:w-2/3">
        <Select
          bind:value={newMeldung.nutzerId}
          allItem={allNutzerItem}
          valueGetter={(v) => v?.value}
          nullable
          label="Nutzer"
          placeholder="Bitte einen Nutzer wählen"
        />
      </div>
      <div class="w-full sm:w-1/3">
        <TextField
          bind:this={focusOn}
          bind:value={newMeldung.termin}
          type="date"
          required
          label="Termin"
          placeholder="Bitte ein Datum eingeben"
        />
      </div>
    </div>
    <TextArea
      bind:value={newMeldung.text}
      required
      label="Text"
      placeholder="Bitte einen Text eingeben"
    />
  </div>
  <div class="py-4 flex flex-row gap-1 items-baseline">
    <div class="flex-initial">
      <Button type="submit">Ok</Button>
    </div>
    <div class="flex-initial">
      <slot name="cancel">
        <Button type="reset">Abbrechen</Button>
      </slot>
    </div>
  </div>
</form>

<div class="h-0" bind:this={bottomDiv}>&nbsp;</div>

{#if import.meta.env.DEV}
  <details tabindex="-1">
    <summary>JSON</summary>
    <pre>{JSON.stringify(newMeldung, null, 2)}</pre>
  </details>
{/if}
