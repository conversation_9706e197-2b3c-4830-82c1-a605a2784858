package esy.api.info;

import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@Tag("fast")
@SuppressWarnings("java:S5961") // allow more than 25 assertions
class TextItemTest {

    @Test
    void equalsHashcodeToString() {
        final var titel = "Alpha";
        final var text = "Lorem ipsum.";
        final var value = new TextItem(titel, text);
        // Identisches Objekt
        assertEquals(value, value);
        assertEquals(value.hashCode(), value.hashCode());
        assertEquals(value.toString(), value.toString());
        // Gleicher Wert
        final var equal = new TextItem(titel, text);
        assertEquals(equal, value);
        assertEquals(equal.hashCode(), value.hashCode());
        assertEquals(equal.toString(), value.toString());
        // Anderer Wert
        final var other = new TextItem("Beta", text);
        assertNotEquals(other, value);
        assertNotEquals(other.hashCode(), value.hashCode());
        assertNotEquals(other.toString(), value.toString());
        // Kein Objekt
        assertNotEquals(null, value);
        // Falsches Objekt
        assertNotEquals(this, value);
    }

    void json() {
        final var titel = "Alpha";
        final var text = "Lorem ipsum.";
        final var value = new TextItem(titel, text);
        assertEquals(value, TextItem.parseJson(value.writeJson()));
    }

    @Test
    void ofNull() {
        final var value = new TextItem();
        assertNull(value.getValue());
        assertNull(value.getTitel());
        assertEquals("", value.getText());
        assertFalse(value.isCreate());
        assertFalse(value.isUpdate());
        assertTrue(value.isDelete());
    }

    @Test
    void ofValue() {
        final var titel = "Gamma";
        final var text = "Lorem ipsum.";
        final var value = new TextItem(titel, text);
        assertEquals(titel, value.getValue());
        assertEquals(titel, value.getTitel());
        assertNotEquals("", value.getText());
        assertFalse(value.isCreate());
        assertTrue(value.isUpdate());
        assertFalse(value.isDelete());
    }
}
