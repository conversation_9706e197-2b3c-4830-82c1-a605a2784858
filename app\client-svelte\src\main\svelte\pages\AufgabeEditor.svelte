<script>
  import { onMount } from "svelte";
  import { createEventDispatcher } from "svelte";
  import { toast } from "../components/Toast";
  import { createValue } from "../utils/rest.js";
  import { updatePatch } from "../utils/rest.js";
  import { textToStichwort } from "../utils/text.js";
  import Button from "../components/Button";
  import Select from "../components/Select";
  import TextArea from "../components/TextArea";
  import TextField from "../components/TextField";
  import Toggle from "../components/Toggle";

  export let visible = false;
  export let autofocus = true;
  export let autoscroll = true;
  export let aufgabe = undefined;
  export let projektId;
  export let allNutzerItem;
  export let allTerminserieItem;

  let clicked = false;
  let focusOn;
  let bottomDiv;
  onMount(async () => {
    console.log(["onMount", autofocus, autoscroll]);
    if (autofocus) focusOn.focus();
    if (autoscroll) bottomDiv.scrollIntoView(false);
  });

  let showUpdate;
  let newAufgabe = {
    nutzerId: undefined,
    projektId: projektId,
    termin: null,
    status: "I",
    allStatusTransition: [],
    titel: "",
    text: "",
    allStichwort: [],
  };

  let allStichwortFromText = [];

  $: if (aufgabe) onChange();
  function onChange() {
    showUpdate = aufgabe.id;
    newAufgabe = {
      id: aufgabe.id,
      nutzerId: aufgabe.nutzerId,
      projektId: aufgabe.projektId,
      termin: aufgabe.termin,
      terminserie: aufgabe.terminserie,
      status: aufgabe.status,
      allStatusTransition: aufgabe.allStatusTransition,
      titel: aufgabe.titel,
      text: aufgabe.text,
      quelle: { ...aufgabe.quelle },
      allStichwort: [...aufgabe.allStichwort],
      version: aufgabe.version,
    };
    allStichwortFromText = textToStichwort(
      newAufgabe.allStichwort,
      newAufgabe.text
    );
  }

  async function onSubmit() {
    try {
      clicked = true;
      if (!aufgabe.nutzerId) {
        aufgabe.nutzerId = null;
      }
      if (showUpdate) {
        await updateAufgabe();
      } else {
        await createAufgabe();
      }
    } finally {
      clicked = false;
    }
  }
  async function onCancel() {
    visible = false;
  }

  const dispatch = createEventDispatcher();
  function createAufgabe() {
    return createValue("/api/aufgabe", newAufgabe)
      .then((json) => {
        console.log(["createAufgabe", newAufgabe, json]);
        visible = false;
        dispatch("create", json);
      })
      .catch((err) => {
        console.log(["createAufgabe", newAufgabe, err]);
        toast.push(err.toString());
      });
  }
  function updateAufgabe() {
    return updatePatch("/api/aufgabe" + "/" + newAufgabe.id, newAufgabe)
      .then((json) => {
        console.log(["updateAufgabe", newAufgabe, json]);
        visible = false;
        dispatch("update", json);
      })
      .catch((err) => {
        console.log(["updateAufgabe", newAufgabe, err]);
        toast.push(err.toString());
      });
  }
</script>

<form on:submit|preventDefault={onSubmit} on:reset|preventDefault={onCancel}>
  <div class="flex flex-col">
    <div class="flex flex-col md:flex-row gap-1">
      <div class="w-full md:w-48">
        <TextField
          bind:this={focusOn}
          bind:value={newAufgabe.termin}
          required
          type="date"
          label="Termin"
          placeholder="Bitte einen Termin wählen"
        />
      </div>
      <div class="w-full md:w-48">
        <Select
          bind:value={newAufgabe.status}
          allItem={newAufgabe.allStatusTransition}
          label="Status"
          placeholder="Bitte einen Status wählen"
        />
      </div>
      <div class="w-full md:w-1/3">
        <Select
          bind:value={newAufgabe.terminserie}
          allItem={allTerminserieItem}
          valueGetter={(v) => v?.value}
          label="Wiederholung"
          placeholder="Bitte eine Terminserie wählen"
        />
      </div>
      <div class="w-full md:w-2/3">
        <Select
          bind:value={newAufgabe.nutzerId}
          allItem={allNutzerItem}
          valueGetter={(v) => v?.value}
          nullable
          label="Nutzer"
          placeholder="Bitte einen Nutzer wählen"
        />
      </div>
    </div>
    <div class="w-full">
      <TextField
        bind:value={newAufgabe.titel}
        required
        label="Titel"
        placeholder="Bitte einen Titel eingeben"
      />
    </div>
    <div class="w-full">
      <TextArea
        bind:value={newAufgabe.text}
        required
        label="Text"
        placeholder="Bitte einen Text eingeben"
      />
    </div>
    <div class="w-full">
      <Toggle
        bind:allValue={newAufgabe.allStichwort}
        allItem={allStichwortFromText}
        label="Stichworte"
        placeholder="Bitte Stichworte wählen"
      />
    </div>
  </div>
  <div class="py-4 flex flex-row gap-1 items-baseline">
    <div class="flex-initial">
      <Button type="submit">Ok</Button>
    </div>
    <div class="flex-initial">
      <slot name="cancel">
        <Button type="reset">Abbrechen</Button>
      </slot>
    </div>
  </div>
</form>

<div class="h-0" bind:this={bottomDiv}>&nbsp;</div>

{#if import.meta.env.DEV}
  <details tabindex="-1">
    <summary>JSON</summary>
    <pre>{JSON.stringify(newAufgabe, null, 2)}</pre>
  </details>
{/if}
