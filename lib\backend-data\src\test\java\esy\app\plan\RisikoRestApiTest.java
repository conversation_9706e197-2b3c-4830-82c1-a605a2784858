package esy.app.plan;

import esy.api.plan.QRisiko;
import esy.api.plan.Risiko;
import esy.api.plan.Terminserie;
import esy.api.plan.VorgangStatus;
import esy.app.EsyBackendConfiguration;
import esy.app.team.NutzerRepository;
import esy.auth.JwtRole;
import esy.auth.WithMockJwt;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.restdocs.RestDocumentationContextProvider;
import org.springframework.restdocs.RestDocumentationExtension;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.UUID;

import static esy.app.RestApiAssertions.assertRestApiSpecExists;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.restdocs.mockmvc.MockMvcRestDocumentation.document;
import static org.springframework.restdocs.mockmvc.MockMvcRestDocumentation.documentationConfiguration;
import static org.springframework.restdocs.operation.preprocess.Preprocessors.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@Tag("slow")
@SpringBootTest
@AutoConfigureMockMvc(addFilters = false)
@ContextConfiguration(classes = {EsyBackendConfiguration.class})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith({MockitoExtension.class, RestDocumentationExtension.class})
class RisikoRestApiTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private GefahrRepository gefahrRepository;

    @Autowired
    private NutzerRepository nutzerRepository;

    @Autowired
    private ProzessRepository prozessRepository;

    @Autowired
    private RisikoRepository risikoRepository;

    @BeforeEach
    void setUp(final WebApplicationContext webApplicationContext,
               final RestDocumentationContextProvider restDocumentation) {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext)
                .apply(documentationConfiguration(restDocumentation))
                .alwaysDo(document("{method-name}",
                        preprocessRequest(prettyPrint()),
                        preprocessResponse(prettyPrint())))
                .build();
    }

    @Test
    @Order(1)
    void asciidoc() {
        assertRestApiSpecExists(Risiko.class);
    }

    @ParameterizedTest
    @ValueSource(strings = {"GET", "POST", "PUT", "PATCH", "DELETE"})
    @Order(1)
    void preflight(final String method) throws Exception {
        mockMvc.perform(options("/api/risiko")
                        .header("Access-Control-Request-Method", method)
                        .header("Access-Control-Request-Headers", "Content-Type")
                        .header("Origin", "http://localhost:5000")) // UI
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(header()
                        .exists("Access-Control-Allow-Origin"))
                .andExpect(header()
                        .exists("Access-Control-Allow-Methods"))
                .andExpect(header()
                        .exists("Access-Control-Allow-Headers"));
    }

    @Sql("/sql/nutzer.sql")
    @Sql("/sql/gefahr.sql")
    @Sql("/sql/prozess.sql")
    @Test
    @Order(10)
    @WithMockJwt
    void getApiRisikoNoElement() throws Exception {
        mockMvc.perform(get("/api/risiko")
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(jsonPath("$.content")
                        .isArray())
                .andExpect(jsonPath("$.content[0]")
                        .doesNotExist());
    }

    @Test
    @Order(20)
    @WithMockJwt
    void postApiRisiko() throws Exception {
        final var titel = "Ausfall der Stromversorgung";
        assertFalse(risikoRepository.findOne(QRisiko.risiko.titel.eq(titel)).isPresent());
        mockMvc.perform(post("/api/risiko")
                        .content("""
                                {
                                    "nutzerId":"a2222222-6ee8-4335-b12a-ef84794bd27a",
                                    "titel":"%s",
                                    "text":"Lorem ipsum dolor sit amet.",
                                    "termin":"2019-04-22"
                                }
                                """.formatted(titel))
                        .contentType(MediaType.APPLICATION_JSON)
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isCreated())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .string("ETag", "\"0\""))
                .andExpect(jsonPath("$.id")
                        .isNotEmpty())
                .andExpect(jsonPath("$.nutzerId")
                        .exists())
                .andExpect(jsonPath("$.titel")
                        .value(titel))
                .andExpect(jsonPath("$.text")
                        .isNotEmpty())
                .andExpect(jsonPath("$.termin")
                        .isNotEmpty())
                .andExpect(jsonPath("$.status")
                        .value(VorgangStatus.I.name()));
    }

    @Test
    @Order(21)
    @WithMockJwt
    void postApiRisikoConflict() throws Exception {
        final var titel = "Ausfall der Stromversorgung";
        assertTrue(risikoRepository.findOne(QRisiko.risiko.titel.eq(titel)).isPresent());
        mockMvc.perform(post("/api/risiko")
                        .content("""
                                {
                                    "nutzerId":"a2222222-6ee8-4335-b12a-ef84794bd27a",
                                    "titel":"%s",
                                    "text":"Lorem ipsum dolor sit amet.",
                                    "termin":"2019-04-23"
                                }
                                """.formatted(titel))
                        .contentType(MediaType.APPLICATION_JSON)
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isConflict());
    }

    @Test
    @Order(30)
    @WithMockJwt
    void putApiRisikoCreate() throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var titel = "Infrastruktur nicht sicher";
        assertFalse(risikoRepository.findById(UUID.fromString(uuid)).isPresent());
        mockMvc.perform(put("/api/risiko/" + uuid)
                        .content("""
                                {
                                    "titel":"%s",
                                    "text":"At vero eos et accusam.",
                                    "termin":"2019-04-22",
                                    "status":"%s"
                                }
                                """.formatted(titel, VorgangStatus.I))
                        .contentType(MediaType.APPLICATION_JSON)
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isCreated())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .string("ETag", "\"0\""))
                .andExpect(jsonPath("$.id")
                        .value(uuid))
                .andExpect(jsonPath("$.nutzerId")
                        .doesNotExist())
                .andExpect(jsonPath("$.titel")
                        .value(titel))
                .andExpect(jsonPath("$.text")
                        .isNotEmpty())
                .andExpect(jsonPath("$.termin")
                        .isNotEmpty())
                .andExpect(jsonPath("$.status")
                        .value(VorgangStatus.I.name()));
    }

    @Test
    @Order(31)
    @WithMockJwt
    void putApiRisikoUpdate() throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var risiko = risikoRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(put("/api/risiko/" + risiko.getId())
                        .content("""
                                {
                                    "titel":"%s",
                                    "text":"%s",
                                    "termin":"2019-04-22",
                                    "status":"%s"
                                }
                                """.formatted(risiko.getTitel(), risiko.getText(), VorgangStatus.B))
                        .contentType(MediaType.APPLICATION_JSON)
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + risiko.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .string("ETag", "\"1\""))
                .andExpect(jsonPath("$.id")
                        .value(risiko.getId().toString()))
                .andExpect(jsonPath("$.nutzerId")
                        .doesNotExist())
                .andExpect(jsonPath("$.titel")
                        .value(risiko.getTitel()))
                .andExpect(jsonPath("$.text")
                        .value(risiko.getText()))
                .andExpect(jsonPath("$.termin")
                        .value(risiko.getTermin().format(Risiko.DATE_FORMATTER)))
                .andExpect(jsonPath("$.status")
                        .value(VorgangStatus.B.name()));
    }

    @Test
    @Order(32)
    @WithMockJwt
    void patchApiRisikoNutzer() throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var risiko = risikoRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(patch("/api/risiko/" + risiko.getId())
                        .content("{\"nutzerId\":\"a2222222-6ee8-4335-b12a-ef84794bd27a\"}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + risiko.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(risiko.getId().toString()))
                .andExpect(jsonPath("$.nutzerId")
                        .exists());
    }

    @Test
    @Order(33)
    @WithMockJwt
    void patchApiRisikoNutzerNull() throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var risiko = risikoRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(patch("/api/risiko/" + risiko.getId())
                        .content("{\"nutzerId\":null}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + risiko.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(risiko.getId().toString()))
                .andExpect(jsonPath("$.nutzerId")
                        .doesNotExist());
    }

    @ParameterizedTest
    @ValueSource(strings = {"X", "I", "A", "B"})
    @Order(40)
    @WithMockJwt
    void patchApiRisikoStatus(final String status) throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var risiko = risikoRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        assertNotEquals(VorgangStatus.valueOf(status), risiko.getStatus());
        mockMvc.perform(patch("/api/risiko/" + risiko.getId())
                        .content(("{\"status\":\"%s\"}").formatted(status))
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + risiko.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(risiko.getId().toString()))
                .andExpect(jsonPath("$.status")
                        .value(status));
    }

    @Test
    @Order(40)
    @WithMockJwt
    void patchApiRisikoStatusConflict() throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var risiko = risikoRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(patch("/api/risiko/" + risiko.getId())
                        .content("{\"status\":\"I\"}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + risiko.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isConflict());
    }

    @ParameterizedTest
    @ValueSource(strings = {"", "?"})
    @Order(40)
    @WithMockJwt
    void patchApiRisikoStatusBadRequest(final String status) throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var risiko = risikoRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(patch("/api/risiko/" + risiko.getId())
                        .content("{\"status\":\"%s\"}".formatted(status))
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + risiko.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isBadRequest());
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "Infrastruktur unsicher",
            "Infrastruktur nicht sicher"
    })
    @Order(41)
    @WithMockJwt
    void patchApiRisikoTitel(final String titel) throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var risiko = risikoRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        assertNotEquals(titel, risiko.getTitel());
        mockMvc.perform(patch("/api/risiko/" + risiko.getId())
                        .content("{\"titel\":\"%s\"}".formatted(titel))
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + risiko.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(risiko.getId().toString()))
                .andExpect(jsonPath("$.titel")
                        .value(titel));
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "Lorem ipsum.\nAt vero eos et accusam.",
            "At vero eos et accusam."
    })
    @Order(41)
    @WithMockJwt
    void patchApiRisikoText(final String text) throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var risiko = risikoRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        assertNotEquals(text, risiko.getText());
        mockMvc.perform(patch("/api/risiko/" + risiko.getId())
                        .content("{\"text\":\"%s\"}".formatted(text.replace("\n", "\\n")))
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + risiko.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(risiko.getId().toString()))
                .andExpect(jsonPath("$.text")
                        .value(text));
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "2000-01-01",
            "2031-04-22",
            "2019-04-22"
    })
    @Order(43)
    @WithMockJwt
    void patchApiRisikoTermin(final String datum) throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var risiko = risikoRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        assertNotEquals(LocalDate.parse(datum), risiko.getTermin());
        mockMvc.perform(patch("/api/risiko/" + risiko.getId())
                        .content("{\"termin\":\"%s\"}".formatted(datum))
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + risiko.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(risiko.getId().toString()))
                .andExpect(jsonPath("$.termin")
                        .value(datum));
    }

    @ParameterizedTest
    @ValueSource(strings = {"J", "M", "W", "T"})
    @Order(44)
    @WithMockJwt
    void patchApiAufgabeTerminserie(final String serie) throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var risiko = risikoRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(patch("/api/risiko/" + risiko.getId())
                        .content("{\"terminserie\":\"%s\"}".formatted(Terminserie.valueOf(serie)))
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + risiko.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(risiko.getId().toString()))
                .andExpect(jsonPath("$.terminserie")
                        .value(serie));
    }

    @Test
    @Order(45)
    @WithMockJwt(roles = {JwtRole.VERWALTUNG})
    void patchApiRisikoGefahr() throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var risiko = risikoRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        assertTrue(risiko.getAllGefahr().isEmpty());
        mockMvc.perform(patch("/api/risiko/" + risiko.getId())
                        .content("{\"allGefahr\":[\"/api/gefahr/a1111111-11b4-a113-f330-cd42452ab140\"]}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + risiko.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(risiko.getId().toString()))
                .andExpect(jsonPath("$.allGefahrItem")
                        .isArray())
                .andExpect(jsonPath("$.allGefahrItem[0].value")
                        .exists())
                .andExpect(jsonPath("$.allGefahrItem[0].text")
                        .isNotEmpty())
                .andExpect(jsonPath("$.allGefahrItem[1]")
                        .doesNotExist());
    }

    @Test
    @Order(46)
    @WithMockJwt(roles = {JwtRole.VERWALTUNG})
    void patchApiRisikoGefahrEmpty() throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var risiko = risikoRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        assertFalse(risiko.getAllGefahr().isEmpty());
        mockMvc.perform(patch("/api/risiko/" + risiko.getId())
                        .content("{\"allGefahr\":[]}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + risiko.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(risiko.getId().toString()))
                .andExpect(jsonPath("$.allGefahrItem")
                        .isArray())
                .andExpect(jsonPath("$.allGefahrItem[0]")
                        .doesNotExist());
    }

    @Test
    @Order(47)
    @WithMockJwt(roles = {JwtRole.VERWALTUNG})
    void patchApiRisikoProzess() throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var risiko = risikoRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        assertTrue(risiko.getAllProzess().isEmpty());
        mockMvc.perform(patch("/api/risiko/" + risiko.getId())
                        .content("{\"allProzess\":[\"/api/prozess/a1111111-2fb4-a2d3-1b0e-cd42452ab140\"]}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + risiko.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(risiko.getId().toString()))
                .andExpect(jsonPath("$.allProzessItem")
                        .isArray())
                .andExpect(jsonPath("$.allProzessItem[0].value")
                        .exists())
                .andExpect(jsonPath("$.allProzessItem[0].text")
                        .isNotEmpty())
                .andExpect(jsonPath("$.allProzessItem[1]")
                        .doesNotExist());
    }

    @Test
    @Order(48)
    @WithMockJwt(roles = {JwtRole.VERWALTUNG})
    void patchApiRisikoProzessEmpty() throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var risiko = risikoRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        assertFalse(risiko.getAllProzess().isEmpty());
        mockMvc.perform(patch("/api/risiko/" + risiko.getId())
                        .content("{\"allProzess\":[]}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + risiko.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(risiko.getId().toString()))
                .andExpect(jsonPath("$.allProzessItem")
                        .isArray())
                .andExpect(jsonPath("$.allProzessItem[0]")
                        .doesNotExist());
    }

    @Test
    @Order(49)
    @WithMockJwt
    void patchApiRisikoStatusTransition() throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var risiko = risikoRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(patch("/api/risiko/" + risiko.getId() + "/" + VorgangStatus.A)
                        .contentType(MediaType.parseMediaType("plain/text"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + risiko.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(risiko.getId().toString()))
                .andExpect(jsonPath("$.status")
                        .value(VorgangStatus.A.name()))
                .andExpect(jsonPath("$.terminserie")
                        .value(Terminserie.T.name()))
                .andExpect(jsonPath("$.termin")
                        .value(risiko.getTermin().plusDays(1L).toString()));
    }

    @Test
    @Order(50)
    @WithMockJwt
    void getApiRisikoById() throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var risiko = risikoRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(get("/api/risiko/" + risiko.getId())
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .string("ETag", "\"23\""))
                .andExpect(jsonPath("$.id")
                        .value(risiko.getId().toString()))
                .andExpect(jsonPath("$.nutzerId")
                        .doesNotExist())
                .andExpect(jsonPath("$.titel")
                        .value(risiko.getTitel()))
                .andExpect(jsonPath("$.text")
                        .value(risiko.getText()))
                .andExpect(jsonPath("$.aktiv")
                        .value(risiko.isAktiv()));
    }

    @Test
    @Order(51)
    @WithMockJwt
    void getApiRisikoByIdNotFound() throws Exception {
        final var uuid = "deadbeef-dead-beef-dead-beefdeadbeef";
        assertFalse(risikoRepository.findById(UUID.fromString(uuid)).isPresent());
        mockMvc.perform(get("/api/risiko/" + uuid)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isNotFound());
    }

    @Test
    @Order(60)
    @WithMockJwt
    void deleteApiRisikoForbidden() throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        assertTrue(risikoRepository.findById(UUID.fromString(uuid)).isPresent());
        mockMvc.perform(delete("/api/risiko/" + uuid)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isForbidden());
    }

    @Test
    @Order(61)
    @WithMockJwt(roles = {JwtRole.VERWALTUNG})
    void deleteApiRisiko() throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        assertTrue(risikoRepository.findById(UUID.fromString(uuid)).isPresent());
        mockMvc.perform(delete("/api/risiko/" + uuid)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .doesNotExist("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(uuid));
    }

    @Test
    @Order(62)
    @WithMockJwt(roles = {JwtRole.VERWALTUNG})
    void deleteApiRisikoNotFound() throws Exception {
        final var uuid = "deadbeef-dead-beef-dead-beefdeadbeef";
        assertFalse(risikoRepository.findById(UUID.fromString(uuid)).isPresent());
        mockMvc.perform(delete("/api/risiko/" + uuid)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isNotFound());
    }

    @Test
    @Order(90)
    @WithMockJwt
    void getApiRisiko() throws Exception {
        mockMvc.perform(get("/api/risiko")
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(jsonPath("$.content")
                        .isArray())
                .andExpect(jsonPath("$.content[0]")
                        .exists())
                .andExpect(jsonPath("$.content[1]")
                        .doesNotExist());
    }

    @Test
    @Order(91)
    void getApiRisikoHistory() throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        mockMvc.perform(get(String.format("/api/risiko/%s/history", uuid))
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(jsonPath("$.content")
                        .isArray())
                .andExpect(jsonPath("$.content[0].id")
                        .exists())
                .andExpect(jsonPath("$.content[0].status")
                        .value(VorgangStatus.I.name())) // CREATE
                .andExpect(jsonPath("$.content[0].termin")
                        .value("2019-04-22"))
                .andExpect(jsonPath("$.content[1].status")
                        .value(VorgangStatus.B.name()))
                .andExpect(jsonPath("$.content[2].status")
                        .value(VorgangStatus.B.name()))
                .andExpect(jsonPath("$.content[2].nutzerId")
                        .exists())
                .andExpect(jsonPath("$.content[3].status")
                        .value(VorgangStatus.B.name()))
                .andExpect(jsonPath("$.content[3].nutzerId")
                        .doesNotExist())
                .andExpect(jsonPath("$.content[4].status")
                        .value(VorgangStatus.X.name()))
                .andExpect(jsonPath("$.content[5].status")
                        .value(VorgangStatus.I.name()))
                .andExpect(jsonPath("$.content[6].status")
                        .value(VorgangStatus.A.name()))
                .andExpect(jsonPath("$.content[7].status")
                        .value(VorgangStatus.B.name()))
                .andExpect(jsonPath("$.content[8].status")
                        .value(VorgangStatus.B.name()))
                .andExpect(jsonPath("$.content[9].status")
                        .value(VorgangStatus.B.name()))
                .andExpect(jsonPath("$.content[10].status")
                        .value(VorgangStatus.B.name()))
                .andExpect(jsonPath("$.content[11].status")
                        .value(VorgangStatus.B.name()))
                .andExpect(jsonPath("$.content[12].status")
                        .value(VorgangStatus.B.name()))
                .andExpect(jsonPath("$.content[12].termin")
                        .value("2000-01-01"))
                .andExpect(jsonPath("$.content[12].terminserie")
                        .value(Terminserie.X.name()))
                .andExpect(jsonPath("$.content[13].status")
                        .value(VorgangStatus.B.name()))
                .andExpect(jsonPath("$.content[13].termin")
                        .value("2031-04-22"))
                .andExpect(jsonPath("$.content[13].terminserie")
                        .value(Terminserie.X.name()))
                .andExpect(jsonPath("$.content[14].status")
                        .value(VorgangStatus.B.name()))
                .andExpect(jsonPath("$.content[14].termin")
                        .value("2019-04-22"))
                .andExpect(jsonPath("$.content[14].terminserie")
                        .value(Terminserie.X.name()))
                .andExpect(jsonPath("$.content[15].status")
                        .value(VorgangStatus.A.name()))
                .andExpect(jsonPath("$.content[16].status")
                        .value(VorgangStatus.A.name())) // DELETE
                .andExpect(jsonPath("$.content[17]")
                        .doesNotExist());
    }

    @Test
    @Order(99)
    @Transactional
    @Rollback(false)
    void cleanup() {
        risikoRepository.findAll().forEach(risikoRepository::delete);
        gefahrRepository.findAll().forEach(gefahrRepository::delete);
        prozessRepository.findAll().forEach(prozessRepository::delete);
        nutzerRepository.findAll().forEach(nutzerRepository::delete);
        assertEquals(0, risikoRepository.count());
        assertEquals(0, gefahrRepository.count());
        assertEquals(0, prozessRepository.count());
        assertEquals(0, nutzerRepository.count());
    }
}
