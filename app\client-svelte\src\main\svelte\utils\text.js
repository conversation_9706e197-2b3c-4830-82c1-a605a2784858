export function isEmpty(text) {
  return !text || text.length === 0;
}

export function isBlank(text) {
  return !text || /^\s*$/.test(text);
}

export function splitByWord(text, textLengthMin = 4) {
  if (isBlank(text)) return [];
  return text
    .replaceAll("\n", " ")
    .replaceAll("\t", " ")
    .replaceAll(".", "")
    .replaceAll(",", "")
    .replaceAll(":", "")
    .replaceAll("?", "")
    .replaceAll("!", "")
    .toLowerCase()
    .split(" ")
    .filter((e) => e.length > textLengthMin);
}

export function textToStichwort(allStichwort, text) {
  return (
    [...allStichwort, ...splitByWord(text)]
      // no empty strings
      .filter((e) => e.length)
      // no duplicate strings
      .filter((e, i, self) => self.indexOf(e) === i)
  );
}
