<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import Button from "../components/Button";
  import { toast } from "../components/Toast";
  import { loadAllValue } from "../utils/rest.js";
  import { loadOneValue } from "../utils/rest.js";
  import { printerUrl } from "../utils/url.js";
  import SeiteEditor from "./SeiteEditor.svelte";

  export let seiteId;

  async function onChange() {
    try {
      loading = true;
      await tick();
      await reloadOneSeite();
    } finally {
      loading = false;
    }
  }
  async function onCancel() {
    await reloadOneSeite();
  }

  let allDateitypItem = [];
  let allLandItem = [];
  let allNutzerItem = [];
  let allSpracheItem = [];
  let allTerminserieItem = [];
  let loading = true;
  onMount(async () => {
    try {
      loading = true;
      allDateitypItem = await loadAllValue("/api/enum/dateityp");
      console.log(["onMount", allDateitypItem]);
      allLandItem = await loadAllValue("/api/enum/land");
      console.log(["onMount", allLandItem]);
      allNutzerItem = await loadAllValue("/api/nutzer/search/findAllItem");
      console.log(["onMount", allNutzerItem]);
      allSpracheItem = await loadAllValue("/api/enum/sprache");
      console.log(["onMount", allSpracheItem]);
      allTerminserieItem = await loadAllValue("/api/enum/terminserie");
      console.log(["onMount", allTerminserieItem]);
      await reloadOneSeite();
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  let seite;
  function reloadOneSeite() {
    return loadOneValue("/api/seite/" + seiteId)
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadOneSeite", msg]);
        seite = json;
      })
      .catch((err) => {
        console.log(["reloadOneSeite", err]);
        seite = {};
        toast.push(err.toString());
      });
  }
</script>

<div class="flex flex-col gap-1 ml-2 mr-2">
  {#if seite}{#key seite}
      <h1>{seite.uri}</h1>
      <fieldset class="p-4 border-2">
        <legend class="text-xl">Seite bearbeiten</legend>
        <SeiteEditor
          on:update={onChange}
          visible={true}
          autofocus={true}
          autoscroll={false}
          {allDateitypItem}
          {allLandItem}
          {allNutzerItem}
          {allSpracheItem}
          {allTerminserieItem}
          ordnerId={seite.ordnerId}
          {seite}
        >
          <div slot="cancel">
            <Button type="reset" onclick={onCancel}>Verwerfen</Button>
          </div>
        </SeiteEditor>
      </fieldset>
      <fieldset class="p-4 border-2">
        <legend class="text-xl">Seite anzeigen</legend>
        <div class="flex flex-row gap-1 items-center">
          {#each seite.allOption as option}
            {@const href = "/doc/seite/" + seite.id + ".html?" + option}
            {@const title = seite.titel + " im Format HTML anzeigen"}
            <a
              class="font-semibold font-mono"
              href={printerUrl(href)}
              target="_blank"
              {title}
            >
              <span class="p-1 text-xl text-white bg-primary-500 rounded">
                {option}
              </span>
            </a>
          {:else}
            {@const href = "/doc/seite/" + seite.id + ".html"}
            {@const title = seite.titel + " im Format HTML anzeigen"}
            <a
              class="font-semibold font-mono"
              href={printerUrl(href)}
              target="_blank"
              {title}
            >
              <span class="p-1 text-xl text-white bg-primary-500 rounded">
                ##
              </span>
            </a>
          {/each}
        </div>
      </fieldset>
    {/key}{/if}
</div>
