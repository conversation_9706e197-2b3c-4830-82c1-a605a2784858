package esy.app.plan;

import esy.api.plan.Risiko;
import esy.api.plan.VorgangStatus;
import esy.auth.JwtRole;
import esy.rest.JsonJpaRestControllerBase;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.history.Revision;
import org.springframework.data.rest.core.annotation.RepositoryEventHandler;
import org.springframework.data.rest.webmvc.BasePathAwareController;
import org.springframework.hateoas.CollectionModel;
import org.springframework.hateoas.EntityModel;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.Optional;
import java.util.UUID;

@RepositoryEventHandler
@BasePathAwareController
public class RisikoRestController extends JsonJpaRestControllerBase<Risiko, UUID> {

    private final RisikoRepository risikoRepository;

    @Autowired
    public RisikoRestController(
            @NonNull final ApplicationEventPublisher eventPublisher,
            @NonNull final TransactionTemplate transactionTemplate,
            @NonNull final RisikoRepository risikoRepository) {
        super(eventPublisher, transactionTemplate);
        this.risikoRepository = risikoRepository;
    }

    // tag::allowDelete[]
    @Override
    protected boolean allowDelete(@NonNull final Risiko value, @NonNull Authentication auth) {
        return hasRole(auth, JwtRole.VERWALTUNG);
    }
    // end::allowDelete[]

    void verifyStatusTransition(@NonNull final UUID id, @NonNull final VorgangStatus statusOld, @NonNull final VorgangStatus statusNew) {
        final var allStatusFollowUp = VorgangStatus.forUpdate(statusOld);
        if (!allStatusFollowUp.contains(statusNew)) {
            throw new DataIntegrityViolationException(
                    String.format("Risiko(%s) not patched, expected one of %s but got %s",
                            id, allStatusFollowUp, statusNew));
        }
    }

    // tag::beforeUpdateTransaction[]
    @Override
    protected Optional<TransactionCallback<Void>> beforeUpdateTransaction(@NonNull final Risiko value) {
        final var statusOld = risikoRepository.findStatusById(value.getId());
        final var statusNew = value.getStatus();
        verifyStatusTransition(value.getId(), statusOld, statusNew);
        return Optional.empty();
    }
    // end::beforeUpdateTransaction[]

    Risiko updateStatusTransition(@NonNull final Risiko value, @NonNull final VorgangStatus status) {
        return updateInTransaction(value, tx -> {
            value.applyStatusTransition(status);
            return risikoRepository.save(value);
        });
    }

    // tag::patchRisikoStatusTransition[]
    @PatchMapping("/risiko/{id}/{status}")
    public ResponseEntity<EntityModel<Risiko>> patchRisikoStatusTransition(@PathVariable("id") final UUID id, @PathVariable("status") final VorgangStatus statusNew) {
        final var valueOld = risikoRepository.getReferenceById(id);
        verifyStatusTransition(valueOld.getId(), valueOld.getStatus(), statusNew);
        final var valueNew = updateStatusTransition(valueOld, statusNew);
        return ResponseEntity
                .status(HttpStatus.OK)
                .eTag(Long.toString(valueNew.getVersion()))
                .body(EntityModel.of(valueNew));
    }
    // end::patchRisikoStatusTransition[]

    // tag::getAllRisikoHistory[]
    @GetMapping("/risiko/{id}/history")
    public ResponseEntity<CollectionModel<Risiko>> getAllRisikoHistory(@PathVariable("id") final UUID id) {
        final var allValue = risikoRepository.findRevisions(id)
                .stream()
                .map(Revision::getEntity)
                .toList();
        return ResponseEntity.status(HttpStatus.OK).body(CollectionModel.of(allValue));
    }
    // end::getAllRisikoHistory[]
}
