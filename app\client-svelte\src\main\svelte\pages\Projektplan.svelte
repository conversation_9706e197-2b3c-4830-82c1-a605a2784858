<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import { toast } from "../components/Toast";
  import { formatDate } from "../utils/date.js";
  import { today } from "../utils/date.js";
  import { loadAllValue } from "../utils/rest.js";
  import { createValue } from "../utils/rest.js";
  import { mapify } from "../utils/list.js";
  import { splitByWord } from "../utils/text.js";
  import { storedProjekt } from "../stores/projekt.js";
  import { Circle } from "svelte-loading-spinners";
  import Icon from "../components/Icon";
  import Select from "../components/Select";
  import TextArea from "../components/TextArea";
  import TextField from "../components/TextField";
  import ProjektplanCard from "./ProjektplanCard.svelte";

  let allNutzerItem = [];
  let allProjektItem = [];
  let loading = true;
  onMount(async () => {
    try {
      loading = true;
      allNutzerItem = await loadAllValue("/api/nutzer/search/findAllItem");
      console.log(["onMount", allNutzerItem]);
      allProjektItem = await loadAllValue("/api/projekt/search/findAllItem");
      console.log(["onMount", allProjektItem]);
      aufgabeProjektId = $storedProjekt.id;
      aufgabeTerminVon = $storedProjekt.von;
      aufgabeTerminBis = $storedProjekt.bis;
      if (aufgabeProjektId) {
        await reloadAllAufgabe();
      }
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  async function onAufgabeFilterClicked() {
    if (aufgabeProjektId) {
      try {
        loading = true;
        $storedProjekt.id = aufgabeProjektId;
        $storedProjekt.von = aufgabeTerminVon;
        $storedProjekt.bis = aufgabeTerminBis;
        await tick();
        await reloadAllAufgabe();
      } finally {
        loading = false;
      }
    }
  }

  $: allAufgabeByTermin = mapify(
    allAufgabe,
    (e) => e.termin,
    (e1, e2) => e1.id.localeCompare(e2.id)
  );

  async function onCreateAufgabeClicked() {
    if (!aufgabeProjektId) return;
    if (!aufgabeTitel) return;
    if (!aufgabeText) return;
    try {
      loading = true;
      await tick();
      await createAufgabe({
        projektId: aufgabeProjektId,
        termin: new Date(),
        titel: aufgabeTitel,
        text: aufgabeText,
        allStichwort: splitByWord(aufgabeText),
      });
      aufgabeTitel = "";
      aufgabeText = "";
    } finally {
      loading = false;
    }
  }

  let aufgabeProjektId = undefined;
  let aufgabeTerminVon;
  let aufgabeTerminBis;
  let aufgabeTitel;
  let aufgabeText;
  async function onAufgabeTerminChanged() {
    let von = new Date(aufgabeTerminVon);
    let bis = new Date(aufgabeTerminBis);
    if (von > bis) aufgabeTerminBis = undefined;
  }
  async function onAufgabeTerminVon(n) {
    aufgabeTerminVon = formatDate(today(n));
  }
  async function onAufgabeTerminBis(n) {
    aufgabeTerminBis = formatDate(today(n * 7));
  }

  let allAufgabe = [];
  function onCreateAufgabe(aufgabe) {
    allAufgabe = allAufgabe.toSpliced(0, 0, aufgabe);
  }
  function onUpdateAufgabe(aufgabe) {
    let index = allAufgabe.findIndex((e) => e.id === aufgabe.id);
    if (index > -1) allAufgabe = allAufgabe.toSpliced(index, 1, aufgabe);
  }
  function onRemoveAufgabe(aufgabe) {
    let index = allAufgabe.findIndex((e) => e.id === aufgabe.id);
    if (index > -1) allAufgabe = allAufgabe.toSpliced(index, 1);
  }
  function reloadAllAufgabe() {
    const allTerminQuery = [];
    if (aufgabeProjektId) {
      allTerminQuery.push("projekt.id=" + aufgabeProjektId);
    }
    if (aufgabeTerminVon) {
      allTerminQuery.push("termin=" + aufgabeTerminVon);
    }
    if (aufgabeTerminBis) {
      allTerminQuery.push("termin=" + aufgabeTerminBis);
    }
    if (allTerminQuery) {
      const query = "?sort=termin,text&" + allTerminQuery.join("&");
      return loadAllValue("/api/aufgabe" + query)
        .then((json) => {
          const msg = import.meta.env.DEV ? json : json.length;
          console.log(["reloadAllAufgabe", query, msg]);
          allAufgabe = json;
        })
        .catch((err) => {
          console.log(["reloadAllAufgabe", query, err]);
          allAufgabe = [];
          toast.push(err.toString());
        });
    } else {
      allAufgabe = [];
    }
  }
  function removeAllAufgabe() {
    allAufgabe = [];
  }

  function createAufgabe(aufgabe) {
    return createValue("/api/aufgabe", aufgabe)
      .then((json) => {
        console.log(["createAufgabe", aufgabe, json]);
        onCreateAufgabe(json);
      })
      .catch((err) => {
        console.log(["createAufgabe", aufgabe, err]);
        toast.push(err.toString());
      });
  }
</script>

<h1>Projektplan</h1>
<div class="flex flex-col gap-1 ml-2 mr-2">
  <form on:submit|preventDefault={onAufgabeFilterClicked}>
    <div class="flex flex-row gap-1 items-center sm:items-baseline">
      <div class="flex flex-col sm:flex-row gap-1 w-full">
        <div class="w-full sm:w-2/4">
          <Select
            bind:value={aufgabeProjektId}
            onchange={removeAllAufgabe}
            allItem={allProjektItem}
            valueGetter={(v) => v?.value}
            nullable
            required
            label="Projekt"
            placeholder="Bitte ein Projekt wählen"
          />
        </div>
        <div class="w-full sm:w-1/4">
          <TextField
            bind:value={aufgabeTerminVon}
            on:change={onAufgabeTerminChanged}
            required
            type="date"
            label="Termine von"
            placeholder="Bitte ein Datum wählen"
          />
          <div class="text-sm">
            <a href="#von" on:click={() => onAufgabeTerminVon(0)}>Heute</a>
            <a href="#von" on:click={() => onAufgabeTerminVon(1)}>Morgen</a>
          </div>
        </div>
        <div class="w-full sm:w-1/4">
          <TextField
            bind:value={aufgabeTerminBis}
            on:change={onAufgabeTerminChanged}
            required
            type="date"
            label="Termine bis"
            placeholder="Bitte ein Datum wählen"
          />
          <div class="text-sm">
            <a href="#bis" on:click={() => onAufgabeTerminBis(1)}>+1</a>,
            <a href="#bis" on:click={() => onAufgabeTerminBis(2)}>+2</a> oder
            <a href="#bis" on:click={() => onAufgabeTerminBis(4)}>+4</a> Wochen.
          </div>
        </div>
      </div>
      <div class="w-min">
        <Icon type="submit" name="search" outlined />
      </div>
    </div>
  </form>
  <form on:submit|preventDefault={onCreateAufgabeClicked}>
    <div class="flex flex-row gap-1 items-center">
      <div class="flex flex-col w-full">
        <div class="w-full">
          <TextField
            bind:value={aufgabeTitel}
            required
            disabled={!aufgabeProjektId}
            label="Titel"
            placeholder="Bitte einen Titel eingeben"
          />
        </div>
        <div class="w-full">
          <TextArea
            bind:value={aufgabeText}
            required
            disabled={!aufgabeProjektId}
            label="Text"
            placeholder="Bitte einen Text eingeben"
            rows="2"
          />
        </div>
      </div>
      <div class="w-min">
        <Icon type="submit" name="add" outlined />
      </div>
    </div>
  </form>
  {#if loading}
    <div class="h-screen flex justify-center items-center">
      <Circle size="60" unit="px" duration="1s" />
    </div>
  {:else}
    {#each [...allAufgabeByTermin] as [key, allAufgabeOfTermin]}
      <h4>{key} <small>({allAufgabeOfTermin.length})</small></h4>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-1">
        {#each allAufgabeOfTermin as aufgabe}
          {#key aufgabe.id}
            <ProjektplanCard
              on:update={(e) => onUpdateAufgabe(e.detail)}
              on:remove={(e) => onRemoveAufgabe(e.detail)}
              {aufgabe}
              {allNutzerItem}
            />
          {/key}
        {/each}
      </div>
    {:else}
      <span class="px-2 py-3">Keine Aufgaben</span>
    {/each}
  {/if}
</div>
