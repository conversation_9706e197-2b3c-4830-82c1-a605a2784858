<script>
  import { createEventDispatcher, tick } from "svelte";
  const dispatch = createEventDispatcher();
  import filterProps from "../filterProps.js";
  const props = filterProps(
    ["allItem", "disabled", "icon", "title", "value", "valueGetter"],
    $$props
  );
  export let allItem;
  export let disabled = false;
  export let icon = undefined;
  export let title = undefined;
  export let value;
  export let valueGetter = undefined;

  let _element;
  export function focus() {
    _element.focus();
  }

  $: _primitive =
    allItem.slice(0, 1).findIndex((e) => typeof e !== "object") !== -1;

  $: _textSelected = itemText(value);
  function itemText(v) {
    if (_primitive) {
      return v || "";
    } else {
      if (typeof valueGetter === "function") {
        const item = allItem.find((e) => valueGetter(e) === v);
        return item?.text || "";
      } else {
        return v?.text || "";
      }
    }
  }

  $: _allItemInternal = allItem.map((e) => itemMapper(e));
  function itemMapper(e) {
    if (_primitive) {
      return {
        value: e,
        text: e,
      };
    } else {
      if (typeof valueGetter === "function") {
        return {
          value: valueGetter(e),
          text: e.text,
        };
      } else {
        return {
          value: e.value,
          text: e.text,
        };
      }
    }
  }

  async function onListItemEnter(item) {
    _textSelected = item.text || "";
  }

  let _listVisible = false;
  let _listFilter = "";
  async function onListItemClick(item) {
    _element.focus();
    _listFilter = "";
    _listVisible = false;
    _textSelected = item.text;
  }

  async function onButtonClick() {
    const item = _allItemInternal.find((e) => _textSelected === e.text);
    if (_primitive) {
      value = item.value || null;
    } else {
      if (typeof valueGetter === "function") {
        value = item.value;
      } else {
        value = allItem.find((e) => e.value === item.value);
      }
    }
    await tick();
    await dispatch("click", value);
  }
</script>

<div class="mt-1 relative">
  <button
    type="button"
    bind:this={_element}
    {...props}
    {title}
    disabled={disabled || !_textSelected}
    class="w-full text-sm text-black rounded py-2 px-4 disabled:opacity-50 hover:opacity-90 focus:ring bg-gray-100 overflow-hidden"
    on:click={onButtonClick}
    on:mouseover
    on:focus
    on:blur
  >
    <span class="flex items-center gap-2">
      {#if icon}
        <i class="material-icons text-lg">{icon}</i>
      {/if}
      {_textSelected || title || ""}
    </span>
  </button>
  <button
    type="button"
    {disabled}
    class="absolute disabled:opacity-50 inset-y-0 right-0 flex items-center pr-2"
    tabindex="-1"
    on:click={() => (_listVisible = !_listVisible)}
  >
    <i class="material-icons icon text-xl">unfold_more</i>
  </button>
  {#if _listVisible}
    <ul
      class="absolute z-50 border-gray-300 border rounded-xl shadow-lg bg-gray-100 w-full"
      tabindex="-1"
    >
      {#each _allItemInternal as item}
        {@const active = _textSelected === item.text}
        <!-- svelte-ignore a11y-click-events-have-key-events -->
        <!-- svelte-ignore a11y-no-noninteractive-element-interactions -->
        <li
          class="pl-4 relative cursor-default select-none text-black"
          class:bg-blue-600={active}
          class:text-white={active}
          tabindex="-1"
          on:click|preventDefault={() => onListItemClick(item)}
          on:mouseenter={() => onListItemEnter(item)}
        >
          <span class="min-w-full block truncate font-normal">{item.text}</span>
        </li>
      {/each}
    </ul>
  {/if}
</div>
