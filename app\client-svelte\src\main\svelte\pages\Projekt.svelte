<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import { toast } from "../components/Toast";
  import { loadAllValue } from "../utils/rest.js";
  import { updatePatch } from "../utils/rest.js";
  import { removeValue } from "../utils/rest.js";
  import { Circle } from "svelte-loading-spinners";
  import Icon from "../components/Icon";
  import Checkbox from "../components/Checkbox";
  import TextField from "../components/TextField";
  import ProjektEditor from "./ProjektEditor.svelte";
  import ProjektOption from "./ProjektOption.svelte";
  import ProjektQuelle from "./ProjektQuelle.svelte";
  import ProjektTeam from "./ProjektTeam.svelte";
  import AufgabeLister from "./AufgabeLister.svelte";

  let allNutzerItem = [];
  let allQuelleItem = [];
  let allSpracheItem = [];
  let allTerminserieItem = [];
  let loading = true;
  onMount(async () => {
    try {
      loading = true;
      allNutzerItem = await loadAllValue("/api/nutzer/search/findAllItem");
      console.log(["onMount", allNutzerItem]);
      allQuelleItem = await loadAllValue("/api/enum/quelle");
      console.log(["onMount", allQuelleItem]);
      allSpracheItem = await loadAllValue("/api/enum/sprache");
      console.log(["onMount", allSpracheItem]);
      allTerminserieItem = await loadAllValue("/api/enum/terminserie");
      console.log(["onMount", allTerminserieItem]);
      await reloadAllProjekt();
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  let projektId = undefined;
  async function onProjektClicked(projekt) {
    projektId = projekt.id;
  }
  async function onProjektRemoveClicked(projekt) {
    projektId = projekt.id;
    await removeProjekt(projekt);
  }
  let projektEditorCreate = false;
  async function onProjektEditorCreateClicked() {
    projektEditorCreate = true;
    projektEditorUpdate = false;
    projektTeamUpdate = false;
    projektOptionUpdate = false;
    projektQuelleUpdate = false;
    aufgabeListerCreated = false;
  }
  let projektEditorUpdate = false;
  async function onProjektEditorUpdateClicked(projekt) {
    projektId = projekt.id;
    projektEditorCreate = false;
    projektEditorUpdate = true;
    projektTeamUpdate = false;
    projektOptionUpdate = false;
    projektQuelleUpdate = false;
    aufgabeListerCreated = false;
  }
  let projektOptionUpdate = false;
  async function onProjektOptionUpdateClicked(projekt) {
    projektId = projekt.id;
    projektEditorCreate = false;
    projektEditorUpdate = false;
    projektTeamUpdate = false;
    projektOptionUpdate = true;
    projektQuelleUpdate = false;
    aufgabeListerCreated = false;
  }
  let projektQuelleUpdate = false;
  async function onProjektQuelleUpdateClicked(projekt) {
    projektId = projekt.id;
    projektEditorCreate = false;
    projektEditorUpdate = false;
    projektTeamUpdate = false;
    projektOptionUpdate = false;
    projektQuelleUpdate = true;
    aufgabeListerCreated = false;
  }
  let projektTeamUpdate = false;
  async function onProjektTeamUpdateClicked(projekt) {
    projektId = projekt.id;
    projektEditorCreate = false;
    projektEditorUpdate = false;
    projektTeamUpdate = true;
    projektOptionUpdate = false;
    projektQuelleUpdate = false;
    aufgabeListerCreated = false;
  }
  let aufgabeListerCreated = false;
  async function onAufgabeListerClicked(projekt) {
    if (projektId !== projekt.id) {
      projektId = projekt.id;
      aufgabeListerCreated = true;
    } else {
      aufgabeListerCreated = !aufgabeListerCreated;
    }
  }
  $: projektEditorDisabled =
    projektEditorCreate ||
    projektEditorUpdate ||
    projektOptionUpdate ||
    projektQuelleUpdate ||
    projektTeamUpdate ||
    aufgabeListerCreated;

  let projektFilter;
  function projektFilterParameter() {
    if (!projektFilter) return "";
    return "&name=" + encodeURIComponent(projektFilter);
  }
  function projektSortParameter() {
    return "?sort=name";
  }
  async function onProjektFilterClicked() {
    try {
      loading = true;
      await tick();
      await reloadAllProjekt();
    } finally {
      loading = false;
    }
  }

  let allProjekt = [];
  function onCreateProjekt(projekt) {
    allProjekt = allProjekt.toSpliced(0, 0, projekt);
  }
  function onUpdateProjekt(projekt) {
    let index = allProjekt.findIndex((e) => e.id === projekt.id);
    if (index > -1) allProjekt = allProjekt.toSpliced(index, 1, projekt);
  }
  function onRemoveProjekt(projekt) {
    let index = allProjekt.findIndex((e) => e.id === projekt.id);
    if (index > -1) allProjekt = allProjekt.toSpliced(index, 1);
  }
  function reloadAllProjekt() {
    const query = projektSortParameter() + projektFilterParameter();
    return loadAllValue("/api/projekt" + query)
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadAllProjekt", query, msg]);
        allProjekt = json;
      })
      .catch((err) => {
        console.log(["reloadAllProjekt", query, err]);
        allProjekt = [];
        toast.push(err.toString());
      });
  }

  function updateProjekt(projekt) {
    return updatePatch("/api/projekt/" + projekt.id, projekt)
      .then((json) => {
        console.log(["updateProjekt", projekt, json]);
        onUpdateProjekt(json);
      })
      .catch((err) => {
        console.log(["updateProjekt", projekt, err]);
        toast.push(err.toString());
      });
  }
  function removeProjekt(projekt) {
    const text = projekt.name;
    const hint = text.length > 20 ? text.substring(0, 20) + "..." : text;
    if (!confirm("Projekt '" + hint + "' wirklich löschen?")) return;
    return removeValue("/api/projekt" + "/" + projekt.id)
      .then((json) => {
        console.log(["removeProjekt", projekt, json]);
        onRemoveProjekt(json);
      })
      .catch((err) => {
        console.log(["removeProjekt", projekt, err]);
        toast.push(err.toString());
      });
  }
</script>

<h1 title="Liste der Projekte, ggfs. gefiltert, jedes Element editierbar">
  Projekt
</h1>
<div class="flex flex-col gap-1 ml-2 mr-2">
  <form on:submit|preventDefault={onProjektFilterClicked}>
    <div class="flex flex-row gap-1 items-center pr-2">
      <div class="w-full">
        <TextField
          bind:value={projektFilter}
          label="Filter"
          placeholder="Bitte Filterkriterien eingeben"
        />
      </div>
      <div class="w-min">
        <Icon type="submit" name="search" outlined />
      </div>
    </div>
  </form>
  {#if loading}
    <div class="h-screen flex justify-center items-center">
      <Circle size="60" unit="px" duration="1s" />
    </div>
  {:else}
    <table class="table-fixed">
      <thead class="justify-between">
        <tr class="bg-title-100">
          <th class="px-2 py-3 table-cell">
            <span class="text-title-600 text-sm">Aktiv</span>
          </th>
          <th class="px-2 py-3 text-left w-full sm:w-1/3 table-cell">
            <span class="text-title-600">Name</span>
          </th>
          <th class="px-2 py-3 text-left w-full hidden sm:table-cell">
            <span class="text-title-600">Team</span>
          </th>
          <th class="px-2 py-3 text-right w-0 table-cell">
            <Icon
              on:click={() => onProjektEditorCreateClicked()}
              disabled={projektEditorDisabled}
              title="Projekt hinzufügen"
              name="add"
              outlined
            />
          </th>
        </tr>
      </thead>
      <tbody>
        {#if projektEditorCreate}
          <tr>
            <td class="px-2" colspan="4">
              <ProjektEditor
                bind:visible={projektEditorCreate}
                on:create={(e) => onCreateProjekt(e.detail)}
                {allSpracheItem}
              />
            </td>
          </tr>
        {/if}
        {#each allProjekt as projekt, i}
          <tr
            on:click={(e) => onProjektClicked(projekt)}
            title={projekt.id}
            class:border-l-2={projektId === projekt.id}
            class:bg-gray-100={i % 2 === 1}
          >
            <td class="px-2 py-3 table-cell">
              <Checkbox
                bind:checked={projekt.aktiv}
                on:change={() => updateProjekt(projekt)}
              />
            </td>
            <td class="px-2 py-3 text-left table-cell">
              <div class="underline text-blue-600">
                <a href={"/projekt/" + projekt.id}>{projekt.name}</a>
              </div>
            </td>
            <td class="px-2 py-3 text-left hidden sm:table-cell">
              <div class="flex flex-col">
                {#each projekt.allMitgliedItem as nutzerItem}
                  <div class="text-sm underline text-blue-600">
                    <a href={"/nutzer/" + nutzerItem.value}>{nutzerItem.text}</a
                    >
                  </div>
                {:else}
                  <div class="text-sm underline text-blue-600">
                    <a href={"/nutzer/"}>Keine Mitglieder</a>
                  </div>
                {/each}
              </div>
            </td>
            <td class="px-2 py-3 table-cell">
              <div
                class="grid grid-cols-1 md:grid-cols-6 items-center gap-1 w-max"
              >
                <Icon
                  on:click={() => onAufgabeListerClicked(projekt)}
                  disabled={projektEditorDisabled && !aufgabeListerCreated}
                  title="Aufgaben anzeigen"
                  name="list"
                  outlined
                />
                <Icon
                  on:click={() => onProjektTeamUpdateClicked(projekt)}
                  disabled={projektEditorDisabled}
                  title="Projekt bearbeiten"
                  name="group"
                  outlined
                />
                <Icon
                  on:click={() => onProjektOptionUpdateClicked(projekt)}
                  disabled={projektEditorDisabled}
                  title="Projekt bearbeiten"
                  name="flag"
                  outlined
                />
                <Icon
                  on:click={() => onProjektQuelleUpdateClicked(projekt)}
                  disabled={projektEditorDisabled}
                  title="Projekt bearbeiten"
                  name="star"
                  outlined
                />
                <Icon
                  on:click={() => onProjektRemoveClicked(projekt)}
                  disabled={projektEditorDisabled || projekt.aktiv}
                  title="Projekt löschen"
                  name="delete"
                  outlined
                />
                <Icon
                  on:click={() => onProjektEditorUpdateClicked(projekt)}
                  disabled={projektEditorDisabled}
                  title="Projekt bearbeiten"
                  name="edit"
                  outlined
                />
              </div>
            </td>
          </tr>
          {#if aufgabeListerCreated && projektId === projekt.id}
            <tr>
              <td class="border-l-4 px-2" colspan="4">
                <AufgabeLister
                  on:create={(e) => onCreateProjekt(e.detail)}
                  on:update={(e) => onUpdateProjekt(e.detail)}
                  on:remove={(e) => onRemoveProjekt(e.detail)}
                  {projektId}
                  {allNutzerItem}
                  {allTerminserieItem}
                />
              </td>
            </tr>
          {/if}
          {#if projektOptionUpdate && projektId === projekt.id}
            <tr>
              <td class="border-l-4 px-2" colspan="4">
                <ProjektOption
                  bind:visible={projektOptionUpdate}
                  on:update={(e) => onUpdateProjekt(e.detail)}
                  {projekt}
                />
              </td>
            </tr>
          {/if}
          {#if projektQuelleUpdate && projektId === projekt.id}
            <tr>
              <td class="border-l-4 px-2" colspan="4">
                <ProjektQuelle
                  bind:visible={projektQuelleUpdate}
                  on:update={(e) => onUpdateProjekt(e.detail)}
                  {projekt}
                  {allQuelleItem}
                />
              </td>
            </tr>
          {/if}
          {#if projektTeamUpdate && projektId === projekt.id}
            <tr>
              <td class="border-l-4 px-2" colspan="4">
                <ProjektTeam
                  bind:visible={projektTeamUpdate}
                  on:update={(e) => onUpdateProjekt(e.detail)}
                  {projekt}
                  {allNutzerItem}
                />
              </td>
            </tr>
          {/if}
          {#if projektEditorUpdate && projektId === projekt.id}
            <tr>
              <td class="border-l-4 px-2" colspan="4">
                <ProjektEditor
                  bind:visible={projektEditorUpdate}
                  on:update={(e) => onUpdateProjekt(e.detail)}
                  {projekt}
                  {allSpracheItem}
                />
              </td>
            </tr>
          {/if}
        {:else}
          <tr>
            <td class="px-2" colspan="4">Keine Projekte</td>
          </tr>
        {/each}
      </tbody>
    </table>
  {/if}
</div>
