<script>
  import { onMount } from "svelte";
  import { createEventDispatcher } from "svelte";
  import { toast } from "../components/Toast";
  import { createValue } from "../utils/rest.js";
  import { updatePatch } from "../utils/rest.js";
  import Button from "../components/Button";
  import Select from "../components/Select";
  import TextField from "../components/TextField";

  export let visible = false;
  export let autofocus = true;
  export let autoscroll = true;
  export let ordner = undefined;
  export let allOrdnerartItem;

  let focusOn;
  let bottomDiv;
  onMount(async () => {
    console.log(["onMount", autofocus, autoscroll]);
    if (autofocus) focusOn.focus();
    if (autoscroll) bottomDiv.scrollIntoView(false);
  });

  let clicked = false;
  let showUpdate;
  let newOrdner = {
    art: "GITHUB",
    uri: undefined,
    titel: "",
    aktiv: true,
  };

  $: if (ordner) onChange();
  async function onChange() {
    showUpdate = ordner.id;
    newOrdner = {
      id: ordner.id,
      art: ordner.art,
      uri: ordner.uri,
      titel: ordner.titel,
      aktiv: ordner.aktiv,
      version: ordner.version,
    };
    console.log(["onChange", newOrdner]);
  }

  async function onSubmit() {
    try {
      clicked = true;
      if (showUpdate) {
        await updateOrdner();
      } else {
        await createOrdner();
      }
    } finally {
      clicked = false;
    }
  }
  async function onCancel() {
    visible = false;
  }

  const dispatch = createEventDispatcher();
  function createOrdner() {
    return createValue("/api/ordner", newOrdner)
      .then((json) => {
        console.log(["createOrdner", newOrdner, json]);
        visible = false;
        dispatch("create", json);
      })
      .catch((err) => {
        console.log(["createOrdner", newOrdner, err]);
        toast.push(err.toString());
      });
  }
  function updateOrdner() {
    return updatePatch("/api/ordner/" + newOrdner.id, newOrdner)
      .then((json) => {
        console.log(["updateOrdner", newOrdner, json]);
        visible = false;
        dispatch("update", json);
      })
      .catch((err) => {
        console.log(["updateOrdner", newOrdner, err]);
        toast.push(err.toString());
      });
  }
</script>

<form on:submit|preventDefault={onSubmit} on:reset|preventDefault={onCancel}>
  <div class="flex flex-col sm:flex-row gap-1">
    <div class="w-full sm:w-4/12">
      <TextField
        bind:this={focusOn}
        bind:value={newOrdner.titel}
        required
        label="Titel"
        placeholder="Bitte einen Titel eingeben"
      />
    </div>
    <div class="w-full sm:w-2/12">
      <Select
        bind:value={newOrdner.art}
        allItem={allOrdnerartItem.map((e) => e.name)}
        label="Art"
        placeholder="Bitte eine Art wählen"
      />
    </div>
    <div class="w-full sm:w-6/12">
      <TextField
        bind:value={newOrdner.uri}
        required
        label="Repository"
        placeholder="Bitte einen Wert eingeben"
      />
    </div>
  </div>
  <div class="py-4 flex flex-row gap-1 items-baseline">
    <div class="flex-initial">
      <Button type="submit">Ok</Button>
    </div>
    <div class="flex-initial">
      <slot name="cancel">
        <Button type="reset">Abbrechen</Button>
      </slot>
    </div>
  </div>
</form>

<div class="h-0" bind:this={bottomDiv}>&nbsp;</div>

{#if import.meta.env.DEV}
  <details tabindex="-1">
    <summary>JSON</summary>
    <pre>{JSON.stringify(newOrdner, null, 2)}</pre>
  </details>
{/if}
