<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import Button from "../components/Button";
  import { toast } from "../components/Toast";
  import { loadOneValue } from "../utils/rest.js";
  import ProzessEditor from "./ProzessEditor.svelte";

  export let prozessId;

  let loading = true;
  onMount(async () => {
    try {
      loading = true;
      await reloadOneProzess();
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  async function onChange() {
    try {
      loading = true;
      await tick();
      await reloadOneProzess();
    } finally {
      loading = false;
    }
  }
  async function onCancel() {
    await reloadOneProzess();
  }

  let prozess;
  function reloadOneProzess() {
    return loadOneValue("/api/prozess/" + prozessId)
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadOneProzess", msg]);
        prozess = json;
      })
      .catch((err) => {
        console.log(["reloadOneProzess", err]);
        prozess = {};
        toast.push(err.toString());
      });
  }
</script>

<div class="flex flex-col gap-1 ml-2 mr-2">
  {#if prozess}{#key prozess}
      <h1>{prozess.name}</h1>
      <fieldset class="p-4 border-2">
        <legend class="text-xl">Prozess bearbeiten</legend>
        <ProzessEditor
          on:update={onChange}
          visible={true}
          autofocus={true}
          autoscroll={false}
          {prozess}
        >
          <div slot="cancel">
            <Button type="reset" onclick={onCancel}>Verwerfen</Button>
          </div>
        </ProzessEditor>
      </fieldset>
    {/key}{/if}
</div>
