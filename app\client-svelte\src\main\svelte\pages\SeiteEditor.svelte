<script>
  import { onMount } from "svelte";
  import { createEventDispatcher } from "svelte";
  import { toast } from "../components/Toast";
  import { createValue } from "../utils/rest.js";
  import { updateValue } from "../utils/rest.js";
  import Button from "../components/Button";
  import LinkRef from "../components/LinkRef";
  import Select from "../components/Select";
  import TextField from "../components/TextField";
  import Toggle from "../components/Toggle";

  export let visible = false;
  export let autofocus = true;
  export let autoscroll = true;
  export let ordnerId;
  export let seite = undefined;
  export let allDateitypItem;
  export let allLandItem;
  export let allNutzerItem;
  export let allSpracheItem;
  export let allTerminserieItem;

  let focusOn;
  let bottomDiv;
  onMount(async () => {
    console.log(["onMount", autofocus, autoscroll]);
    if (autofocus) focusOn.focus();
    if (autoscroll) bottomDiv.scrollIntoView(false);
  });

  let clicked = false;
  let showUpdate;
  let newSeite = {
    nutzerId: undefined,
    ordnerId: ordnerId,
    ordnerBlob: undefined,
    typ: "ADOC",
    uri: undefined,
    termin: null,
    terminserie: "X",
    titel: "",
    text: "",
    sprache: "DE",
    allOption: [],
    aktiv: true,
  };

  $: if (seite) onChange();
  async function onChange() {
    showUpdate = seite.id;
    newSeite = {
      id: seite.id,
      nutzerId: seite.nutzerId,
      ordnerId: seite.ordnerId,
      ordnerBlob: seite.ordnerBlob,
      typ: seite.typ,
      uri: seite.uri,
      termin: seite.termin,
      terminserie: seite.terminserie,
      titel: seite.titel,
      text: seite.text,
      sprache: seite.sprache,
      allOption: seite.allOption,
      aktiv: seite.aktiv,
      version: seite.version,
    };
    console.log(["onChange", newSeite]);
  }

  async function onSubmit() {
    try {
      clicked = true;
      if (showUpdate) {
        await updateSeite();
      } else {
        await createSeite();
      }
    } finally {
      clicked = false;
    }
  }
  async function onCancel() {
    visible = false;
  }

  const dispatch = createEventDispatcher();
  function createSeite() {
    return createValue("/api/seite", newSeite)
      .then((json) => {
        console.log(["createSeite", newSeite, json]);
        visible = false;
        dispatch("create", json);
      })
      .catch((err) => {
        console.log(["createSeite", newSeite, err]);
        toast.push(err.toString());
      });
  }
  function updateSeite() {
    return updateValue("/api/seite/" + newSeite.id, newSeite)
      .then((json) => {
        console.log(["updateSeite", newSeite, json]);
        visible = false;
        dispatch("update", json);
      })
      .catch((err) => {
        console.log(["updateSeite", newSeite, err]);
        toast.push(err.toString());
      });
  }
</script>

<form on:submit|preventDefault={onSubmit} on:reset|preventDefault={onCancel}>
  <div class="flex flex-col sm:flex-row gap-1">
    <div class="flex flex-col grow">
      <div class="w-full flex flex-col md:flex-row gap-1">
        <div class="w-full md:w-48">
          <TextField
            bind:this={focusOn}
            bind:value={newSeite.termin}
            required
            type="date"
            label="Termin"
            placeholder="Bitte einen Termin wählen"
          />
        </div>
        <div class="w-full md:w-1/3">
          <Select
            bind:value={newSeite.terminserie}
            allItem={allTerminserieItem}
            valueGetter={(v) => v?.value}
            label="Wiederholung"
            placeholder="Bitte eine Terminserie wählen"
          />
        </div>
        <div class="w-full md:w-2/3">
          <Select
            bind:value={newSeite.nutzerId}
            allItem={allNutzerItem}
            valueGetter={(v) => v?.value}
            nullable
            label="Nutzer"
            placeholder="Bitte einen Nutzer wählen"
          />
        </div>
      </div>
      <div class="w-full flex flex-col sm:flex-row gap-1">
        <div class="w-full sm:w-3/5">
          <TextField
            bind:value={newSeite.titel}
            required
            label="Titel"
            placeholder="Bitte einen Titel eingeben"
          />
        </div>
        <div class="w-full sm:w-1/5">
          <Select
            bind:value={newSeite.typ}
            required
            allItem={allDateitypItem.map((e) => e.name)}
            label="Typ"
            placeholder="Bitte einen Typ wählen"
          />
        </div>
        <div class="w-full sm:w-1/5">
          <Select
            bind:value={newSeite.sprache}
            required
            valueGetter={(v) => v?.value}
            allItem={allSpracheItem}
            label="Sprache"
            placeholder="Bitte eine Sprache wählen"
          />
        </div>
      </div>
      <div class="w-full">
        <Toggle
          bind:allValue={newSeite.allOption}
          allItem={allLandItem.map((e) => e.name)}
          label="Optionen"
          placeholder="Bitte Optionen wählen"
        />
      </div>
      <div class="w-full">
        <LinkRef path={newSeite.ordnerBlob} bind:value={newSeite.uri} />
      </div>
    </div>
  </div>
  <div class="py-4 flex flex-row gap-1 items-baseline">
    <div class="flex-initial">
      <Button type="submit">Ok</Button>
    </div>
    <div class="flex-initial">
      <slot name="cancel">
        <Button type="reset">Abbrechen</Button>
      </slot>
    </div>
  </div>
</form>

<div class="h-0" bind:this={bottomDiv}>&nbsp;</div>

{#if import.meta.env.DEV}
  <details tabindex="-1">
    <summary>JSON</summary>
    <pre>{JSON.stringify(newSeite, null, 2)}</pre>
  </details>
{/if}
