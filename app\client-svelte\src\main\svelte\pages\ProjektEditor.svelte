<script>
  import { onMount } from "svelte";
  import { createEventDispatcher } from "svelte";
  import { toast } from "../components/Toast";
  import { createValue } from "../utils/rest.js";
  import { updatePatch } from "../utils/rest.js";
  import Button from "../components/Button";
  import Select from "../components/Select";
  import TextField from "../components/TextField";

  export let visible = false;
  export let autofocus = true;
  export let autoscroll = true;
  export let projekt = undefined;
  export let allSpracheItem;

  let clicked = false;
  let focusOn;
  let bottomDiv;
  onMount(async () => {
    console.log(["onMount", autofocus, autoscroll]);
    if (autofocus) focusOn.focus();
    if (autoscroll) bottomDiv.scrollIntoView(false);
  });

  let showUpdate;
  let newProjekt = {
    name: "",
    sprache: undefined,
    aktiv: true,
  };

  $: if (projekt) onChange();
  async function onChange() {
    showUpdate = projekt.id;
    newProjekt = {
      id: projekt.id,
      name: projekt.name,
      sprache: projekt.sprache,
      aktiv: projekt.aktiv,
      version: projekt.version,
    };
    console.log(["onChange", newProjekt]);
  }

  async function onSubmit() {
    try {
      clicked = true;
      if (showUpdate) {
        await updateProjekt();
      } else {
        await createProjekt();
      }
    } finally {
      clicked = false;
    }
  }
  async function onCancel() {
    visible = false;
  }

  const dispatch = createEventDispatcher();
  function createProjekt() {
    return createValue("/api/projekt", newProjekt)
      .then((json) => {
        console.log(["createProjekt", newProjekt, json]);
        visible = false;
        dispatch("create", json);
      })
      .catch((err) => {
        console.log(["createProjekt", newProjekt, err]);
        toast.push(err.toString());
      });
  }
  function updateProjekt() {
    return updatePatch("/api/projekt" + "/" + newProjekt.id, newProjekt)
      .then((json) => {
        console.log(["updateProjekt", newProjekt, json]);
        visible = false;
        dispatch("update", json);
      })
      .catch((err) => {
        console.log(["updateProjekt", newProjekt, err]);
        toast.push(err.toString());
      });
  }
</script>

<form on:submit|preventDefault={onSubmit} on:reset|preventDefault={onCancel}>
  <div class="flex flex-col sm:flex-row gap-1">
    <div class="flex flex-row gap-1">
      <div class="w-3/4">
        <TextField
          bind:this={focusOn}
          bind:value={newProjekt.name}
          required
          label="Name"
          placeholder="Bitte einen Namen eingeben"
        />
      </div>
      <div class="w-1/4">
        <div class="w-full">
          <TextField bind:value={newProjekt.id} disabled label="Code" />
        </div>
      </div>
    </div>
    <div class="w-full">
      <Select
        bind:value={newProjekt.sprache}
        valueGetter={(v) => v?.value}
        allItem={allSpracheItem}
        required
        label="Sprache"
        placeholder="Bitte eine Sprache wählen"
      />
    </div>
  </div>
  <div class="py-4 flex flex-row gap-1 items-baseline">
    <div class="flex-initial">
      <Button type="submit">Ok</Button>
    </div>
    <div class="flex-initial">
      <slot name="cancel">
        <Button type="reset">Abbrechen</Button>
      </slot>
    </div>
  </div>
</form>

<div class="h-0" bind:this={bottomDiv}>&nbsp;</div>

{#if import.meta.env.DEV}
  <details tabindex="-1">
    <summary>JSON</summary>
    <pre>{JSON.stringify(newProjekt, null, 2)}</pre>
  </details>
{/if}
