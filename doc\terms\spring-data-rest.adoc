:keyword: library,java,spring
:term: spring-data-rest

https://spring.io/projects/spring-data-rest[Spring Data REST]
baut auf Spring Data JPA auf.
Mit einem gültigen JPA-Modell lässt sich ganz leicht ein HAL-konformes voll funktionsfähiges REST-API mit den Operationen GET, POST, PUT, PATCH und DELETE realisieren.
Alle für das REST-API notwendigen Persistenzfunktionen werden von den JPA-Repositories bereitgestellt.
Mit Callbacks kann individuelle Logik in den Operationen hinzugefügt werden.
Zusätzliche Operationen können bei Bedarf auch individuell direkt im REST-Controller implementiert werden.
