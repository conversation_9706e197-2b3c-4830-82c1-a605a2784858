:keyword: practice,documentation
:term: docs-as-code

https://www.writethedocs.org/guide/docs-as-code[_Documentation as Code_]
ist eine Praxis in der Softwareentwicklung, die dazu führt, dass ein Entwicklungsteam für Dokumentation und Implementierung mit denselben Werkzeugen arbeiten und die gleichen Arbeitsabläufe haben.
Dies ermöglicht eine Kultur, in der sich sowohl Autoren als auch Entwickler für die Dokumentation verantwortlich fühlen und gemeinsam daran arbeiten.
Teile des Codes werden direkt zur Dokumentation, z.B. Schema- und Konfigurationsdateien, Skripte oder Auszüge aus Klassen.
Die Dokumentation wird in einem Versionskontrollsystem verwaltet und automatisiert veröffentlicht.
