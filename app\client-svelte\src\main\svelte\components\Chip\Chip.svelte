<script lang="ts">
  let {
    checked = $bindable(false),
    clicked = $bindable(0),
    disabled = false,
    icon,
    outlined = false,
    title = undefined,
    onclick = undefined,
    children,
    ...elementProps
  } = $props();

  let element;
  export function focus() {
    element?.focus();
  }

  function handleClick(_event: MouseEvent) {
    checked = !checked;
    clicked++;
    onclick?.(_event);
  }
</script>

<button
  type="button"
  aria-label={icon}
  bind:this={element}
  {...elementProps}
  {title}
  {disabled}
  class:disabled
  class="text-sm text-white rounded-full py-2 px-4 disabled:opacity-50 hover:opacity-90 focus:ring bg-primary-500 overflow-hidden"
  class:outlined
  onclick={handleClick}
>
  <div class="flex flex-row content-center justify-start items-center gap-1">
    <i
      {title}
      aria-hidden="true"
      class="material-icons icon text-xl select-none duration-200 ease-in"
    >
      {icon}
    </i>
    {@render children?.()}
  </div>
</button>
