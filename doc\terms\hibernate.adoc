:keyword: library,java,jpa,orm
:term: hibernate

Hibernate ist eine leistungsstarke Java-Bibliothek, die für _Object-Relational Mapping_ (kurz ORM) fest im Spring-Universum verankert ist.
Hibernate ist solide und ausgereift.
Die API bietet umfassende Möglichkeiten, Java-Objekte mit Objekten in einer relationalen Datenbank in objektorientierter Weise zu verbinden.
Ziel ist es, Datenbankoperationen zu generieren.
