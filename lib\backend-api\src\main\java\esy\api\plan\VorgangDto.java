package esy.api.plan;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import esy.api.wiki.Seite;
import esy.json.JsonDocument;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;

import java.time.LocalDate;
import java.util.*;

/**
 * Ein {@link VorgangDto Vorgang} repräsentiert eine Aktivität mit einer Fälligkeit.
 */
@EqualsAndHashCode
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder(alphabetic = true)
@JsonDocument
public class VorgangDto {

    @Getter
    @JsonProperty
    private final VorgangTyp typ;

    @Getter
    @JsonProperty
    private final UUID id;

    @Getter
    @JsonProperty
    private final long version;

    @Getter
    @JsonProperty
    private final String titel;

    @Getter
    @JsonProperty
    private final String text;

    @Getter
    @JsonProperty
    @JsonFormat(pattern = "yyyy-MM-dd")
    private final LocalDate termin;

    @Getter
    @JsonProperty
    private final Terminserie terminserie;

    @Getter
    @JsonProperty
    private final VorgangStatus status;

    @Getter
    @JsonProperty
    private final boolean aktiv;

    @Getter
    @JsonProperty
    private final boolean wiedervorlage;

    @JsonProperty
    private final List<VorgangStatus> allStatusTransition = new ArrayList<>();

    @JsonProperty
    private final Set<String> allStichwort = new LinkedHashSet<>();

    public VorgangDto(@NonNull final Aufgabe value) {
        this.typ = VorgangTyp.AUFTRAG;
        this.id = value.getId();
        this.version = value.getVersion();
        this.titel = value.getTitel();
        this.text = value.getText();
        this.termin = value.getTermin();
        this.terminserie = value.getTerminserie();
        this.status = value.getStatus();
        this.aktiv = value.isAktiv();
        this.wiedervorlage = value.isWiedervorlage();
        this.allStatusTransition.addAll(VorgangStatus.forUpdate(this.status));
        this.allStichwort.addAll(value.getAllStichwort());
    }

    public VorgangDto(@NonNull final Meldung value) {
        this.typ = VorgangTyp.MELDUNG;
        this.id = value.getId();
        this.version = value.getVersion();
        this.titel = value.getTitel();
        this.text = value.getText();
        this.termin = value.getTermin();
        this.terminserie = Terminserie.X;
        this.status = value.getStatus();
        this.allStatusTransition.add(VorgangStatus.X);
        this.allStatusTransition.add(VorgangStatus.I);
        this.allStatusTransition.remove(this.status);
        this.aktiv = value.isAktiv();
        this.wiedervorlage = value.isWiedervorlage();
    }

    public VorgangDto(@NonNull final Risiko value) {
        this.typ = VorgangTyp.RISIKO;
        this.id = value.getId();
        this.version = value.getVersion();
        this.titel = value.getTitel();
        this.text = value.getText();
        this.termin = value.getTermin();
        this.terminserie = value.getTerminserie();
        this.status = value.getStatus();
        this.allStatusTransition.addAll(VorgangStatus.forUpdate(this.status));
        this.aktiv = value.isAktiv();
        this.wiedervorlage = value.isWiedervorlage();
    }

    public VorgangDto(@NonNull final Seite value) {
        this.typ = VorgangTyp.SEITE;
        this.id = value.getId();
        this.version = value.getVersion();
        this.titel = value.getTitel();
        this.text = value.getText();
        this.termin = value.getTermin();
        this.terminserie = value.getTerminserie();
        this.status = value.getStatus();
        this.allStatusTransition.add(VorgangStatus.X);
        this.allStatusTransition.add(VorgangStatus.B);
        this.allStatusTransition.remove(this.status);
        this.aktiv = value.isAktiv();
        this.wiedervorlage = value.isWiedervorlage();
    }

    @JsonProperty
    String getPageUri() {
        return String.join("/", this.typ.getBaseUri(), this.id.toString());
    }
}
