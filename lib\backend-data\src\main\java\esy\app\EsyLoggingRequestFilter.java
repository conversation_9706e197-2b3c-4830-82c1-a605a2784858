package esy.app;

import lombok.RequiredArgsConstructor;
import org.springframework.web.filter.AbstractRequestLoggingFilter;

import jakarta.servlet.http.HttpServletRequest;

@RequiredArgsConstructor
public class EsyLoggingRequestFilter extends AbstractRequestLoggingFilter {

    private final boolean enabled;

    @Override
    protected boolean shouldLog(final HttpServletRequest request) {
        return enabled;
    }

    /**
     * Writes a log message before the request is processed.
     */
    @Override
    protected void beforeRequest(final HttpServletRequest request, final String message) {
        logger.info(message);
    }

    /**
     * Writes a log message after the request is processed.
     */
    @Override
    protected void afterRequest(final HttpServletRequest request, final String message) {
        logger.info(message);
    }
}
