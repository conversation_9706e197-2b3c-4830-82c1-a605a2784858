package esy.api.wiki;

import esy.api.info.Land;
import esy.api.info.Sprache;
import esy.api.plan.Terminserie;
import esy.api.plan.VorgangStatus;
import esy.api.team.Nutzer;
import esy.json.JsonMapper;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@Tag("fast")
@SuppressWarnings("java:S5961") // allow more than 25 assertions
class SeiteTest {

    Seite createWithTitel(final String titel) {
        final var json = """
                {
                    "uri":"%s.adoc",
                    "titel":"%s",
                    "text":"Lorem ipsum dolor sit amet.",
                    "sprache":"DE",
                    "aktiv":"false",
                    "termin":"2019-04-22",
                    "allOption":["DE","AT"]
                }
                """.formatted(titel, titel);
        return Seite.parseJson(json)
                .setOrdner(Ordner.parseJson("""
                        {
                            "id":"018e3257-ddb1-a173-a73e-829058a09c35",
                            "uri":"https://github.com/cardsplus/demoscs",
                            "titel":"DEMOSCS"
                        }
                        """));
    }

    @Test
    void equalsHashcodeToString() {
        final var titel = "README";
        final var value = createWithTitel(titel);
        // Identisches Objekt
        assertEquals(value, value);
        assertTrue(value.isEqual(value));
        assertEquals(value.hashCode(), value.hashCode());
        assertEquals(value.toString(), value.toString());
        // Gleiches Objekt
        final var clone = createWithTitel(titel).withId(value.getId());
        assertNotSame(clone, value);
        assertEquals(clone, value);
        assertTrue(value.isEqual(clone));
        assertEquals(clone.hashCode(), value.hashCode());
        assertEquals(clone.toString(), value.toString());
        // Gleicher Titel
        final var equal = createWithTitel(titel);
        assertNotSame(equal, value);
        assertNotEquals(equal, value);
        assertTrue(value.isEqual(equal));
        assertNotEquals(equal.hashCode(), value.hashCode());
        assertNotEquals(equal.toString(), value.toString());
        // Anderer Titel
        final var other = createWithTitel("HELPME");
        assertNotEquals(other, value);
        assertFalse(value.isEqual(other));
        assertNotEquals(other.hashCode(), value.hashCode());
        assertNotEquals(other.toString(), value.toString());
        // Kein Objekt
        assertNotEquals(null, value);
        assertFalse(value.isEqual(null));
        // Falsches Objekt
        assertNotEquals(this, value);
    }

    @Test
    void withId() {
        final var titel = "README";
        final var value0 = createWithTitel(titel);
        final var value1 = value0.withId(value0.getId());
        assertSame(value0, value1);
        final var value2 = value0.withId(UUID.randomUUID());
        assertNotSame(value0, value2);
        assertTrue(value0.isEqual(value2));
    }

    @Test
    void json() {
        final var titel = "README";
        final var value = createWithTitel(titel);
        assertDoesNotThrow(value::verify);
        assertNotNull(value.getId());
        assertNotNull(value.getTyp());
        assertNotNull(value.getUri());
        assertEquals(titel, value.getTitel());
        assertEquals(Sprache.DE.name(), value.getSprache());
        assertEquals(2, value.getAllOption().size());
        assertTrue(value.getAllOption().contains(Land.AT.name()));
        assertTrue(value.getAllOption().contains(Land.DE.name()));
        assertFalse(value.isAktiv());
        assertEquals(VorgangStatus.X, value.getStatus());
        assertEquals(2019, value.getTermin().getYear());
        assertEquals(4, value.getTermin().getMonthValue());
        assertEquals(22, value.getTermin().getDayOfMonth());
        assertFalse(value.isFaellig(LocalDate.of(2019, 4, 21)));
        assertFalse(value.isFaellig(LocalDate.of(2019, 4, 22)));
        assertTrue(value.isFaellig(LocalDate.of(2019, 4, 23)));
        assertNotNull(value.getOrdner());
        assertNotNull(value.getOrdnerBlob());
        assertNull(value.getNutzer());

        final var jsonReader = new JsonMapper().parseJsonPath(value.writeJson());
        assertNotNull(jsonReader.read("id"));
        assertEquals(0, jsonReader.read("version", Integer.class));
        assertEquals(value.isAktiv(), jsonReader.read("aktiv", Boolean.class));
        assertEquals(value.getTitel(), jsonReader.read("titel", String.class));
        assertEquals(value.getText(), jsonReader.read("text", String.class));
        assertEquals(value.getTermin(), jsonReader.read("termin", LocalDate.class));
        assertEquals(value.getTerminserie(), jsonReader.read("terminserie", Terminserie.class));
        assertEquals(value.getSprache(), jsonReader.read("sprache", String.class));
        assertEquals(value.getTyp(), jsonReader.read("typ", Dateityp.class));
        assertEquals(value.getUri(), jsonReader.read("uri", String.class));
        assertNotNull(jsonReader.read("allOption"));
        assertNotNull(jsonReader.read("ordnerBlob"));
    }

    @Test
    void jsonOrdner() {
        final var titel = "README";
        final var json = """
                {
                    "uri":"%s.adoc",
                    "titel":"%s"
                }
                """.formatted(titel, titel);
        final var value = Seite.parseJson(json);
        assertDoesNotThrow(value::verify);
        assertEquals(titel, value.getTitel());
        assertNull(value.getOrdner());

        final var ordner = Ordner.parseJson("""
                {
                    "name":"Ordner A"
                }
                """);
        value.setOrdner(ordner);
        assertDoesNotThrow(value::verify);
        assertSame(ordner, value.getOrdner());

        value.setOrdner(null);
        assertDoesNotThrow(value::verify);
        assertNull(value.getOrdner());
    }

    @Test
    void jsonNutzer() {
        final var titel = "README";
        final var json = """
                {
                    "uri":"%s.adoc",
                    "titel":"%s"
                }
                """.formatted(titel, titel);
        final var value = Seite.parseJson(json);
        assertDoesNotThrow(value::verify);
        assertEquals(titel, value.getTitel());
        assertNull(value.getNutzer());
        assertFalse(value.isBesitzer("<EMAIL>"));

        final var nutzer = Nutzer.parseJson("""
                {
                    "mail":"<EMAIL>",
                    "name":"Nutzer A"
                }""");
        value.setNutzer(nutzer);
        assertDoesNotThrow(value::verify);
        assertSame(nutzer, value.getNutzer());
        assertTrue(value.isBesitzer("<EMAIL>"));

        value.setNutzer(null);
        assertDoesNotThrow(value::verify);
        assertNull(value.getNutzer());
        assertFalse(value.isBesitzer("<EMAIL>"));
    }

    @Test
    void jsonOption() {
        final var titel = "HELPME";
        final var value = createWithTitel(titel);
        assertDoesNotThrow(value::verify);
        assertEquals(2, value.getAllOption().size());
        assertEquals("AT,DE", String.join(",", value.getAllOption()));

        value.getAllOption().addAll(List.of("DE", "AT", "IT"));
        assertDoesNotThrow(value::verify);
        assertEquals(3, value.getAllOption().size());
        assertEquals("AT,DE,IT", String.join(",", value.getAllOption()));

        value.getAllOption().clear();
        assertDoesNotThrow(value::verify);
        assertEquals(0, value.getAllOption().size());

        value.getAllOption().add("DE");
        assertDoesNotThrow(value::verify);
        assertEquals(1, value.getAllOption().size());
        assertEquals("DE", String.join(",", value.getAllOption()));

        value.getAllOption().add("AT");
        assertDoesNotThrow(value::verify);
        assertEquals(2, value.getAllOption().size());
        assertEquals("AT,DE", String.join(",", value.getAllOption()));

        value.getAllOption().remove("DE");
        assertDoesNotThrow(value::verify);
        assertEquals(1, value.getAllOption().size());
        assertEquals("AT", String.join(",", value.getAllOption()));
    }
}
