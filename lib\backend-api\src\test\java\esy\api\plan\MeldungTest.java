package esy.api.plan;

import esy.api.team.Nutzer;
import esy.json.JsonMapper;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.time.LocalDate;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@Tag("fast")
@SuppressWarnings("java:S5961") // allow more than 25 assertions
class MeldungTest {

    Meldung createWithTitel(final String titel) {
        final var json = "{" +
                "\"titel\":\"" + titel + "\"," +
                "\"text\":\"Lorem ipsum dolor sit amet.\"," +
                "\"termin\":\"2019-04-22\"," +
                "\"aktiv\":\"false\"" +
                "}";
        return Meldung.parseJson(json);
    }

    @Test
    void equalsHashcodeToString() {
        final var titel = "Ausfall der Stromversorgung";
        final var value = createWithTitel(titel);
        // Identisches Objekt
        assertEquals(value, value);
        assertTrue(value.isEqual(value));
        assertEquals(value.hashCode(), value.hashCode());
        assertEquals(value.toString(), value.toString());
        // Gleiches Objekt
        final var clone = createWithTitel(titel).withId(value.getId());
        assertNotSame(clone, value);
        assertEquals(clone, value);
        assertTrue(value.isEqual(createWithTitel(titel)));
        assertEquals(clone.hashCode(), value.hashCode());
        assertEquals(clone.toString(), value.toString());
        // Gleicher Name
        final var equal = createWithTitel(titel);
        assertNotSame(equal, value);
        assertNotEquals(equal, value);
        assertTrue(value.isEqual(createWithTitel(titel)));
        assertNotEquals(equal.hashCode(), value.hashCode());
        assertNotEquals(equal.toString(), value.toString());
        // Anderer Name
        final var other = createWithTitel("Ausfall von Arbeitsgeräten");
        assertNotEquals(other, value);
        assertFalse(value.isEqual(other));
        assertNotEquals(other.hashCode(), value.hashCode());
        assertNotEquals(other.toString(), value.toString());
        // Kein Objekt
        assertNotEquals(null, value);
        assertFalse(value.isEqual(null));
        // Falsches Objekt
        assertNotEquals(this, value);
    }

    @Test
    void withId() {
        final var titel = "Ausfall der Stromversorgung";
        final var value0 = createWithTitel(titel);
        final var value1 = value0.withId(value0.getId());
        assertSame(value0, value1);
        final var value2 = value0.withId(UUID.randomUUID());
        assertNotSame(value0, value2);
        assertTrue(value0.isEqual(value2));
    }

    @SuppressWarnings("java:S5778")
    @ParameterizedTest
    @ValueSource(strings = {
            "{}",
            "{\"aktiv\":\"false\"}",
            "{\"titel\":\"\"}",
            "{\"titel\":\" \"}",
            "{\"termin\":\"\"}",
            "{\"termin\":\"2024\"}",
            "{\"termin\":\"2024-04\"}",
            "{\"termin\":\"heute\"}"
    })
    void jsonConstraints(final String json) {
        assertThrows(IllegalArgumentException.class, () -> Meldung.parseJson(json).verify());
    }

    @Test
    void json() {
        final var titel = "Ausfall der Stromversorgung";
        final var value = createWithTitel(titel);
        assertDoesNotThrow(value::verify);
        assertNotNull(value.getId());
        assertFalse(value.isAktiv());
        assertEquals(VorgangStatus.X, value.getStatus());
        assertEquals(titel, value.getTitel());
        assertNotNull(value.getText());
        assertNotNull(value.getTermin());
        assertFalse(value.isWiedervorlage());
        assertFalse(value.isFaellig(LocalDate.of(2019, 4, 21)));
        assertFalse(value.isFaellig(LocalDate.of(2019, 4, 22)));
        assertTrue(value.isFaellig(LocalDate.of(2019, 4, 23)));

        final var jsonReader = new JsonMapper().parseJsonPath(value.writeJson());
        assertNotNull(jsonReader.read("id"));
        assertEquals(0, jsonReader.read("version", Integer.class));
        assertEquals(value.isAktiv(), jsonReader.read("aktiv", Boolean.class));
        assertEquals(value.getTitel(), jsonReader.read("titel", String.class));
        assertEquals(value.getText(), jsonReader.read("text", String.class));
        assertEquals(value.getTermin(), jsonReader.read("termin", LocalDate.class));
    }

    @Test
    void jsonNutzer() {
        final var titel = "Ausfall der Stromversorgung";
        final var value = createWithTitel(titel);
        assertDoesNotThrow(value::verify);
        assertEquals(titel, value.getTitel());
        assertNull(value.getNutzer());
        assertFalse(value.isBesitzer("<EMAIL>"));

        final var nutzer = Nutzer.parseJson("{" +
                "\"mail\":\"<EMAIL>\"," +
                "\"name\":\"Nutzer A\"" +
                "}");
        value.setNutzer(nutzer);
        assertDoesNotThrow(value::verify);
        assertSame(nutzer, value.getNutzer());
        assertTrue(value.isBesitzer("<EMAIL>"));

        value.setNutzer(null);
        assertDoesNotThrow(value::verify);
        assertNull(value.getNutzer());
        assertFalse(value.isBesitzer("<EMAIL>"));
    }
}
