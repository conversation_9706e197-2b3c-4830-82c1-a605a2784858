<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import Button from "../components/Button";
  import { toast } from "../components/Toast";
  import { loadAllValue } from "../utils/rest.js";
  import { loadOneValue } from "../utils/rest.js";
  import { Circle } from "svelte-loading-spinners";
  import MeldungEditor from "./MeldungEditor.svelte";

  export let meldungId;

  async function onChange() {
    try {
      loading = true;
      await tick();
      await reloadOneMeldung();
    } finally {
      loading = false;
    }
  }
  async function onCancel() {
    await reloadOneMeldung();
  }

  let allNutzerItem = [];
  let loading = true;
  onMount(async () => {
    try {
      loading = true;
      allNutzerItem = await loadAllValue("/api/nutzer/search/findAllItem");
      console.log(["onMount", allNutzerItem]);
      await reloadOneMeldung();
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  let meldung;
  function reloadOneMeldung() {
    return loadOneValue("/api/meldung/" + meldungId)
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadOneMeldung", msg]);
        meldung = json;
      })
      .catch((err) => {
        console.log(["reloadOneMeldung", err]);
        meldung = {};
        toast.push(err.toString());
      });
  }
</script>

{#if loading}
  <div class="h-screen flex justify-center items-center">
    <Circle size="60" unit="px" duration="1s" />
  </div>
{:else}
  <div class="flex flex-col gap-1 ml-2 mr-2">
    {#if meldung}{#key meldung}
        <h1>{meldung.titel}</h1>
        <fieldset class="p-4 border-2">
          <legend class="text-xl">Meldung bearbeiten</legend>
          <MeldungEditor
            on:update={onChange}
            visible={true}
            autofocus={true}
            autoscroll={false}
            {allNutzerItem}
            {meldung}
          >
            <div slot="cancel">
              <Button type="reset" onclick={onCancel}>Verwerfen</Button>
            </div>
          </MeldungEditor>
        </fieldset>
      {/key}{/if}
  </div>
{/if}
