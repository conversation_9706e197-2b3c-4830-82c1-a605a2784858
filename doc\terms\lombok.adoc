:keyword: library,java
:term: lombok

https://projectlombok.org/[Lombok]
hilft, überflüssigen Java-Code zu vermeiden.
Mit Annotationen werden Konstruktoren, Getter-, Setter- und verschiedene andere Methoden automatisch erzeugt.
Der Code wird dadurch wesentlich besser lesbar.
Der Einsatz von Lombok reduziert lästige Fehlerquellen und ist dadurch eine Maßnahme für Wartbarkeit, weil wir generierten Code (z.B. equals-, hashcode- oder toString-Methoden) durch ausdrucksstarke Annotationen ersetzen.
