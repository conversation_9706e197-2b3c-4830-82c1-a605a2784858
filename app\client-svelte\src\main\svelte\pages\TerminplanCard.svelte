<script>
  import { createEventDispatcher } from "svelte";
  import { toast } from "../components/Toast";
  import { updatePatch } from "../utils/rest.js";
  import Action from "../components/Action";
  export let vorgang;
  export let allTerminserieItem;
  function hinweise() {
    const terminserieText = allTerminserieItem.find(
      (e) => e.value === vorgang.terminserie
    )?.text;
    switch (vorgang.typ) {
      case "AUFTRAG":
        return ["wird", terminserieText, "wiederholt"].join(" ");
      case "RISIKO":
        return ["wird", terminserieText, "überprüft"].join(" ");
      default:
        return "";
    }
  }

  $: newStatus = vorgang.allStatusTransition[0];
  $: newStatusTransition = vorgang.allStatusTransition.map((e) => {
    switch (e) {
      case "I":
        return { value: e, text: "Identifizieren" };
      case "B":
        return { value: e, text: "Bestätigen" };
      case "A":
        return { value: e, text: "Abbrechen" };
      case "X":
        return { value: e, text: "Schließen" };
      default:
        return { value: e, text: e };
    }
  });

  const dispatch = createEventDispatcher();
  function updateStatus() {
    return updatePatch("/api" + vorgang.pageUri + "/" + newStatus, vorgang)
      .then((json) => {
        console.log(["updateStatus", newStatus, json]);
        dispatch("update", json);
      })
      .catch((err) => {
        console.log(["updateStatus", newStatus, err]);
        toast.push(err.toString());
      });
  }
</script>

<div class="block p-2 rounded-lg shadow-lg">
  <div class="flex flex-col gap-1 pt-2">
    <div class="w-full">
      <a class="text-sm underline text-blue-600" href={vorgang.pageUri}
        >{vorgang.typ}</a
      >
      <span class="text-xs">{hinweise()}</span>
    </div>
    <div class="w-full">
      <p class="text-lg font-semibold">{vorgang.titel}</p>
    </div>
    {#if newStatusTransition.length > 0}
      <div class="w-full">
        <Action
          bind:value={newStatus}
          valueGetter={(v) => v?.value}
          allItem={newStatusTransition}
          on:click={updateStatus}
          label="Aktion"
          title="Wähle eine Aktion"
        />
      </div>
    {/if}
    <div class="w-full">
      <p class="text-sm whitespace-pre-line">{vorgang.text}</p>
    </div>
  </div>

  {#if import.meta.env.DEV}
    <details tabindex="-1">
      <summary tabindex="-1">JSON</summary>
      <pre>{JSON.stringify(vorgang, null, 2)}</pre>
    </details>
  {/if}
</div>
