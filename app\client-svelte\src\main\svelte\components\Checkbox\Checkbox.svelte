<script>
  let {
    checked = $bindable(),
    disabled = false,
    label = undefined,
    title,
    onchange = undefined,
    ...elementProps
  } = $props();

  let checkedInternal = $state(checked);
  function handleChange() {
    checked = checkedInternal;
    onchange?.(checked);
  }
</script>

<div class="relative w-auto h-auto flex flex-col pt-1">
  <label {title} class="inline-flex items-center px-2 cursor-pointer">
    <input
      {...elementProps}
      {title}
      class="disabled:opacity-50 border-2 border-primary-500 text-primary-500"
      type="checkbox"
      {disabled}
      bind:checked={checkedInternal}
      onchange={handleChange}
    />
    {#if label}
      <div class="pl-2 cursor-pointer text-label-700">
        {label}
      </div>
    {/if}
  </label>
</div>
