package esy.api.plan;

import esy.api.info.Enumerable;
import lombok.NonNull;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;

public enum VorgangStatus implements Enumerable {
  I("identifiziert"),
  B("bestätigt"),
  A("abgelehnt"),
  X("geschlossen");

  static final Map<VorgangStatus, List<VorgangStatus>> allTransition = Map.of(
          VorgangStatus.I, List.of(B, A, X, I),
          VorgangStatus.B, List.of(B, A, X),
          VorgangStatus.A, List.of(A, B, X),
          VorgangStatus.X, List.of(X, I)
  );

  private final String text;

  VorgangStatus(final String text) {
    this.text = text;
  }

  /**
   * Returns the initial status.
   */
  public static VorgangStatus forCreate() {
    return VorgangStatus.I;
  }

  /**
   * Returns all possible follow-up status.
   */
  public static List<VorgangStatus> forUpdate(@NonNull final VorgangStatus status) {
    return allTransition.get(status);
  }

  /**
   * Returns the final status.
   */
  public static VorgangStatus forDelete() {
    return VorgangStatus.X;
  }

  public static VorgangStatus fromString(@NonNull final String value) throws IllegalArgumentException {
    return VorgangStatus.fromText(value.trim()).orElseGet(() -> VorgangStatus.valueOf(value.trim().toUpperCase()));
  }

  @Override
  public String text() {
    return text;
  }

  public static Optional<VorgangStatus> fromText(@NonNull final String text) {
    return Stream.of(VorgangStatus.values())
            .filter(e -> e.text().equals(text))
            .findFirst();
  }
}
