<script>
  import { onMount } from "svelte";
  import { createEventDispatcher } from "svelte";
  import { toast } from "../components/Toast";
  import { createValue } from "../utils/rest.js";
  import { updatePatch } from "../utils/rest.js";
  import Button from "../components/Button";
  import SelectSaveCsv from "../components/SelectSaveCsv";
  import TextField from "../components/TextField";
  import Toggle from "../components/Toggle";

  export let visible = false;
  export let autofocus = true;
  export let autoscroll = true;
  export let nutzer = undefined;
  export let allAdresseItem;
  export let allSpracheItem;

  let clicked = false;
  let focusOn;
  let bottomDiv;
  onMount(async () => {
    console.log(["onMount", autofocus, autoscroll]);
    if (autofocus) focusOn.focus();
    if (autoscroll) bottomDiv.scrollIntoView(false);
  });

  let showUpdate;
  let newNutzer = {
    name: undefined,
    mail: undefined,
    adresseItem: {
      value: undefined,
      text: "",
    },
    allSprache: [],
    aktiv: true,
  };

  $: if (nutzer) onChange();
  function onChange() {
    showUpdate = nutzer.id;
    newNutzer = {
      id: nutzer.id,
      name: nutzer.name,
      mail: nutzer.mail,
      adresseItem: { ...nutzer.adresseItem },
      allSprache: [...nutzer.allSprache],
      aktiv: nutzer.aktiv,
      version: nutzer.version,
    };
    console.log(["onChange", newNutzer]);
  }

  async function onSubmit() {
    try {
      clicked = true;
      if (newNutzer.adresseItem.value) {
        newNutzer.adresse = "/api/adresse/" + newNutzer.adresseItem.value;
      } else {
        newNutzer.adresse = null;
      }
      if (showUpdate) {
        await updateNutzer();
      } else {
        await createNutzer();
      }
    } finally {
      clicked = false;
    }
  }
  async function onCancel() {
    visible = false;
  }

  const dispatch = createEventDispatcher();
  function createNutzer() {
    return createValue("/api/nutzer", newNutzer)
      .then((json) => {
        console.log(["createNutzer", newNutzer, json]);
        visible = false;
        dispatch("create", json);
      })
      .catch((err) => {
        console.log(["createNutzer", newNutzer, err]);
        toast.push(err.toString());
      });
  }
  function updateNutzer() {
    return updatePatch("/api/nutzer" + "/" + newNutzer.id, newNutzer)
      .then((json) => {
        console.log(["updateNutzer", newNutzer, json]);
        visible = false;
        dispatch("update", json);
      })
      .catch((err) => {
        console.log(["updateNutzer", newNutzer, err]);
        toast.push(err.toString());
      });
  }
</script>

<form on:submit|preventDefault={onSubmit} on:reset|preventDefault={onCancel}>
  <div class="flex flex-col">
    <div class="w-full">
      <TextField
        bind:this={focusOn}
        bind:value={newNutzer.name}
        required
        label="Name"
        placeholder="Bitte einen Namen eingeben"
      />
    </div>
    <div class="w-full">
      <TextField
        bind:value={newNutzer.mail}
        required
        type="email"
        label="E-Mail"
        placeholder="Bitte eine E-Mail-Adresse eingeben"
      />
    </div>
    <div class="w-full">
      <SelectSaveCsv
        url="/api/adresse/csv/item"
        bind:value={newNutzer.adresseItem}
        nullable
        allItem={allAdresseItem}
        label="Adresse"
        placeholder="Bitte eine Adresse eingeben"
      />
    </div>
    <div class="w-full">
      <Toggle
        bind:allValue={newNutzer.allSprache}
        allItem={allSpracheItem}
        label="Sprachen"
        placeholder="Bitte Sprachen wählen"
      />
    </div>
  </div>
  <div class="py-4 flex flex-row gap-1 items-baseline">
    <div class="flex-initial">
      <Button type="submit">Ok</Button>
    </div>
    <div class="flex-initial">
      <slot name="cancel">
        <Button type="reset">Abbrechen</Button>
      </slot>
    </div>
  </div>
</form>

<div class="h-0" bind:this={bottomDiv}>&nbsp;</div>

{#if import.meta.env.DEV}
  <details tabindex="-1">
    <summary>JSON</summary>
    <pre>{JSON.stringify(newNutzer, null, 2)}</pre>
  </details>
{/if}
