package esy.api.plan;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import esy.api.wiki.Seite;
import esy.json.JsonDocument;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

import java.time.LocalDate;
import java.time.Period;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Stream;

/**
 * Ein {@link TerminplanDto Terminplan} repräsentiert einen kalenderbasierten Plan mit Vorgängen unterschiedlicher Art.
 */
@EqualsAndHashCode
@RequiredArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder(alphabetic = true)
@JsonDocument
public class TerminplanDto {

    @JsonProperty
    private final Map<LocalDate, TerminDto> allTag = new TreeMap<>();

    public TerminplanDto apply(@NonNull final LocalDate von, @NonNull final LocalDate bis) {
        final var period = Period.between(von, bis);
        for (var i = 0L; i <= period.getDays(); i++) {
            final var datum = von.plusDays(i);
            allTag.put(datum, new TerminDto(datum));
        }
        return this;
    }

    public TerminplanDto apply(@NonNull final Aufgabe value) {
        final var datum = value.getTermin();
        final var tag = allTag.computeIfAbsent(datum, TerminDto::new);
        tag.add(value);
        return this;
    }

    public TerminplanDto apply(@NonNull final Meldung value) {
        final var datum = value.getTermin();
        final var tag = allTag.computeIfAbsent(datum, TerminDto::new);
        tag.add(value);
        return this;
    }

    public TerminplanDto apply(@NonNull final Risiko value) {
        final var datum = value.getTermin();
        final var tag = allTag.computeIfAbsent(datum, TerminDto::new);
        tag.add(value);
        return this;
    }

    public TerminplanDto apply(@NonNull final Seite value) {
        final var datum = value.getTermin();
        final var tag = allTag.computeIfAbsent(datum, TerminDto::new);
        tag.add(value);
        return this;
    }

    @JsonIgnore
    public boolean isEmpty() {
        return allTag.isEmpty();
    }

    @JsonIgnore
    public int size() {
        return allTag.size();
    }

    @JsonIgnore
    public Stream<TerminDto> stream() {
        return allTag.values().stream();
    }
}
