package esy.api.wiki;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import esy.api.plan.Terminserie;
import esy.api.plan.VorgangStatus;
import esy.api.plan.VorgangStatusAware;
import esy.api.team.Nutzer;
import esy.json.JsonJpaUlidEntity;
import esy.json.JsonMapper;
import lombok.Getter;
import lombok.NonNull;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Entity-Objekt für eine Seite in einem Ordner.
 */
@Entity
@Table(name = "seite", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"id"}),
        @UniqueConstraint(columnNames = {"ordner_id", "uri"})
})
public final class Seite extends JsonJpaUlidEntity<Seite> implements VorgangStatusAware<UUID> {

    public static final String DATE_PATTERN = "yyyy-MM-dd";
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DATE_PATTERN);

    /**
     * Dateityp der Seite im Filesystem.
     */
    // tag::typ[]
    @Column(name = "typ")
    @Enumerated(EnumType.STRING)
    @Getter
    @JsonProperty
    @NotNull
    private Dateityp typ;
    // end::typ[]

    /**
     * Pfad der Seite im Filesystem.
     */
    // tag::uri[]
    @Column(name = "uri")
    @Getter
    @JsonProperty
    @NotBlank
    private String uri;
    // end::uri[]

    /**
     * Titel für die Seite.
     */
    // tag::titel[]
    @Column(name = "titel")
    @Getter
    @JsonProperty
    @NotBlank
    private String titel;
    // end::titel[]

    /**
     * Beschreibung der Seite.
     */
    // tag::text[]
    @Column(name = "text")
    @Getter
    @JsonProperty
    private String text;
    // end::text[]

    /**
     * Sprache für Titel und Text der Seite.
     */
    // tag::sprache[]
    @Column(name = "sprache")
    @Getter
    @JsonProperty
    private String sprache;
    // end::sprache[]

    /**
     * Seite ist aktiv?
     */
    // tag::aktiv[]
    @Column(name = "aktiv")
    @Getter
    @JsonProperty
    private boolean aktiv;
    // end::aktiv[]

    /**
     * Prüfung der Seite ist fällig ab diesem Datum.
     */
    // tag::termin[]
    @Column(name = "termin", columnDefinition = "DATE")
    @Getter
    @JsonProperty
    @NotNull
    @DateTimeFormat(pattern = DATE_PATTERN)
    private LocalDate termin;
    // end::termin[]

    /**
     * Prüfung der Seite wird regelmäßig wiederholt.
     */
    // tag::terminserie[]
    @Column(name = "terminserie")
    @Enumerated(EnumType.STRING)
    @Getter
    @JsonProperty
    private Terminserie terminserie;
    // end::terminserie[]

    /**
     * Bezug zu einem Ordner.
     */
    // tag::ordner[]
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @JoinColumn(name = "ordner_id", referencedColumnName = "id", insertable = false, updatable = false)
    @Getter
    @JsonIgnore
    private Ordner ordner;
    // end::ordner[]

    /**
     * Bezug zu einem Ordner (nur ID).
     */
    @Column(name = "ordner_id")
    @Getter
    @JsonProperty
    private UUID ordnerId;

    /**
     * Bezug zu einem Nutzer.
     */
    // tag::nutzer[]
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = true)
    @JoinColumn(name = "nutzer_id", referencedColumnName = "id", insertable = false, updatable = false)
    @Getter
    @JsonIgnore
    private Nutzer nutzer;
    // end::nutzer[]

    /**
     * Bezug zu einem Nutzer (nur ID).
     */
    // tag::nutzerId[]
    @Column(name = "nutzer_id")
    @Getter
    @JsonProperty
    private UUID nutzerId;
    // end::nutzerId[]

    /**
     * Selektoren für optionalen Text.
     */
    @ElementCollection(
            fetch = FetchType.EAGER)
    @CollectionTable(
            name = "seite_option",
            joinColumns = @JoinColumn(name = "id"))
    @Column(name = "option")
    @Getter
    @JsonProperty
    private SortedSet<String> allOption;

    Seite() {
        super();
        this.typ = Dateityp.ADOC;
        this.uri = "";
        this.titel = "";
        this.text = "";
        this.sprache = "";
        this.aktiv = true;
        this.termin = LocalDate.parse("2000-01-01");
        this.terminserie = Terminserie.X;
        this.ordner = null;
        this.ordnerId = null;
        this.nutzer = null;
        this.nutzerId = null;
        this.allOption = new TreeSet<>();
    }

    Seite(@NonNull final Long version, @NonNull final UUID id) {
        super(version, id);
        this.typ = Dateityp.ADOC;
        this.uri = "";
        this.titel = "";
        this.text = "";
        this.sprache = "";
        this.aktiv = true;
        this.termin = LocalDate.parse("2000-01-01");
        this.terminserie = Terminserie.X;
        this.ordner = null;
        this.ordnerId = null;
        this.nutzer = null;
        this.nutzerId = null;
        this.allOption = new TreeSet<>();
    }

    public static Seite parseJson(@NonNull final String json) {
        return new JsonMapper().parseJson(json, Seite.class);
    }

    @Override
    public String toString() {
        return super.toString() + ",title='" + titel + "'";
    }

    @Override
    public boolean isEqual(final Seite that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        return this.typ.equals(that.typ) &&
                Objects.equals(this.uri, that.uri) &&
                this.titel.equals(that.titel) &&
                this.text.equals(that.text) &&
                this.sprache.equals(that.sprache) &&
                this.aktiv == that.aktiv &&
                this.termin.equals(that.termin) &&
                this.terminserie.equals(that.terminserie) &&
                Objects.equals(this.ordnerId, that.ordnerId) &&
                Objects.equals(this.nutzerId, that.nutzerId) &&
                Objects.equals(this.allOption, that.allOption);
    }

    @Override
    public Seite withId(@NonNull final UUID id) {
        if (Objects.equals(getId(), id)) {
            return this;
        } else {
            return new Seite(getVersion(), id).merge(this);
        }
    }

    @Override
    public Seite merge(@NonNull final Seite that) {
        this.typ = that.typ;
        this.uri = that.uri;
        this.titel = that.titel;
        this.text = that.text;
        this.sprache = that.sprache;
        this.aktiv = that.aktiv;
        this.termin = that.termin;
        this.terminserie = that.terminserie;
        this.ordner = that.ordner;
        this.ordnerId = that.ordnerId;
        this.nutzer = that.nutzer;
        this.nutzerId = that.nutzerId;
        this.allOption = that.allOption;
        return this;
    }

    /**
     * Applies a transition for {@link Seite#aktiv}.
     * Computes a new date for {@link Seite#termin}.
     *
     * @param status {@link VorgangStatus status}
     */
    @Override
    public void applyStatusTransition(@NonNull final VorgangStatus status) {
        this.aktiv = !VorgangStatus.X.equals(status);
        this.termin = this.terminserie.apply(this.termin);
    }

    @JsonAnyGetter
    private Map<String, Object> extraJson() {
        final var allExtra = new HashMap<String, Object>();
        allExtra.put("version", getVersion());
        allExtra.put("ordnerBlob", getOrdnerBlob());
        return allExtra;
    }

    @JsonIgnore
    public boolean isFaellig(@NonNull final LocalDate datum) {
        return datum.isAfter(this.termin);
    }

    @JsonIgnore
    public boolean isWiedervorlage() {
        return !Terminserie.X.equals(this.terminserie);
    }

    @JsonIgnore
    @Override
    public VorgangStatus getStatus() {
        return this.aktiv ? VorgangStatus.B : VorgangStatus.X;
    }

    @JsonIgnore
    public boolean isBesitzer(@NonNull final String mail) {
        return nutzer != null && mail.equals(nutzer.getMail());
    }

    @JsonIgnore
    public String getOrdnerBlob() {
        if (ordner != null) {
            return String.join("/", ordner.getUri(), "blob", "master", uri);
        } else {
            return null;
        }
    }

    @JsonIgnore
    public Seite setOrdner(final Ordner ordner) {
        if (ordner != null) {
            this.ordner = ordner;
            this.ordnerId = ordner.getId();
        } else {
            this.ordner = null;
            this.ordnerId = null;
        }
        return this;
    }

    @JsonIgnore
    public Seite setNutzer(final Nutzer nutzer) {
        if (nutzer != null) {
            this.nutzer = nutzer;
            this.nutzerId = nutzer.getId();
        } else {
            this.nutzer = null;
            this.nutzerId = null;
        }
        return this;
    }

    @Override
    public String writeJson() {
        return new JsonMapper().writeJson(this);
    }
}
