<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import Button from "../components/Button";
  import { toast } from "../components/Toast";
  import { loadAllValue } from "../utils/rest.js";
  import { loadOneValue } from "../utils/rest.js";
  import { Circle } from "svelte-loading-spinners";
  import RisikoEditor from "./RisikoEditor.svelte";
  import RisikoGefahr from "./RisikoGefahr.svelte";
  import RisikoProzess from "./RisikoProzess.svelte";

  export let risikoId;

  async function onChange() {
    try {
      loading = true;
      await tick();
      await reloadOneRisiko();
    } finally {
      loading = false;
    }
  }
  async function onCancel() {
    await reloadOneRisiko();
  }

  let allNutzerItem = [];
  let allGefahrItem = [];
  let allProzessItem = [];
  let allTerminserieItem = [];
  let loading = true;
  onMount(async () => {
    try {
      loading = true;
      allNutzerItem = await loadAllValue("/api/nutzer/search/findAllItem");
      console.log(["onMount", allNutzerItem]);
      allGefahrItem = await loadAllValue("/api/gefahr/search/findAllItem");
      console.log(["onMount", allGefahrItem]);
      allProzessItem = await loadAllValue("/api/prozess/search/findAllItem");
      console.log(["onMount", allProzessItem]);
      allTerminserieItem = await loadAllValue("/api/enum/terminserie");
      console.log(["onMount", allTerminserieItem]);
      await reloadOneRisiko();
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  let risiko;
  function reloadOneRisiko() {
    return loadOneValue("/api/risiko/" + risikoId)
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadOneRisiko", msg]);
        risiko = json;
      })
      .catch((err) => {
        console.log(["reloadOneRisiko", err]);
        risiko = {};
        toast.push(err.toString());
      });
  }
</script>

{#if loading}
  <div class="h-screen flex justify-center items-center">
    <Circle size="60" unit="px" duration="1s" />
  </div>
{:else}
  <div class="flex flex-col gap-1 ml-2 mr-2">
    {#if risiko}{#key risiko}
        <h1>{risiko.titel}</h1>
        <fieldset class="p-4 border-2">
          <legend class="text-xl">Risiko bearbeiten</legend>
          <RisikoEditor
            on:update={onChange}
            visible={true}
            autofocus={true}
            autoscroll={false}
            {allNutzerItem}
            {allTerminserieItem}
            {risiko}
          >
            <div slot="cancel">
              <Button type="reset" on:click={onCancel}>Verwerfen</Button>
            </div>
          </RisikoEditor>
        </fieldset>
        <fieldset class="p-4 border-2">
          <legend class="text-xl">Gefahren bearbeiten</legend>
          <RisikoGefahr
            on:update={onChange}
            visible={true}
            autofocus={false}
            autoscroll={false}
            {allGefahrItem}
            {risiko}
          >
            <div slot="cancel">
              <Button type="reset" on:click={onCancel}>Verwerfen</Button>
            </div>
          </RisikoGefahr>
        </fieldset>
        <fieldset class="p-4 border-2">
          <legend class="text-xl">Prozesse bearbeiten</legend>
          <RisikoProzess
            on:update={onChange}
            visible={true}
            autofocus={false}
            autoscroll={false}
            {allProzessItem}
            {risiko}
          >
            <div slot="cancel">
              <Button type="reset" on:click={onCancel}>Verwerfen</Button>
            </div>
          </RisikoProzess>
        </fieldset>
      {/key}{/if}
  </div>
{/if}
