<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import { toast } from "../components/Toast";
  import { loadOneValue } from "../utils/rest.js";
  import { loadAllValue } from "../utils/rest.js";
  import Button from "../components/Button";
  import AufgabeEditor from "./AufgabeEditor.svelte";

  export let aufgabeId;

  let allNutzerItem = [];
  let allTerminserieItem = [];
  let loading = true;
  onMount(async () => {
    try {
      loading = true;
      allNutzerItem = await loadAllValue("/api/nutzer/search/findAllItem");
      console.log(["onMount", allNutzerItem]);
      allTerminserieItem = await loadAllValue("/api/enum/terminserie");
      console.log(["onMount", allTerminserieItem]);
      await reloadOneAufgabe();
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  async function onChange() {
    try {
      loading = true;
      await tick();
      await reloadOneAufgabe();
    } finally {
      loading = false;
    }
  }
  async function onCancel() {
    await reloadOneAufgabe();
  }

  let aufgabe;
  function reloadOneAufgabe() {
    return loadOneValue("/api/aufgabe/" + aufgabeId)
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadOneAufgabe", msg]);
        aufgabe = json;
      })
      .catch((err) => {
        console.log(["reloadOneAufgabe", err]);
        aufgabe = {};
        toast.push(err.toString());
      });
  }
</script>

<div class="flex flex-col gap-1 ml-2 mr-2">
  {#if aufgabe}{#key aufgabe}
      {@const projektId = aufgabe.projektId}
      <h1>{aufgabe.titel}</h1>
      <fieldset class="p-4 border-2">
        <legend class="text-xl">Aufgabe bearbeiten</legend>
        <AufgabeEditor
          on:update={onChange}
          visible={true}
          autofocus={true}
          autoscroll={false}
          {aufgabe}
          {projektId}
          {allNutzerItem}
          {allTerminserieItem}
        >
          <div slot="cancel">
            <Button type="reset" on:click={onCancel}>Verwerfen</Button>
          </div>
        </AufgabeEditor>
      </fieldset>
    {/key}{/if}
</div>
