package esy.app;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 * @see <a href="https://spring.io/projects/spring-boot">Spring Boot project</a>
 * @see <a href="https://docs.spring.io/spring-boot/docs/current/reference/html/features.html#features.logging">Logging</a>
 * @see <a href="https://www.baeldung.com/spring-http-logging">Log Incoming Requests</a>
 */
@Configuration
@PropertySource(
        ignoreResourceNotFound = false,
        value = "classpath:logging.properties")
public class EsyLoggingConfiguration {

    @Value("${spring.profiles.active:dev}")
    private String profile;

    @Value("${logging.show.client:false}")
    private boolean showClient;

    @Value("${logging.show.headers:false}")
    private boolean showHaeders;

    @Value("${logging.show.payload:false}")
    private boolean showPayload;

    @Bean
    public EsyLoggingRequestFilter requestLoggingFilter() {
        final var filter = new EsyLoggingRequestFilter("dev".equals(profile) || showClient || showHaeders || showPayload);
        filter.setMaxPayloadLength(10000);
        filter.setIncludeClientInfo(showClient);
        filter.setIncludeQueryString(true);
        filter.setIncludeHeaders(showHaeders);
        filter.setIncludePayload(showPayload);
        return filter;
    }
}
