<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import { toast } from "../components/Toast";
  import { loadAllValue } from "../utils/rest.js";
  import { loadOneValue } from "../utils/rest.js";
  import Button from "../components/Button";
  import ProjektEditor from "./ProjektEditor.svelte";
  import ProjektOption from "./ProjektOption.svelte";
  import ProjektQuelle from "./ProjektQuelle.svelte";
  import ProjektTeam from "./ProjektTeam.svelte";

  export let projektId;

  let allNutzerItem = [];
  let allSpracheItem = [];
  let allQuelleItem = [];
  let loading = true;
  onMount(async () => {
    try {
      loading = true;
      allNutzerItem = await loadAllValue("/api/nutzer/search/findAllItem");
      console.log(["onMount", allNutzerItem]);
      allSpracheItem = await loadAllValue("/api/enum/sprache");
      console.log(["onMount", allSpracheItem]);
      allQuelleItem = await loadAllValue("/api/enum/quelle");
      console.log(["onMount", allQuelleItem]);
      reloadOneProjekt();
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  async function onChange() {
    try {
      loading = true;
      await tick();
      await reloadOneProjekt();
    } finally {
      loading = false;
    }
  }
  async function onCancel() {
    await reloadOneProjekt();
  }

  let projekt;
  function reloadOneProjekt() {
    return loadOneValue("/api/projekt/" + projektId)
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadOneProjekt", msg]);
        projekt = json;
      })
      .catch((err) => {
        console.log(["reloadOneProjekt", err]);
        projekt = {};
        toast.push(err.toString());
      });
  }
</script>

<div class="flex flex-col gap-1 ml-2 mr-2">
  {#if projekt}{#key projekt}
      <h1>{projekt.name}</h1>
      <fieldset class="p-4 border-2">
        <legend class="text-xl">Projekt bearbeiten</legend>
        <ProjektEditor
          on:update={onChange}
          visible={true}
          autofocus={true}
          autoscroll={false}
          {projekt}
          {allSpracheItem}
        >
          <div slot="cancel">
            <Button type="reset" onclick={onCancel}>Verwerfen</Button>
          </div>
        </ProjektEditor>
      </fieldset>
      <fieldset class="p-4 border-2">
        <legend class="text-xl">Optionen bearbeiten</legend>
        <ProjektOption
          on:update={onChange}
          visible={true}
          autofocus={false}
          autoscroll={false}
          {projekt}
        >
          <div slot="cancel">
            <Button type="reset" onclick={onCancel}>Verwerfen</Button>
          </div>
        </ProjektOption>
      </fieldset>
      <fieldset class="p-4 border-2">
        <legend class="text-xl">Quellen bearbeiten</legend>
        <ProjektQuelle
          on:update={onChange}
          visible={true}
          autofocus={false}
          autoscroll={false}
          {projekt}
          {allQuelleItem}
        >
          <div slot="cancel">
            <Button type="reset" onclick={onCancel}>Verwerfen</Button>
          </div>
        </ProjektQuelle>
      </fieldset>
      <fieldset class="p-4 border-2">
        <legend class="text-xl">Team bearbeiten</legend>
        <ProjektTeam
          on:update={onChange}
          visible={true}
          autofocus={false}
          autoscroll={false}
          {projekt}
          {allNutzerItem}
        >
          <div slot="cancel">
            <Button type="reset" onclick={onCancel}>Verwerfen</Button>
          </div>
        </ProjektTeam>
      </fieldset>
    {/key}{/if}
</div>
