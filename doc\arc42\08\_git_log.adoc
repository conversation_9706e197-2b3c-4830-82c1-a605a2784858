:relrootdir: ../../..
== Git Log

=== Lesbare Commits

https://www.conventionalcommits.org/de

Typen von Commits::
[width="100%",cols="1,4"]
|===
| Typ | Bedeutung
| fix | Behebt einen Fehler.
| feat | Führt eine neue Funktion ein.
| spike | Führt eine experimentelle und nicht finalisierte Funktion ein.
| build | Verbessert nur das Build-System.
| doc | Verbessert nur die Dokumentation.
| refactor | Verbessert die Struktur durch ein _Refactoring_.
| renovate | Enthält automatische Änderungen durch `renovate`.
|===

 Ein Commit mit einem angehängten `!` nach dem Typ enthält nicht-kompatible Änderungen (engl. _breaking change_) an der Codebasis.

=== Commit-Historie bereinigen

==== Rebase mit `reword`

Der Aufruf von

[source, git, subs=attributes+]
----
git log --oneline
----

zeigt die aktuelle _commit history_.

.Beispiel
----
$ git log --oneline
b3cb07bc (HEAD -> master, origin/master, origin/HEAD) PETCLINIC-24: #findAllByOrderByNameAsc durch Querydsl ersetzt
254013f9 PETCLINIC-30: Owner mit Kontaktdaten
184100db PETCLINIC-4: Version 1.7
4302c67b PETCLINIC-24: SecurityRole -> JwtRole
cbcb8bee PETCLINIC-13: SPECIES
bcd95d82 PETCLINIC-12: SKILLS
93a5d93a Version 1.6
d7d4d810 PETCLINIC-4: Version 1.5
----

Die _commit message_ für Version entspricht nicht der Regelung im Projekt.
Es fehlt der Bezug zu `PETCLINIC-4`.

Der Aufruf von

[source, git, subs=attributes+]
----
git rebase -i
----

startet einen interaktiven _Rebase_.

.Beispiel
----
$ git rebase -i d7d4d810
pick b3cb07bc PETCLINIC-24: #findAllByOrderByNameAsc durch Querydsl ersetzt
pick 254013f9 PETCLINIC-30: Owner mit Kontaktdaten
pick 184100db PETCLINIC-4: Version 1.7
pick 4302c67b PETCLINIC-24: SecurityRole -> JwtRole
pick cbcb8bee PETCLINIC-13: SPECIES
pick bcd95d82 PETCLINIC-12: SKILLS
reword 93a5d93a Version 1.6
pick d7d4d810 PETCLINIC-4: Version 1.5
----

Die _commit message_ für Version 1.6 wird korrigiert.

Der Aufruf von

[source, git, subs=attributes+]
----
git log --oneline
----

zeigt die aktuelle _commit history_.

.Beispiel
----
$ git log --oneline
b3cb07bc (HEAD -> master, origin/master, origin/HEAD) PETCLINIC-24: #findAllByOrderByNameAsc durch Querydsl ersetzt
254013f9 PETCLINIC-30: Owner mit Kontaktdaten
184100db PETCLINIC-4: Version 1.7
4302c67b PETCLINIC-24: SecurityRole -> JwtRole
cbcb8bee PETCLINIC-13: SPECIES
bcd95d82 PETCLINIC-12: SKILLS
93a5d93a PETCLINIC-4: Version 1.6
d7d4d810 PETCLINIC-4: Version 1.5
----

==== Rebase mit `fixup`

Der Aufruf von

[source, git, subs=attributes+]
----
git log --oneline
----

zeigt die aktuelle _commit history_ in einem kompakten und gut lesbaren Format.

.Beispiel
----
$ git log --oneline
b3cb07bc (HEAD -> master, origin/master, origin/HEAD) PETCLINIC-24: #findAllByOrderByNameAsc durch Querydsl ersetzt
254013f9 PETCLINIC-30: Owner mit Kontaktdaten
184100db PETCLINIC-4: Version 1.7
4302c67b PETCLINIC-24: SecurityRole -> JwtRole
cbcb8bee PETCLINIC-13: SPECIES
bcd95d82 PETCLINIC-12: SKILLS
93a5d93a PETCLINIC-4: Version 1.6
d7d4d810 PETCLINIC-4: Version 1.5
----

Der Aufruf von

[source, git, subs=attributes+]
----
git rebase -i
----

startet einen interaktiven _Rebase_.

Ausgehend von der aktuellen _commit history_ soll die Version 1.6 bereinigt werden.
Der _commit_ "Version 1.6" enthält ausschließlich die Änderungen für die Version in den Dateien `package.json`, `Chart.yml`, `VERSION` und `VERSION.md`.
Alle weiteren angezeigten _commits_ enhalten die durchgeführten fachlichen und technischen Änderungen.
Ziel ist es, am Ende nur mehr ein _commit_ für die Version 1.6 zu haben.

.Beispiel
----
$ git rebase -i d7d4d810
pick b3cb07bc PETCLINIC-24: #findAllByOrderByNameAsc durch Querydsl ersetzt
pick 254013f9 PETCLINIC-30: Owner mit Kontaktdaten
pick 184100db PETCLINIC-4: Version 1.7
fixup 4302c67b PETCLINIC-24: SecurityRole -> JwtRole
fixup cbcb8bee PETCLINIC-13: SPECIES
fixup bcd95d82 PETCLINIC-12: SKILLS
pick 93a5d93a PETCLINIC-4: Version 1.6
pick d7d4d810 PETCLINIC-4: Version 1.5
----

Alle Änderungen für die Version 1.6 wird im ersten mit `pick` markierten _commit_ nach den mit `fixup` markierten _commits_ zusammengeführt.

Der _rebase_ stoppt, wenn es Konflikte gibt.
Konflikte müssen gelöst werden.

Der Aufruf von

[source, git, subs=attributes+]
----
git rebase --continue
----

setzt den _rebase_ fort bzw. beendet ihn.

.Beispiel
----
$ git log --oneline
b3cb07bc (HEAD -> master, origin/master, origin/HEAD) PETCLINIC-24: #findAllByOrderByNameAsc durch Querydsl ersetzt
254013f9 PETCLINIC-30: Owner mit Kontaktdaten
184100db PETCLINIC-4: Version 1.7
93a5d93a PETCLINIC-4: Version 1.6
d7d4d810 PETCLINIC-4: Version 1.5
----
==== Rebase mit `edit`

Der Aufruf von

[source, git, subs=attributes+]
----
git log --oneline
----

zeigt die aktuelle _commit history_.

.Beispiel
----
$ git log --oneline
b3cb07bc (HEAD -> master, origin/master, origin/HEAD) PETCLINIC-24: #findAllByOrderByNameAsc durch Querydsl ersetzt
254013f9 PETCLINIC-30: Owner mit Kontaktdaten
184100db PETCLINIC-4: Version 1.7
93a5d93a PETCLINIC-4: Version 1.6
d7d4d810 PETCLINIC-4: Version 1.5
----

In diesem Beispiel hat die Version 1.6 Fehler, die durch ein vorangegangenes _rebase_ entstanden sind.

Der Aufruf von

[source, git, subs=attributes+]
----
git rebase -i
----

startet einen interaktiven _Rebase_.

.Beispiel
----
$ git rebase -i d7d4d810
pick b3cb07bc PETCLINIC-24: #findAllByOrderByNameAsc durch Querydsl ersetzt
pick 254013f9 PETCLINIC-30: Owner mit Kontaktdaten
pick 184100db PETCLINIC-4: Version 1.7
edit 93a5d93a PETCLINIC-4: Version 1.6
pick d7d4d810 PETCLINIC-4: Version 1.5
----

Der _rebase_ stoppt direkt nach dem _commit_ für die Version 1.6.
Die Fehler können jetzt korrgiert werden.

Der Aufruf von

[source, git, subs=attributes+]
----
git add .
git commit --amend
----

übernimmt die Änderungen in den letzten _commit_.
Das ist der _commit_ für die Version 1.6.

Der Aufruf von

[source, git, subs=attributes+]
----
git rebase --continue
----

setzt den _rebase_ fort bzw. beendet ihn.
