<script lang="ts">
  import { zip } from "../../utils/rest.js";
  import { toast } from "../Toast";
  let {
    checked = $bindable(false),
    clicked = $bindable(0),
    disabled = false,
    name = "download",
    job,
    outlined = false,
    title = undefined,
    onclick = undefined,
    onsuccess = undefined,
    onfailure = undefined,
    ...elementProps
  } = $props();

  let element;
  export function focus() {
    element?.focus();
  }

  function handleClick(_event: MouseEvent) {
    checked = !checked;
    clicked++;
    const toastId = toast.push(title, { duration: 60000 });
    const json = job instanceof Function ? job() : job;
    onclick?.(json);
    zip(json)
      .then((blob) => {
        console.log(["zip", json]);
        toast.pop(toastId);
        let saveAs;
        saveAs.href = URL.createObjectURL(blob);
        saveAs.download = json.archive;
        saveAs.click();
        saveAs.href = URL.revokeObjectURL(saveAs.href);
        saveAs.download = undefined;
        onsuccess?.(json);
      })
      .catch((err) => {
        console.log(["zip", json, err]);
        toast.pop(toastId);
        toast.push(err.toString());
        onfailure?.(json);
      });
  }
</script>

<button
  type="button"
  aria-label={name}
  bind:this={element}
  {...elementProps}
  {title}
  {disabled}
  class:disabled
  class="text-xl text-white w-12 h-12 rounded-full p-2 disabled:opacity-50 hover:opacity-90 focus:ring bg-primary-500"
  class:outlined
  onclick={handleClick}
>
  <div class="flex justify-center items-center">
    <i
      {title}
      aria-hidden="true"
      class="material-icons icon text-xl select-none"
    >
      {name}
    </i>
  </div>
</button>

<!-- svelte-ignore a11y-missing-attribute -->
<a aria-label="Zip" class="hidden" bind:this={saveAs}>&nbsp;</a>
