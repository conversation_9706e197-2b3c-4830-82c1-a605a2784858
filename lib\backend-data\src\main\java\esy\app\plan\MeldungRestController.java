package esy.app.plan;

import esy.api.plan.Meldung;
import esy.api.plan.VorgangStatus;
import esy.auth.JwtRole;
import esy.rest.JsonJpaRestControllerBase;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.rest.core.annotation.RepositoryEventHandler;
import org.springframework.data.rest.webmvc.BasePathAwareController;
import org.springframework.hateoas.EntityModel;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.UUID;

@RepositoryEventHandler
@BasePathAwareController
public class MeldungRestController extends JsonJpaRestControllerBase<Meldung, UUID> {

    private final MeldungRepository meldungRepository;

    @Autowired
    public MeldungRestController(
            @NonNull final ApplicationEventPublisher eventPublisher,
            @NonNull final TransactionTemplate transactionTemplate,
            @NonNull final MeldungRepository meldungRepository) {
        super(eventPublisher, transactionTemplate);
        this.meldungRepository = meldungRepository;
    }

    // tag::allowDelete[]
    @Override
    protected boolean allowDelete(@NonNull final Meldung value, @NonNull Authentication auth) {
        return hasRole(auth, JwtRole.VERWALTUNG);
    }
    // end::allowDelete[]

    Meldung updateStatusTransition(@NonNull final Meldung value, @NonNull final VorgangStatus status) {
        return updateInTransaction(value, tx -> {
            value.applyStatusTransition(status);
            return meldungRepository.save(value);
        });
    }

    // tag::patchMeldungStatusTransition[]
    @PatchMapping("/meldung/{id}/{status}")
    public ResponseEntity<EntityModel<Meldung>> patchRisikoStatusTransition(@PathVariable("id") final UUID id, @PathVariable("status") final VorgangStatus statusNew) {
        final var valueOld = meldungRepository.getReferenceById(id);
        final var valueNew = updateStatusTransition(valueOld, statusNew);
        return ResponseEntity
                .status(HttpStatus.OK)
                .eTag(Long.toString(valueNew.getVersion()))
                .body(EntityModel.of(valueNew));
    }
    // end::patchMeldungStatusTransition[]
}
