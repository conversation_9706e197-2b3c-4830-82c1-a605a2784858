package esy.app.wiki;

import esy.api.info.Land;
import esy.api.info.Sprache;
import esy.api.plan.VorgangStatus;
import esy.api.wiki.Seite;
import esy.app.EsyBackendConfiguration;
import esy.app.team.NutzerRepository;
import esy.auth.JwtRole;
import esy.auth.WithMockJwt;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.restdocs.RestDocumentationContextProvider;
import org.springframework.restdocs.RestDocumentationExtension;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import java.nio.charset.StandardCharsets;
import java.util.UUID;

import static esy.app.RestApiAssertions.assertRestApiSpecExists;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.restdocs.mockmvc.MockMvcRestDocumentation.document;
import static org.springframework.restdocs.mockmvc.MockMvcRestDocumentation.documentationConfiguration;
import static org.springframework.restdocs.operation.preprocess.Preprocessors.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@Tag("slow")
@SpringBootTest
@AutoConfigureMockMvc(addFilters = false)
@ContextConfiguration(classes = {EsyBackendConfiguration.class})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith({MockitoExtension.class, RestDocumentationExtension.class})
class SeiteRestApiTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private OrdnerRepository ordnerRepository;

    @Autowired
    private NutzerRepository nutzerRepository;

    @Autowired
    private SeiteRepository seiteRepository;

    @BeforeEach
    void setUp(final WebApplicationContext webApplicationContext,
               final RestDocumentationContextProvider restDocumentation) {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext)
                .apply(documentationConfiguration(restDocumentation))
                .alwaysDo(document("{method-name}",
                        preprocessRequest(prettyPrint()),
                        preprocessResponse(prettyPrint())))
                .build();
    }

    @Test
    @Order(1)
    void asciidoc() {
        assertRestApiSpecExists(Seite.class);
    }

    @ParameterizedTest
    @ValueSource(strings = {"GET", "POST", "PUT", "PATCH", "DELETE"})
    @Order(1)
    void preflight(final String method) throws Exception {
        mockMvc.perform(options("/api/seite")
                        .header("Access-Control-Request-Method", method)
                        .header("Access-Control-Request-Headers", "Content-Type")
                        .header("Origin", "http://localhost:5000")) // UI
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(header()
                        .exists("Access-Control-Allow-Origin"))
                .andExpect(header()
                        .exists("Access-Control-Allow-Methods"))
                .andExpect(header()
                        .exists("Access-Control-Allow-Headers"));
    }

    @Test
    @Order(2)
    void getApiEnumDateityp() throws Exception {
        mockMvc.perform(get("/api/enum/dateityp")
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(jsonPath("$.content")
                        .isArray())
                .andExpect(jsonPath("$.content[0].value")
                        .value("ADOC"))
                .andExpect(jsonPath("$.content[1]")
                        .doesNotExist());
    }

    @Sql("/sql/nutzer.sql")
    @Sql("/sql/ordner.sql")
    @Test
    @Order(10)
    @WithMockJwt
    void getApiSeiteNoElement() throws Exception {
        mockMvc.perform(get("/api/seite")
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(jsonPath("$.content")
                        .isArray())
                .andExpect(jsonPath("$.content[0]")
                        .doesNotExist());
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "README",
            "HELPME"
    })
    @Order(20)
    @WithMockJwt
    void postApiSeite(final String titel) throws Exception {
        mockMvc.perform(post("/api/seite")
                        .content("""
                                {
                                    "ordnerId":"a1111111-b12a-4bd2-4794-ef84794bd27a",
                                    "uri":"%s.adoc",
                                    "titel":"%s",
                                    "sprache":"DE",
                                    "allOption":["DE","AT"],
                                    "aktiv":"false"
                                }
                                """.formatted(titel, titel))
                        .contentType(MediaType.APPLICATION_JSON)
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isCreated())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .string("ETag", "\"1\""))
                .andExpect(jsonPath("$.id")
                        .isNotEmpty())
                .andExpect(jsonPath("$.ordnerId")
                        .isNotEmpty())
                .andExpect(jsonPath("$.uri")
                        .isNotEmpty())
                .andExpect(jsonPath("$.titel")
                        .value(titel))
                .andExpect(jsonPath("$.sprache")
                        .value(Sprache.DE.name()))
                .andExpect(jsonPath("$.allOption")
                        .isArray())
                .andExpect(jsonPath("$.allOption[0]")
                        .value(Land.AT.name()))
                .andExpect(jsonPath("$.allOption[1]")
                        .value(Land.DE.name()))
                .andExpect(jsonPath("$.allOption[2]")
                        .doesNotExist())
                .andExpect(jsonPath("$.aktiv")
                        .value("false"));
    }

    @Test
    @Order(30)
    @WithMockJwt
    void putApiSeiteCreate() throws Exception {
        final var titel = "INSTALL";
        final var uuid = UUID.fromString("a3333333-8479-bd27-8479-ef84794bd27a");
        mockMvc.perform(put("/api/seite/" + uuid)
                        .content("""
                                {
                                    "ordnerId":"a1111111-b12a-4bd2-4794-ef84794bd27a",
                                    "uri":"%s.adoc",
                                    "titel":"%s",
                                    "sprache":"DE",
                                    "allOption":["DE","AT"],
                                    "aktiv":"false"
                                }
                                """.formatted(titel, titel))
                        .contentType(MediaType.APPLICATION_JSON)
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isCreated())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .string("ETag", "\"1\""))
                .andExpect(jsonPath("$.id")
                        .isNotEmpty())
                .andExpect(jsonPath("$.ordnerId")
                        .isNotEmpty())
                .andExpect(jsonPath("$.uri")
                        .isNotEmpty())
                .andExpect(jsonPath("$.titel")
                        .value(titel))
                .andExpect(jsonPath("$.sprache")
                        .value(Sprache.DE.name()))
                .andExpect(jsonPath("$.allOption")
                        .isArray())
                .andExpect(jsonPath("$.allOption[0]")
                        .value(Land.AT.name()))
                .andExpect(jsonPath("$.allOption[1]")
                        .value(Land.DE.name()))
                .andExpect(jsonPath("$.allOption[2]")
                        .doesNotExist())
                .andExpect(jsonPath("$.aktiv")
                        .value("false"));
    }

    @Test
    @Order(31)
    @WithMockJwt
    void putApiSeiteUpdate() throws Exception {
        final var titel = "INSTALL";
        final var uuid = UUID.fromString("a3333333-8479-bd27-8479-ef84794bd27a");
        final var seite = seiteRepository.findById(uuid).orElseThrow();
        mockMvc.perform(put("/api/seite/" + seite.getId())
                        .content("""
                                {
                                    "ordnerId":"a1111111-b12a-4bd2-4794-ef84794bd27a",
                                    "uri":"%s.adoc",
                                    "titel":"%s",
                                    "sprache":"DE",
                                    "allOption":["DE","AT"]
                                }
                                """.formatted(titel, titel))
                        .contentType(MediaType.APPLICATION_JSON)
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + seite.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(seite.getId().toString()))
                .andExpect(jsonPath("$.ordnerId")
                        .value(seite.getOrdnerId().toString()))
                .andExpect(jsonPath("$.uri")
                        .value(seite.getUri()))
                .andExpect(jsonPath("$.titel")
                        .value(titel))
                .andExpect(jsonPath("$.sprache")
                        .value(Sprache.DE.name()))
                .andExpect(jsonPath("$.allOption")
                        .isArray())
                .andExpect(jsonPath("$.allOption[0]")
                        .value(Land.AT.name()))
                .andExpect(jsonPath("$.allOption[1]")
                        .value(Land.DE.name()))
                .andExpect(jsonPath("$.allOption[2]")
                        .doesNotExist())
                .andExpect(jsonPath("$.aktiv")
                        .value("true"));
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "a2222222-b12a-4bd2-4794-ef84794bd27a",
            "a1111111-b12a-4bd2-4794-ef84794bd27a"
    })
    @Order(33)
    @WithMockJwt
    void patchApiSeiteOrdner(final String ordnerId) throws Exception {
        final var uuid = UUID.fromString("a3333333-8479-bd27-8479-ef84794bd27a");
        final var seite = seiteRepository.findById(uuid).orElseThrow();
        mockMvc.perform(patch("/api/seite/" + seite.getId())
                        .content("{\"ordnerId\":\"%s\"}".formatted(ordnerId))
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(seite.getId().toString()))
                .andExpect(jsonPath("$.ordnerId")
                        .value(ordnerId));
    }

    @Test
    @Order(34)
    @WithMockJwt
    void patchApiSeiteOrdnerNull() throws Exception {
        final var uuid = UUID.fromString("a3333333-8479-bd27-8479-ef84794bd27a");
        final var seite = seiteRepository.findById(uuid).orElseThrow();
        mockMvc.perform(patch("/api/seite/" + seite.getId())
                        .content("{\"ordnerId\":null}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isConflict());
    }

    @Test
    @Order(35)
    @WithMockJwt
    void patchApiSeiteNutzer() throws Exception {
        final var uuid = UUID.fromString("a3333333-8479-bd27-8479-ef84794bd27a");
        final var seite = seiteRepository.findById(uuid).orElseThrow();
        mockMvc.perform(patch("/api/seite/" + seite.getId())
                        .content("{\"nutzerId\":\"a2222222-6ee8-4335-b12a-ef84794bd27a\"}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + seite.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(seite.getId().toString()))
                .andExpect(jsonPath("$.nutzerId")
                        .exists());
    }

    @Test
    @Order(36)
    @WithMockJwt
    void patchApiSeiteNutzerNull() throws Exception {
        final var uuid = UUID.fromString("a3333333-8479-bd27-8479-ef84794bd27a");
        final var seite = seiteRepository.findById(uuid).orElseThrow();
        mockMvc.perform(patch("/api/seite/" + seite.getId())
                        .content("{\"nutzerId\":null}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + seite.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(seite.getId().toString()))
                .andExpect(jsonPath("$.nutzerId")
                        .doesNotExist());
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "doc/INSTALL.adoc",
            "INSTALL.adoc"
    })
    @Order(40)
    @WithMockJwt
    void patchApiSeiteUri(final String uri) throws Exception {
        final var uuid = UUID.fromString("a3333333-8479-bd27-8479-ef84794bd27a");
        final var seite = seiteRepository.findById(uuid).orElseThrow();
        mockMvc.perform(patch("/api/seite/" + seite.getId())
                        .content("{\"uri\":\"%s\"}".formatted(uri))
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + seite.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(seite.getId().toString()))
                .andExpect(jsonPath("$.uri")
                        .value(uri));
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "LLATSNI",
            "INSTALL"
    })
    @Order(41)
    @WithMockJwt
    void patchApiSeiteTitel(final String titel) throws Exception {
        final var uuid = UUID.fromString("a3333333-8479-bd27-8479-ef84794bd27a");
        final var seite = seiteRepository.findById(uuid).orElseThrow();
        mockMvc.perform(patch("/api/seite/" + seite.getId())
                        .content("{\"titel\":\"%s\"}".formatted(titel))
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + seite.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(seite.getId().toString()))
                .andExpect(jsonPath("$.titel")
                        .value(titel));
    }

    @Test
    @Order(42)
    @WithMockJwt
    void patchApiSeiteText() throws Exception {
        final var uuid = UUID.fromString("a3333333-8479-bd27-8479-ef84794bd27a");
        final var seite = seiteRepository.findById(uuid).orElseThrow();
        mockMvc.perform(patch("/api/seite/" + seite.getId())
                        .content("{\"text\":\"Lorem ipsum dolor sit amet.\"}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + seite.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(seite.getId().toString()))
                .andExpect(jsonPath("$.text")
                        .isNotEmpty());
    }

    @ParameterizedTest
    @ValueSource(strings = {"EN", "DE"})
    @Order(43)
    @WithMockJwt
    void patchApiSeiteSprache(final String sprache) throws Exception {
        assertDoesNotThrow(() -> Sprache.fromString(sprache));
        final var uuid = UUID.fromString("a3333333-8479-bd27-8479-ef84794bd27a");
        final var seite = seiteRepository.findById(uuid).orElseThrow();
        mockMvc.perform(patch("/api/seite/" + seite.getId())
                        .content("{\"sprache\":\"%s\"}".formatted(sprache))
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + seite.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(seite.getId().toString()))
                .andExpect(jsonPath("$.sprache")
                        .value(sprache));
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "2019-04-22",
            "2031-04-22",
            "2000-01-01"
    })
    @Order(43)
    @WithMockJwt
    void patchApiSeiteTermin(final String datum) throws Exception {
        final var uuid = UUID.fromString("a3333333-8479-bd27-8479-ef84794bd27a");
        final var seite = seiteRepository.findById(uuid).orElseThrow();
        mockMvc.perform(patch("/api/seite/" + seite.getId())
                        .content("{\"termin\":\"%s\"}".formatted(datum))
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + seite.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(seite.getId().toString()))
                .andExpect(jsonPath("$.termin")
                        .value(datum));
    }

    @Test
    @Order(45)
    @WithMockJwt
    void patchApiSeiteOption() throws Exception {
        final var uuid = UUID.fromString("a3333333-8479-bd27-8479-ef84794bd27a");
        final var seite = seiteRepository.findById(uuid).orElseThrow();
        mockMvc.perform(patch("/api/seite/" + seite.getId())
                        .content("{\"allOption\":[\"%s\"]}".formatted(Land.AT))
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + seite.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(seite.getId().toString()))
                .andExpect(jsonPath("$.allOption")
                        .isArray())
                .andExpect(jsonPath("$.allOption[0]")
                        .value(Land.AT.name()))
                .andExpect(jsonPath("$.allOption[1]")
                        .doesNotExist());
    }

    @Test
    @Order(46)
    @WithMockJwt
    void patchApiSeiteOptionEmpty() throws Exception {
        final var uuid = UUID.fromString("a3333333-8479-bd27-8479-ef84794bd27a");
        final var seite = seiteRepository.findById(uuid).orElseThrow();
        mockMvc.perform(patch("/api/seite/" + seite.getId())
                        .content("{\"allOption\":[]}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + seite.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(seite.getId().toString()))
                .andExpect(jsonPath("$.allOption")
                        .isArray())
                .andExpect(jsonPath("$.allOption[0]")
                        .doesNotExist());
    }

    @ParameterizedTest
    @ValueSource(booleans = {false, true})
    @Order(47)
    @WithMockJwt
    void patchApiSeiteAktiv(final boolean aktiv) throws Exception {
        final var uuid = UUID.fromString("a3333333-8479-bd27-8479-ef84794bd27a");
        final var seite = seiteRepository.findById(uuid).orElseThrow();
        mockMvc.perform(patch("/api/seite/" + seite.getId())
                        .content("{\"aktiv\":\"%s\"}".formatted(aktiv))
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + seite.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(seite.getId().toString()))
                .andExpect(jsonPath("$.aktiv")
                        .value(aktiv));
    }

    @Test
    @Order(48)
    @WithMockJwt
    void patchApiSeiteStatusTransition() throws Exception {
        final var uuid = UUID.fromString("a3333333-8479-bd27-8479-ef84794bd27a");
        final var seite = seiteRepository.findById(uuid).orElseThrow();
        assertNotEquals(VorgangStatus.X, seite.getStatus());
        mockMvc.perform(patch("/api/seite/" + seite.getId() + "/" + VorgangStatus.X)
                        .contentType(MediaType.parseMediaType("plain/text"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + seite.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(seite.getId().toString()))
                .andExpect(jsonPath("$.aktiv")
                        .value("false"))
                .andExpect(jsonPath("$.termin")
                        .exists());
    }

    @Test
    @Order(50)
    @WithMockJwt
    void getApiSeiteById() throws Exception {
        final var uuid = UUID.fromString("a3333333-8479-bd27-8479-ef84794bd27a");
        final var seite = seiteRepository.findById(uuid).orElseThrow();
        mockMvc.perform(get("/api/seite/" + seite.getId())
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .string("ETag", "\"21\""))
                .andExpect(jsonPath("$.id")
                        .value(seite.getId().toString()))
                .andExpect(jsonPath("$.ordnerId")
                        .value(seite.getOrdnerId().toString()))
                .andExpect(jsonPath("$.uri")
                        .value(seite.getUri()))
                .andExpect(jsonPath("$.titel")
                        .value(seite.getTitel()))
                .andExpect(jsonPath("$.sprache")
                        .value(seite.getSprache()))
                .andExpect(jsonPath("$.allOption")
                        .isArray())
                .andExpect(jsonPath("$.allOption[0]")
                        .doesNotExist())
                .andExpect(jsonPath("$.aktiv")
                        .value(seite.isAktiv()));
    }

    @Test
    @Order(51)
    @WithMockJwt
    void getApiSeiteByIdNotFound() throws Exception {
        mockMvc.perform(get("/api/seite/" + UUID.randomUUID())
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isNotFound());
    }

    @Test
    @Order(52)
    @WithMockJwt
    void getApiSeiteByOrdnerIdAndUri() throws Exception {
        final var uuid = UUID.fromString("a3333333-8479-bd27-8479-ef84794bd27a");
        final var seite = seiteRepository.findById(uuid).orElseThrow();
        mockMvc.perform(get("/api/seite/search/findByOrdnerIdAndUri")
                        .queryParam("ordnerId", seite.getOrdnerId().toString())
                        .queryParam("uri", seite.getUri())
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(seite.getId().toString()))
                .andExpect(jsonPath("$.ordnerId")
                        .value(seite.getOrdnerId().toString()))
                .andExpect(jsonPath("$.uri")
                        .value(seite.getUri()));
    }

    @Test
    @Order(53)
    @WithMockJwt
    void getApiSeiteByOrdnerIdAndUriNotFound() throws Exception {
        final var uuid = UUID.fromString("a3333333-8479-bd27-8479-ef84794bd27a");
        final var seite = seiteRepository.findById(uuid).orElseThrow();
        mockMvc.perform(get("/api/seite/search/findByOrdnerIdAndUri")
                        .queryParam("ordnerId", UUID.randomUUID().toString())
                        .queryParam("uri", seite.getUri())
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isNotFound());
    }

    @Test
    @Order(54)
    @WithMockJwt
    void getApiSeiteByIdRelOrdner() throws Exception {
        final var uuid = UUID.fromString("a3333333-8479-bd27-8479-ef84794bd27a");
        final var seite = seiteRepository.findById(uuid).orElseThrow();
        mockMvc.perform(get("/api/seite/" + seite.getId() + "/ordner")
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(jsonPath("$.id")
                        .value(seite.getOrdnerId().toString()));
    }

    @Test
    @Order(60)
    @WithMockJwt
    void deleteApiSeiteForbidden() throws Exception {
        final var uuid = UUID.fromString("a3333333-8479-bd27-8479-ef84794bd27a");
        final var seite = seiteRepository.findById(uuid).orElseThrow();
        mockMvc.perform(delete("/api/seite/" + seite.getId())
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isForbidden());
    }

    @Test
    @Order(61)
    @WithMockJwt(roles = {JwtRole.VERWALTUNG})
    void deleteApiSeite() throws Exception {
        final var uuid = UUID.fromString("a3333333-8479-bd27-8479-ef84794bd27a");
        final var seite = seiteRepository.findById(uuid).orElseThrow();
        mockMvc.perform(delete("/api/seite/" + seite.getId())
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .doesNotExist("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(seite.getId().toString()));
    }

    @Test
    @Order(62)
    @WithMockJwt(roles = {JwtRole.VERWALTUNG})
    void deleteApiSeiteNotFound() throws Exception {
        mockMvc.perform(delete("/api/seite/" + UUID.randomUUID())
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isNotFound());
    }

    @Test
    @Order(90)
    @WithMockJwt
    void getApiSeite() throws Exception {
        assertEquals(2, seiteRepository.count());
        mockMvc.perform(get("/api/seite")
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(jsonPath("$.content")
                        .isArray())
                .andExpect(jsonPath("$.content[0]")
                        .exists())
                .andExpect(jsonPath("$.content[1]")
                        .exists())
                .andExpect(jsonPath("$.content[2]")
                        .doesNotExist());
    }

    @Test
    @Order(99)
    @Transactional
    @Rollback(false)
    void cleanup() {
        ordnerRepository.findAll().forEach(ordnerRepository::delete);
        nutzerRepository.findAll().forEach(nutzerRepository::delete);
        assertEquals(0, ordnerRepository.count());
        assertEquals(0, seiteRepository.count());
        assertEquals(0, nutzerRepository.count());
    }
}
