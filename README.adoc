= README

include::settings.adoc[]

== Anwendungen

[[_app_client-svelte]]
=== xref:app/client-svelte/HELPME.adoc[app/client-svelte]

Der browser-basierte Client ist zuständig für die Anzeige und Bearbeitung der Daten und den Abruf von Schriftstücken und Berichten.

Der Client wird mit

[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew :app:client-svelte:buildImage
----

als Container-Image gebaut.

Der Client wird mit

[source, bash, subs=attributes+, options="nowrap"]
----
$ npm --prefix app/client-svelte run dev
----

gestartet.
Er verbindet sich mit einem lokalen Server.
Er ist unter `localhost:5000` erreichbar.
Das Programm muss mit `Ctrl-C` beendet werden.

[[_app_adapter]]
=== xref:app/adapter/HELPME.adoc[app/adapter]

Der Client ist zuständig für die Ausführung von einmaligen und regelmäßig wiederholten Aufgaben.

Der Client wird mit

[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew :app:adapter:buildImage
----

als Container-Image gebaut.

Der Client wird mit

[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew :app:adapter:nodeRun
----

gestartet.
Er führt alle Tasks auf einem lokalen Server aus.
Das Programm endet automatisch.

[[_app_spooler]]
=== xref:app/spooler/HELPME.adoc[app/spooler]

Der Server ist zuständig für das Versenden von E-Mails und das Drucken von Dokumenten.

Der Server wird mit

[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew :app:spooler:buildImage
----

als Container-Image gebaut.

Der Server wird mit

[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew :app:spooler:nodeRun
----

gestartet.
Er ist unter `localhost:8082` erreichbar.
Das Programm muss mit `Ctrl-C` beendet werden.

[[_app_printer]]
=== xref:app/printer/HELPME.adoc[app/printer]

Der Server ist zuständig für die Erstellung von Schriftstücken und Berichten und die Bereitstellung von Dateien in verschiedenen Formaten zum Download.

Der Server wird mit

[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew :app:printer:buildImage
----

als Container-Image gebaut.

Der Server wird im Vordergrund mit

[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew :app:printer:bootRun
----

gestartet.
Er ist unter `localhost:8081` erreichbar.
Er muss mit `Ctrl-C` beendet werden.

[[_app_backend]]
=== xref:app/backend/HELPME.adoc[app/backend]

Der Server realisiert das Backend mit dem REST- und GraphQL-API für den Zugriff auf die relationale Datenbank.

Der Server wird mit 

[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew :app:backend:buildImage
----

als Container-Image gebaut.

Mit der Umgebungsvariable `LOGGING_LEVEL_ORG.HIBERNATE_ORM_JDBC_BIND` kann die Ausgabe von Typinformationen gesteuert werden.
Mit dem Wert `TRACE` werden die aktuellen Parameter von Statements ausgegeben.

Mit der Umgebungsvariable `LOGGING_LEVEL_ORG_HIBERNATE_STAT` kann die Ausgabe von Statistiken gesteuert werden.
Mit dem Wert `DEBUG` werden die Kennzahlen von Statements ausgegeben.

Mit der Umgebungsvariable `GITHUB_TOKEN` wird der Zugriff auf das <<_github_api>> von
https://github.com/[Github]
ermöglicht.

Mit der Umgebungsvariable `GITLAB_TOKEN` wird der Zugriff auf das <<_gitlab_api>> von
https://gitlab.com[Gitlab]
ermöglicht.

Mit der Umgebungsvariable `JIRA_TOKEN` wird der Zugriff auf das <<_jira_api>> von
https://www.atlassian.com/software/jira[Jira]
ermöglicht.

Der Server wird im Vordergrund mit

[source, bash, subs=attributes+, options="nowrap"]
----
$ export SPRING_PROFILES_ACTIVE=hsql
$ ./gradlew :app:backend:bootRun
----

gestartet.
Er nutzt eine _In-Memory_-Datenbank.
Er ist unter `localhost:8080` erreichbar.
Der Server muss mit `Ctrl-C` beendet werden.

Der Server mit Login via _Github Oauth App_ wird im Vordergrund mit

[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew :app:backend:bootRun --args='--spring.profiles.active=github'
----

gestartet.
Er ist unter `localhost:8080` erreichbar.
Der Server muss mit `Ctrl-C` beendet werden.

== Bibliotheken

[[_lib_backend-api]]
=== xref:lib/backend-api/HELPME.adoc[lib/backend-api]

Die Bibliothek enthält die Implementierung für das Datenmodell des REST-APIs.

[[_lib_backend-data]]
=== xref:lib/backend-data/HELPME.adoc[lib/backend-data]

Die Bibliothek enthält die Implementierung der Grundfunktionen für Sicherheit und Speicherung von Daten in einer Datenbank.

[[_lib_backend-test]]
=== xref:lib/backend-test/HELPME.adoc[lib/backend-test]

Die Bibliothek enthält die Implementierung für den Test des `backend`-Servers.

Die Bibliothek bestimmt durch die `api`-Abhängigkeit die einsetzbaren Testwerkzeuge.

[[_lib_printer-base]]
=== xref:poi/printer-base/HELPME.adoc[lib/printer-base]

Die Bibliothek enthält die Implementierung der Grundfunktionen für Sicherheit und Speicherung von Dateien für den Download. 

[[_lib_printer-adoc]]
=== xref:poi/printer-adoc/HELPME.adoc[lib/printer-adoc]

Die Bibliothek enthält die Implementierung für Schriftstücke auf Basis von Asciidoc und Konvertierung in die Formate `ADOC`, `HTML` und `PDF`. 

[[_lib_printer-git]]
=== xref:poi/printer-git/HELPME.adoc[lib/printer-git]

Die Bibliothek enthält die Implementierung für Schriftstücke aus einem `git`-Repository. 

[[_lib_printer-pdf]]
=== xref:poi/printer-pdf/HELPME.adoc[lib/printer-pdf]

Die Bibliothek enthält die Implementierung für Schriftstücke auf Basis von `PDF`-Vorlagen, die mit Daten gefüllt werden. 

[[_lib_printer-poi]]
=== xref:poi/printer-poi/HELPME.adoc[lib/printer-poi]

Die Bibliothek enthält die Implementierung für Schriftstücke auf Basis von Vorlagen, die mit Daten gefüllt werden.

[[_lib_printer-test]]
=== xref:lib/printer-test/HELPME.adoc[lib/printer-test]

Die Bibliothek enthält die Implementierung für den Test des `printer`-Servers.

Die Bibliothek bestimmt durch die `api`-Abhängigkeit die einsetzbaren Testwerkzeuge.

== Entwicklung

=== Code formatieren

[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew format
----

=== Code bauen und testen

[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew clean
$ ./gradlew build
----

Der gesamte Code wird frisch kompiliert, alle Artefakte gebaut und alle Unit-Tests ausgeführt.

[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew jacocoTestReport
----

Die aktuelle Code-Coverage der Java-Projekte wird analysiert.
Die Daten werden im Ordner `build/jacoco` gespeichert.
Ein HTML-Report wird im Ordner `build/reports/jacoco` erstellt.

[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew smoke
----

Alle Container-Images werden gebaut, in die lokale Container-Registry hochgeladen und damit Integrationstests ausgeführt.

== Installation mit Docker

[[_app_postgres_up]]
=== Datenbank lokal einrichten

Die PostgreSQL-Datenbank wird mit

[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew :app:deploy:postgresUp
----

mit `docker compose` gestartet.

Die Datenbank ist auf dem Port `5432` erreichbar.
Die Daten werden in einem _persistent volume_ gespeichert.

Das interaktive PostgreSQL-Terminal `psql` wird mit

[source, bash, subs=attributes+, options="nowrap"]
----
$ docker run -it --rm --network {project}_default postgres:17 psql 'postgresql://sa@postgres17:5432/postgres'
Password for user sa: 
psql (17.4 (Debian 17.4-1.pgdg120+2))
Type "help" for help.
----

gestartet.

[_app_compose_up]
=== Anwendungen lokal einrichten

Client, Server und Datenbank wird mit

[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew :app:deploy:composeUp
----

mit `docker compose` gestartet.

Der Client ist im Browser unter `localhost:5000` erreichbar.
Das Backend ist im Browser unter `localhost:8080` erreichbar.
Das Printer ist im Browser unter `localhost:8081` erreichbar.
Das Spooler ist im Browser unter `localhost:8082` erreichbar.
Die Datenbank ist auf dem Port `5432` erreichbar.
Die Daten werden in einem _persistent volume_ gespeichert.

Die Anwendung wird _end to end_ mit 

[source, bash, subs=attributes+, options="nowrap"]
----
$ npm --prefix app/client-svelte run e2e
----

getestet.

[_app_compose_down]
=== Anwendungen lokal entfernen

Client, Server und Datenbank wird mit

[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew :app:deploy:composeDown
----

gestoppt und entfernt.
Die Daten im _persistent volume_ bleiben erhalten.

[_app_postgres_down]
=== Datenbank lokal entfernen

Die PostgreSQL-Datenbank wird mit

[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew :app:deploy:postgresDown
----

gestoppt.
Die Daten im _persistent volume_ bleiben erhalten.

== Installation mit Kubernetes

=== Container-Registry einrichten

Mit
https://www.docker.com/products/docker-desktop[Docker-Desktop]
oder
https://rancherdesktop.io[Rancher-Desktop]
eine lokale Docker-Registry einrichten.

=== Kubernetes-Cluster lokal einrichten

Mit
https://www.docker.com/products/docker-desktop[Docker-Desktop]
oder
https://rancherdesktop.io[Rancher-Desktop]
einen lokalen Kubernetes-Cluster einrichten.

=== Datenbank konfigurieren

.Secret erzeugen
[source, bash, subs=attributes+, options="nowrap"]
----
$ kubectl create secret generic {project}-db --from-literal username='sa' --from-literal password='P@ssw0rd'
----

.Secret anzeigen
[source, bash, subs=attributes+, options="nowrap"]
----
kubectl get secret {project}-db -o json | jq '.data | map_values(@base64)'
----

Für den Test kann die <<_app_postgres_up,PostgreSQL-Datenbank>> lokal gestartet werden.

=== CUPS-Server konfigurieren

.Secret erzeugen
[source, bash, subs=attributes+, options="nowrap"]
----
$ kubectl create secret generic {project}-cups --from-literal password='P@ssw0rd'
----

.Secret anzeigen
[source, bash, subs=attributes+, options="nowrap"]
----
kubectl get secret {project}-cups -o json | jq '.data | map_values(@base64)'
----

=== E-Mail-Server konfigurieren

.Secret erzeugen
[source, bash, subs=attributes+, options="nowrap"]
----
$ kubectl create secret generic {project}-smtp --from-literal host='smtp.ethereal.email' --from-literal port='587' --from-literal username='<EMAIL>' --from-literal password='qwB...'
----

.Secret anzeigen
[source, bash, subs=attributes+, options="nowrap"]
----
kubectl get secret {project}-smtp -o json | jq '.data | map_values(@base64)'
----

Für den Test kann der E-Mail-Server von
https://ethereal.email[Ethereal]
verwendet werden.

[[_github_api]]
=== Github-API konfigurieren

https://github.com/[Github]
kann für die Anmeldung mit 
https://docs.github.com/v3/oauth[OAuth2]
verwendet werden.
Dazu muss eine
https://github.com/settings/developers[OAUTH-Anwendung]
für die verwendete Redirect-URL eingerichtet werden.

.**********
[plantuml, github-oauth, subs=attributes+]
----
salt
{
  **Application name**
  "{project}-172-22-1-1"
  **Homepage URL**
  "http://**********"
  **Application description**
  "{project} on ********** via zerotier"
  **Authorization callback URL**
  "http://**********/login/oauth2/code/github"
}
----

https://github.com/[Github]
kann als Provider für Daten verwendet werden.
Dafür muss ein
https://github.com/settings/tokens[_personal access tokens_]
mindestens den Zugriff auf `repo` und `user` erlauben.

.Secret erzeugen
[source, bash, subs=attributes+, options="nowrap"]
----
$ kubectl create secret generic {project}-github --from-literal clientId=f1940f6a42d54c4e50bf --from-literal clientSecret=d6.. --from-literal token='gh..'
----

.Secret anzeigen
[source, bash, subs=attributes+, options="nowrap"]
----
kubectl get secret {project}-github -o json | jq '.data | map_values(@base64)'
----

[[_gitlab_api]]
=== Gitlab-API konfigurieren

https://gitlab.com/[Gitlab]
kann als Provider für Daten verwendet werden.
Dafür muss ein
https://docs.gitlab.com/ee/user/profile/personal_access_tokens[_personal access tokens_]
mindestens den Zugriff auf `read_api` und `read_user` erlauben.

.Secret erzeugen
[source, bash, subs=attributes+, options="nowrap"]
----
$ kubectl create secret generic {project}-gitlab --from-literal token='8xC...'
----

.Secret anzeigen
[source, bash, subs=attributes+, options="nowrap"]
----
kubectl get secret {project}-gitlab -o json | jq '.data | map_values(@base64)'
----

[[_keycloak_api]]
=== Keycloak-API konfigurieren

https://www.keycloak.org/[Keycloak]
kann für die Anmeldung mit 
https://www.keycloak.org/securing-apps/oidc-layers[OpenID]
verwendet werden.
Dazu muss ein
https://www.keycloak.org/securing-apps/oidc-layers[_OpenID Connect Client_]
für die verwendete Redirect-URL eingerichtet werden.

.**********
[plantuml, keycloak-openid, subs=attributes+]
----
salt
{
  **Client ID**
  "2be5ff537772e904ab28"
  **Valid redirect URIs**
  "http://**********/login/*"
  **Web origins**
  "http://**********"
}
----

.Secret erzeugen
[source, bash, subs=attributes+, options="nowrap"]
----
$ kubectl create secret generic {project}-keycloak --from-literal clientId=2be5ff537772e904ab28 --from-literal clientSecret=ey..
----

.Secret anzeigen
[source, bash, subs=attributes+, options="nowrap"]
----
kubectl get secret {project}-keycloak -o json | jq '.data | map_values(@base64)'
----

Für den Test kann eine selbst gehostete Instanz auf
https://keycloak.cardsplus.info
verwendet werden.

[[_jira_api]]
=== Jira-API konfigurieren

https://www.atlassian.com/software/jira[Jira]
kann als Provider für Daten verwendet werden.
Dafür muss ein
https://developer.atlassian.com/server/jira/platform/personal-access-token[_personal access tokens_]
den Zugriff erlauben.

.Secret erzeugen
[source, bash, subs=attributes+, options="nowrap"]
----
$ kubectl create secret generic {project}-jira --from-literal token='<EMAIL>:AT...'
----

.Secret anzeigen
[source, bash, subs=attributes+, options="nowrap"]
----
kubectl get secret {project}-jira -o json | jq '.data | map_values(@base64)'
----

Für den Test kann eine kostenlose Instanz von
https://developer.atlassian.com/cloud/jira/platform[Atlassian]
verwendet werden.

[[_kube_ingress_install]]
=== Kubernetes-Ingress lokal einrichten

.Nginx installieren
[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew installNginx
----

.Traefik installieren
[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew installTraefik
----

Der <<_kube_ingress_install,Ingress>> wird mit `helm` installiert und im Hintergrund gestartet.

[[_kube_install]]
=== Anwendungen lokal installieren

[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew installDockerDesktop
----

Voraussetzung ist die Installation von Kubernetes mit _Docker Desktop_.
Die <<_kube_install,Anwendung>> wird mit `helm` installiert und gestartet.
Sie ist im Browser unter `localhost` erreichbar.

[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew installRancherDesktop
----

Voraussetzung ist die Installation von Kubernetes mit _Rancher Desktop_.
Die <<_kube_install,Anwendung>> wird mit `helm` installiert und gestartet.
Sie ist im Browser unter `localhost` erreichbar.

[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew installRancherK3s
----

Voraussetzung ist die Installation von Kubernetes mit _Rancher K3s_.
Die <<_kube_install,Anwendung>> wird mit `helm` installiert und gestartet.
Sie ist im Browser unter `localhost` erreichbar.

[[_kube_uninstall]]
=== Anwendung lokal entfernen

[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew uninstall
----

Die <<_kube_install,Anwendung>> wird mit `helm` gestoppt und entfernt.
Ingress, PostgreSQL-Datenbank,Grafana, Loki und Promtail>> bleiben unverändert erhalten.

[[_kube_ingress_uninstall]]
=== Kubernetes-Ingress lokal entfernen

.Nginx entfernen
[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew uninstallNginx
----

.Traefik entfernen
[source, bash, subs=attributes+, options="nowrap"]
----
$ ./gradlew uninstallTraefik
----

Der <<_kube_ingress_install,Ingress>> wird mit `helm` gestoppt und entfernt.
