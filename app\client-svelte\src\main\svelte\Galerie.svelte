<script>
  import { collectAllYearItem } from "./utils/date.js";
  import { collectAllMonthItem } from "./utils/date.js";
  import { Circle, Jumper } from "svelte-loading-spinners";
  import Action from "./components/Action";
  import Button from "./components/Button";
  import Checkbox from "./components/Checkbox";
  import Combobox from "./components/Combobox";
  import Chip from "./components/Chip";
  import Dialog from "./components/Dialog";
  import Expand from "./components/Expand";
  import Goto from "./components/Goto";
  import Groupbox from "./components/Groupbox";
  import Icon from "./components/Icon";
  import LinkRef from "./components/LinkRef";
  import Radiobox from "./components/Radiobox";
  import Select from "./components/Select";
  import Scribble from "./components/Scribble";
  import Text from "./components/Text";
  import TextArea from "./components/TextArea";
  import TextField from "./components/TextField";
  import Toggle from "./components/Toggle";
  import { toast } from "./components/Toast";

  const now = new Date();
  const allJahrItem = collectAllYearItem(1, 4);
  const allMonatItem = collectAllMonthItem();

  const allQuelle = [
    "JIRA",
    "GITHUB",
    "GITLAB",
    "BITBUCKET",
    "CONFLUENCE",
    "MAIL",
    "CHAT",
    "SKYPE",
    "DISCORD",
    "SLACK",
    "TEAMS",
  ];

  const DE = { name: "DE", code: 1 };
  const EN = { name: "EN", code: 2 };
  const IT = { name: "IT", code: 3 };
  const allSpracheItem = [
    {
      icon: "bolt",
      value: DE,
      text: "Deutsch",
    },
    {
      icon: "cloud",
      value: EN,
      text: "Englisch",
    },
    {
      icon: "sunny",
      value: IT,
      text: "Italienisch",
    },
  ];

  let buttonValue = {
    text: undefined,
    clicked: 0,
    checked: false,
  };

  let gotoValue = {
    text: undefined,
    clicked: 0,
    checked: false,
  };

  let iconValue = {
    text: undefined,
    clicked: 0,
    checked: false,
  };

  let chipValue = {
    text: undefined,
    clicked: 0,
    checked: false,
  };

  let checkboxValue = {
    status: undefined,
  };

  let groupboxValue = {
    allQuelle: allQuelle,
    allSprache: [],
  };

  let radioboxValue = {
    quelle: "JIRA",
    sprache: EN,
  };

  let dialogVisible = false;

  let actionValue = {
    spracheItem: {},
    spracheValue: undefined,
    spracheIcon: undefined,
  };

  let comboboxValue = {
    quelle: "JIRA",
    spracheItem: {},
    spracheValue: undefined,
    spracheIcon: undefined,
  };

  let selectValue = {
    quelle: "JIRA",
    jahr: now.getFullYear(),
    monat: now.getMonth() + 1,
    spracheItem: {},
    spracheValue: undefined,
    spracheIcon: undefined,
  };

  let toggleValue = {
    allQuelle: ["JIRA"],
    allSprache: [EN],
  };

  let textAreaValue = {
    text: [
      "Lorem ipsum dolor sit amet.",
      "sed diam voluptua.",
      "Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.",
      "Sed diam voluptua.",
      "At vero eos et accusam.",
      "Stet clita kasd gubergren.",
    ].join("\n"),
  };

  let textFieldValue = {
    text: "Lorem Ipsum",
    month: [now.getFullYear(), 11].join("-"),
    week: [now.getFullYear(), "W48"].join("-"),
    date: [now.getFullYear(), "11", "27"].join("-"),
    time: "15:13",
    number: 7,
  };

  let linkFieldValue = {
    base: "https://cardsplus.info/de/struktur",
    path: "Topic",
  };

  let scribbleSvg = undefined;

  function onDialogSuccess() {
    console.log(["onDialogSuccess"]);
    dialogVisible = false;
  }

  function onDialogFailure() {
    console.log(["onDialogFailure"]);
    dialogVisible = false;
    try {
      throw new Error(textAreaValue.text);
    } catch (err) {
      toast.push(err.toString());
    }
  }

  function onButtonClicked(text) {
    console.log(["onButtonClicked", text]);
    buttonValue.text = text;
  }

  function onGotoClicked(text) {
    console.log(["onGotoClicked", text]);
    gotoValue.text = text;
  }

  function onIconClicked(text) {
    console.log(["onIconClicked", text]);
    iconValue.text = text;
  }

  function onChipClicked(text) {
    console.log(["onChipClicked", text]);
    chipValue.text = text;
  }

  function onChange(e) {
    console.log(["onChange", e]);
  }
</script>

<Dialog bind:dialogVisible>
  <h5 slot="title">{textFieldValue.name}</h5>
  <div class="w-96">
    <span>{textAreaValue.text}</span>
  </div>
  <div slot="actions">
    <Button onclick={onDialogSuccess}>Success</Button>
    <Button onclick={onDialogFailure}>Failure</Button>
  </div>
</Dialog>

<h1>Galerie</h1>
<div class="w-full flex flex-col overflow-hidden">
  <Expand>
    <span slot="title">Header</span>
    <div class="flex flex-col gap-1 mx-4">
      <h1>Level 1</h1>
      <h2>Level 2</h2>
      <h3>Level 3</h3>
      <h4>Level 4</h4>
      <h5>Level 5</h5>
      <h6>Level 6</h6>
      <p>Text</p>
    </div>
  </Expand>
  <Expand>
    <span slot="title">Dialog</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="flex flex-row items-center gap-1">
        <Icon
          name="forward"
          title="Dialog aufrufen"
          onclick={() => (dialogVisible = true)}
        />
        <Chip
          icon="forward"
          title="Dialog aufrufen"
          onclick={() => (dialogVisible = true)}
          >Dialog
        </Chip>
        <Button title="Dialog aufrufen" onclick={() => (dialogVisible = true)}
          >Dialog
        </Button>
      </div>
    </div>
  </Expand>
  <Expand>
    <a
      slot="title"
      href="https://github.com/Schum123/svelte-loading-spinners"
      target="_blank">Spinner</a
    >
    <div class="flex flex-row gap-1 mx-4">
      <Circle size="60" unit="px" duration="1s" />
      <Jumper size="60" unit="px" duration="1s" />
    </div>
  </Expand>
  <Expand>
    <span slot="title">Combobox (Quelle)</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <Combobox
            bind:value={comboboxValue.quelle}
            on:change={onChange}
            allItem={allQuelle}
            title="Wähle mich"
            label="Quelle"
            placeholder="Bitte eine Quelle wählen"
          />
        </div>
        <div class="w-full md:w-1/2">
          <Combobox
            bind:value={comboboxValue.quelle}
            on:change={onChange}
            nullable
            allItem={allQuelle}
            title="Wähle mich"
            label="Quelle"
            placeholder="Bitte eine Quelle wählen"
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <Combobox
            bind:value={comboboxValue.quelle}
            on:change={onChange}
            allItem={allQuelle}
            disabled
          />
        </div>
        <div class="w-full md:w-1/2">
          <Combobox
            bind:value={comboboxValue.quelle}
            on:change={onChange}
            nullable
            allItem={allQuelle}
            disabled
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <Text value={comboboxValue.quelle} type="text" label="Text" />
        </div>
      </div>
      <details tabindex="-1">
        <summary>JSON</summary>
        <pre>{JSON.stringify(comboboxValue, null, 2)}</pre>
      </details>
    </div>
  </Expand>
  <Expand>
    <span slot="title">Combobox (Sprache)</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-2/4">
          <Combobox
            bind:value={comboboxValue.spracheItem}
            on:change={onChange}
            allItem={allSpracheItem}
            title="Wähle mich"
            label="Sprache"
            placeholder="Bitte eine Sprache wählen"
          />
        </div>
        <div class="w-full md:w-1/4">
          <Combobox
            bind:value={comboboxValue.spracheValue}
            valueGetter={(v) => v?.value}
            on:change={onChange}
            nullable
            allItem={allSpracheItem}
            title="Wähle mich"
            s
            label="Sprache"
            placeholder="Bitte eine Sprache wählen"
          />
        </div>
        <div class="w-full md:w-1/4">
          <Combobox
            bind:value={comboboxValue.spracheIcon}
            valueGetter={(v) => v?.icon}
            on:change={onChange}
            nullable
            allItem={allSpracheItem}
            title="Wähle mich"
            s
            label="Sprache"
            placeholder="Bitte eine Sprache wählen"
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-2/4">
          <Combobox
            bind:value={comboboxValue.spracheItem}
            on:change={onChange}
            allItem={allSpracheItem}
            disabled
          />
        </div>
        <div class="w-full md:w-1/4">
          <Combobox
            bind:value={comboboxValue.spracheValue}
            valueGetter={(v) => v?.value}
            on:change={onChange}
            nullable
            allItem={allSpracheItem}
            disabled
          />
        </div>
        <div class="w-full md:w-1/4">
          <Combobox
            bind:value={comboboxValue.spracheIcon}
            valueGetter={(v) => v?.icon}
            on:change={onChange}
            nullable
            allItem={allSpracheItem}
            disabled
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-2/4">
          <Text
            value={comboboxValue.spracheItem.text}
            type="text"
            label="Text"
          />
        </div>
        <div class="w-full md:w-1/4">
          <Text
            value={comboboxValue.spracheValue?.name}
            type="text"
            label="Text"
          />
        </div>
        <div class="w-full md:w-1/4">
          <Chip icon={comboboxValue.spracheIcon || "help"} disabled />
        </div>
      </div>
      <details tabindex="-1">
        <summary>JSON</summary>
        <pre>{JSON.stringify(comboboxValue, null, 2)}</pre>
      </details>
    </div>
  </Expand>
  <Expand>
    <span slot="title">Action (Sprache)</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-2/4">
          <Action
            bind:value={actionValue.spracheItem}
            allItem={allSpracheItem}
            on:click={onChange}
            label="Sprache"
            icon="language"
            title="Wähle eine Sprache"
          />
        </div>
        <div class="w-full md:w-1/4">
          <Action
            bind:value={actionValue.spracheValue}
            valueGetter={(v) => v?.value}
            allItem={allSpracheItem}
            on:click={onChange}
            label="Sprache"
            icon="settings"
            title="Wähle eine Sprache"
          />
        </div>
        <div class="w-full md:w-1/4">
          <Action
            bind:value={actionValue.spracheIcon}
            valueGetter={(v) => v?.icon}
            allItem={allSpracheItem}
            on:click={onChange}
            label="Sprache"
            icon="palette"
            title="Wähle eine Sprache"
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-2/4">
          <Action
            bind:value={actionValue.spracheItem}
            allItem={allSpracheItem}
            on:click={onChange}
            label="Sprache"
            icon="language"
            disabled
          />
        </div>
        <div class="w-full md:w-1/4">
          <Action
            bind:value={actionValue.spracheValue}
            valueGetter={(v) => v?.value}
            allItem={allSpracheItem}
            on:click={onChange}
            label="Sprache"
            icon="settings"
            disabled
          />
        </div>
        <div class="w-full md:w-1/4">
          <Action
            bind:value={actionValue.spracheIcon}
            valueGetter={(v) => v?.icon}
            allItem={allSpracheItem}
            on:click={onChange}
            label="Sprache"
            icon="palette"
            disabled
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-2/4">
          <Text value={actionValue.spracheItem.text} type="text" label="Text" />
        </div>
        <div class="w-full md:w-1/4">
          <Text
            value={actionValue.spracheValue?.name}
            type="text"
            label="Name"
          />
        </div>
        <div class="w-full md:w-1/4">
          <Chip icon={actionValue.spracheIcon || "help"} disabled />
        </div>
      </div>
      <details tabindex="-1">
        <summary>JSON</summary>
        <pre>{JSON.stringify(actionValue, null, 2)}</pre>
      </details>
    </div>
  </Expand>
  <Expand>
    <span slot="title">Select (Quelle)</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <Select
            bind:value={selectValue.quelle}
            onchange={onChange}
            allItem={allQuelle}
            title="Wähle mich"
            label="Quelle"
            placeholder="Bitte eine Quelle wählen"
          />
        </div>
        <div class="w-full md:w-1/2">
          <Select
            bind:value={selectValue.quelle}
            onchange={onChange}
            allItem={allQuelle}
            nullable
            title="Wähle mich"
            label="Quelle"
            placeholder="Bitte eine Quelle wählen"
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <Select
            bind:value={selectValue.quelle}
            allItem={allQuelle}
            disabled
          />
        </div>
        <div class="w-full md:w-1/2">
          <Select
            bind:value={selectValue.quelle}
            allItem={allQuelle}
            nullable
            disabled
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <Text value={selectValue.quelle} type="text" label="Text" />
        </div>
      </div>
      <details tabindex="-1">
        <summary>JSON</summary>
        <pre>{JSON.stringify(selectValue, null, 2)}</pre>
      </details>
    </div>
  </Expand>
  <Expand>
    <span slot="title">Select (Monat,Jahr)</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/4">
          <Select
            bind:value={selectValue.jahr}
            onchange={onChange}
            valueGetter={(v) => v?.value}
            allItem={allJahrItem}
            title="Wähle mich"
            label="Jahr"
            placeholder="Bitte ein Jahr wählen"
          />
        </div>
        <div class="w-full md:w-1/4">
          <Select
            bind:value={selectValue.monat}
            onchange={onChange}
            valueGetter={(v) => v?.value}
            allItem={allMonatItem}
            title="Wähle mich"
            label="Monat"
            placeholder="Bitte einen Monat wählen"
          />
        </div>
        <div class="w-full md:w-1/4">
          <Select
            bind:value={selectValue.jahr}
            on:change={onChange}
            valueGetter={(v) => v?.value}
            allItem={allJahrItem}
            nullable
            title="Wähle mich"
            label="Jahr"
            placeholder="Bitte ein Jahr wählen"
          />
        </div>
        <div class="w-full md:w-1/4">
          <Select
            bind:value={selectValue.monat}
            onchange={onChange}
            valueGetter={(v) => v?.value}
            allItem={allMonatItem}
            nullable
            title="Wähle mich"
            label="Monat"
            placeholder="Bitte einen Monat wählen"
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/4">
          <Select
            bind:value={selectValue.jahr}
            valueGetter={(v) => v?.value}
            allItem={allJahrItem}
            disabled
          />
        </div>
        <div class="w-full md:w-1/4">
          <Select
            bind:value={selectValue.monat}
            onchange={onChange}
            valueGetter={(v) => v?.value}
            allItem={allMonatItem}
            disabled
          />
        </div>
        <div class="w-full md:w-1/4">
          <Select
            bind:value={selectValue.jahr}
            valueGetter={(v) => v?.value}
            allItem={allJahrItem}
            nullable
            disabled
          />
        </div>
        <div class="w-full md:w-1/4">
          <Select
            bind:value={selectValue.monat}
            onchange={onChange}
            valueGetter={(v) => v?.value}
            allItem={allMonatItem}
            nullable
            disabled
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/4">
          <Text value={selectValue.jahr} type="number" label="Jahr" />
        </div>
        <div class="w-full md:w-1/4">
          <Text value={selectValue.monat} type="number" label="Monat" />
        </div>
      </div>
      <details tabindex="-1">
        <summary>JSON</summary>
        <pre>{JSON.stringify(selectValue, null, 2)}</pre>
      </details>
    </div>
  </Expand>
  <Expand>
    <span slot="title">Select (Sprache)</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-2/4">
          <Select
            bind:value={selectValue.spracheItem}
            onchange={onChange}
            allItem={allSpracheItem}
            title="Wähle mich"
            label="Sprache"
            placeholder="Bitte eine Sprache wählen"
          />
        </div>
        <div class="w-full md:w-1/4">
          <Select
            bind:value={selectValue.spracheValue}
            valueGetter={(v) => v?.value}
            onchange={onChange}
            allItem={allSpracheItem}
            nullable
            title="Wähle mich"
            label="Sprache"
            placeholder="Bitte eine Sprache wählen"
          />
        </div>
        <div class="w-full md:w-1/4">
          <Select
            bind:value={selectValue.spracheIcon}
            valueGetter={(v) => v?.icon}
            onchange={onChange}
            allItem={allSpracheItem}
            nullable
            title="Wähle mich"
            label="Sprache"
            placeholder="Bitte eine Sprache wählen"
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-2/4">
          <Select
            bind:value={selectValue.spracheItem}
            onchange={onChange}
            allItem={allSpracheItem}
            disabled
          />
        </div>
        <div class="w-full md:w-1/4">
          <Select
            bind:value={selectValue.spracheValue}
            valueGetter={(v) => v?.value}
            onchange={onChange}
            allItem={allSpracheItem}
            nullable
            disabled
          />
        </div>
        <div class="w-full md:w-1/4">
          <Select
            bind:value={selectValue.spracheIcon}
            valueGetter={(v) => v?.icon}
            onchange={onChange}
            allItem={allSpracheItem}
            nullable
            disabled
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-2/4">
          <Text value={selectValue.spracheItem.text} type="text" label="Text" />
        </div>
        <div class="w-full md:w-1/4">
          <Text
            value={selectValue.spracheValue?.name}
            type="text"
            label="Name"
          />
        </div>
        <div class="w-full md:w-1/4">
          <Chip icon={selectValue.spracheIcon || "help"} disabled />
        </div>
      </div>
      <details tabindex="-1">
        <summary>JSON</summary>
        <pre>{JSON.stringify(selectValue, null, 2)}</pre>
      </details>
    </div>
  </Expand>
  <Expand>
    <span slot="title">Toggle (Quelle)</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <Toggle
            bind:allValue={toggleValue.allQuelle}
            onchange={onChange}
            allItem={allQuelle}
            title="Wähle mich"
            label="Quellen"
            placeholder="Bitte Quellen wählen"
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <Toggle
            bind:allValue={toggleValue.allQuelle}
            allItem={allQuelle}
            disabled
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <Text
            value={toggleValue.allQuelle.join(",")}
            type="text"
            label="Text"
          />
        </div>
      </div>
      <details tabindex="-1">
        <summary>JSON</summary>
        <pre>{JSON.stringify(toggleValue, null, 2)}</pre>
        <pre>{JSON.stringify(allQuelle, null, 2)}</pre>
      </details>
    </div>
  </Expand>
  <Expand>
    <span slot="title">Toggle (Sprache)</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <Toggle
            bind:allValue={toggleValue.allSprache}
            onchange={onChange}
            allItem={allSpracheItem}
            title="Wähle mich"
            label="Sprachen"
            placeholder="Bitte Sprachen wählen"
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <Toggle
            bind:allValue={toggleValue.allSprache}
            allItem={allSpracheItem}
            disabled
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <Text
            value={toggleValue.allSprache.map((e) => e.name).join(",")}
            type="text"
            label="Text"
          />
        </div>
      </div>
      <details tabindex="-1">
        <summary>JSON</summary>
        <pre>{JSON.stringify(toggleValue, null, 2)}</pre>
        <pre>{JSON.stringify(allSpracheItem, null, 2)}</pre>
      </details>
    </div>
  </Expand>
  <Expand>
    <span slot="title">TextField (type=text)</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <TextField
            bind:value={textFieldValue.text}
            on:change={onChange}
            type="text"
            label="Text"
            placeholder="Bitte einen Text eingeben"
          />
        </div>
        <div class="w-full md:w-1/2">
          <TextField
            bind:value={textFieldValue.text}
            on:change={onChange}
            type="password"
            label="Versteckter Text"
            placeholder="Bitte einen Text eingeben"
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <TextField bind:value={textFieldValue.text} type="text" disabled />
        </div>
        <div class="w-full md:w-1/2">
          <TextField
            bind:value={textFieldValue.text}
            type="password"
            disabled
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <Text value={textFieldValue.text} type="text" label="Text" />
        </div>
      </div>
      <details tabindex="-1">
        <summary>JSON</summary>
        <pre>{JSON.stringify(textFieldValue, null, 2)}</pre>
      </details>
    </div>
  </Expand>
  <Expand>
    <span slot="title">TextField (type=month)</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <TextField
            bind:value={textFieldValue.month}
            on:change={onChange}
            type="month"
            label="Monat"
            placeholder="Bitte einen Monat eingeben"
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <TextField bind:value={textFieldValue.month} type="month" disabled />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <Text value={textFieldValue.month} type="month" label="Monat" />
        </div>
      </div>
      <details tabindex="-1">
        <summary>JSON</summary>
        <pre>{JSON.stringify(textFieldValue, null, 2)}</pre>
      </details>
    </div>
  </Expand>
  <Expand>
    <span slot="title">TextField (type=week)</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <TextField
            bind:value={textFieldValue.week}
            on:change={onChange}
            type="week"
            label="Woche"
            placeholder="Bitte eine Woche eingeben"
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <TextField bind:value={textFieldValue.week} type="week" disabled />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <Text value={textFieldValue.week} type="week" label="Woche" />
        </div>
      </div>
      <details tabindex="-1">
        <summary>JSON</summary>
        <pre>{JSON.stringify(textFieldValue, null, 2)}</pre>
      </details>
    </div>
  </Expand>
  <Expand>
    <span slot="title">TextField (type=date)</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <TextField
            bind:value={textFieldValue.date}
            on:change={onChange}
            type="date"
            label="Datum"
            placeholder="Bitte ein Datum eingeben"
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <TextField bind:value={textFieldValue.date} type="date" disabled />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <Text value={textFieldValue.date} type="date" label="Datum" />
        </div>
      </div>
      <details tabindex="-1">
        <summary>JSON</summary>
        <pre>{JSON.stringify(textFieldValue, null, 2)}</pre>
      </details>
    </div>
  </Expand>
  <Expand>
    <span slot="title">TextField (type=time)</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <TextField
            bind:value={textFieldValue.time}
            on:change={onChange}
            type="time"
            label="Zeit"
            placeholder="Bitte eine Zeit eingeben"
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <TextField bind:value={textFieldValue.time} type="time" disabled />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <Text value={textFieldValue.time} type="time" label="Zeit" />
        </div>
      </div>
      <details tabindex="-1">
        <summary>JSON</summary>
        <pre>{JSON.stringify(textFieldValue, null, 2)}</pre>
      </details>
    </div>
  </Expand>
  <Expand>
    <span slot="title">TextField (type=number)</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <TextField
            bind:value={textFieldValue.number}
            on:change={onChange}
            type="number"
            step="1"
            min="0"
            max="720"
            label="Zahl"
            placeholder="nnn"
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <TextField
            bind:value={textFieldValue.number}
            type="number"
            step="1"
            min="0"
            max="720"
            disabled
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <Text value={textFieldValue.number} type="number" label="Zahl" />
        </div>
      </div>
      <details tabindex="-1">
        <summary>JSON</summary>
        <pre>{JSON.stringify(textFieldValue, null, 2)}</pre>
      </details>
    </div>
  </Expand>
  <Expand>
    <span slot="title">LinkRef</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <LinkRef
            path={linkFieldValue.base + "/" + linkFieldValue.path}
            bind:value={linkFieldValue.path}
            on:change={onChange}
            label="Link"
            placeholder="Link"
          />
        </div>
        <div class="w-full md:w-1/2">
          <LinkRef
            bind:value={linkFieldValue.path}
            on:change={onChange}
            label="Link"
            placeholder="Link"
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <LinkRef
            path={linkFieldValue.base + "/" + linkFieldValue.path}
            bind:value={linkFieldValue.path}
            disabled
          />
        </div>
        <div class="w-full md:w-1/2">
          <LinkRef bind:value={linkFieldValue.path} disabled />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <Text value={linkFieldValue.path} type="text" label="Pfad" />
        </div>
      </div>
      <details tabindex="-1">
        <summary>JSON</summary>
        <pre>{JSON.stringify(linkFieldValue, null, 2)}</pre>
      </details>
    </div>
  </Expand>
  <Expand>
    <span slot="title">TextArea</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <TextArea
            bind:value={textAreaValue.text}
            on:change={onChange}
            rows="5"
            resize
            label="Text"
            placeholder="Bitte einen Text eingeben"
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1 items-baseline">
        <div class="w-full md:w-1/2">
          <TextArea
            bind:value={textAreaValue.text}
            rows="5"
            resize={false}
            disabled
          />
        </div>
      </div>
      <details tabindex="-1">
        <summary>JSON</summary>
        <pre>{JSON.stringify(textAreaValue, null, 2)}</pre>
      </details>
    </div>
  </Expand>
  <Expand>
    <span slot="title">Checkbox</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="flex flex-col md:flex-row gap-1">
        <div class="w-full md:w-1/2 border-2">
          <Checkbox bind:checked={checkboxValue.status} title="Status wählen" />
          <Checkbox
            bind:checked={checkboxValue.status}
            title="Status wählen"
            label="Status"
          />
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1">
        <div class="w-full md:w-1/2 border-2">
          <Checkbox
            bind:checked={checkboxValue.status}
            title="Status wählen"
            disabled
          />
          <Checkbox
            bind:checked={checkboxValue.status}
            title="Status wählen"
            label="Status"
            disabled
          />
        </div>
      </div>
      <details tabindex="-1">
        <summary>JSON</summary>
        <pre>{JSON.stringify(checkboxValue, null, 2)}</pre>
      </details>
    </div>
  </Expand>
  <Expand>
    <span slot="title">Groupbox (Quelle, 2 Spalten)</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="flex flex-col md:flex-row gap-1">
        <div class="w-full md:w-1/2 border-2">
          <div class="grid grid-cols-2">
            <Groupbox
              bind:group={groupboxValue.allQuelle}
              allItem={allQuelle}
              title="Quelle wählen"
            />
          </div>
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1">
        <div class="w-full md:w-1/2 border-2">
          <div class="grid grid-cols-2">
            <Groupbox
              bind:group={groupboxValue.allQuelle}
              allItem={allQuelle}
              disabled
            />
          </div>
        </div>
      </div>
      <details tabindex="-1">
        <summary>JSON</summary>
        <pre>{JSON.stringify(groupboxValue, null, 2)}</pre>
      </details>
    </div>
  </Expand>
  <Expand>
    <span slot="title">Groupbox (Sprache, 1 Spalte)</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="flex flex-col md:flex-row gap-1">
        <div class="w-full md:w-1/2 border-2">
          <div class="grid grid-cols-1">
            <Groupbox
              bind:group={groupboxValue.allSprache}
              allItem={allSpracheItem}
              title="Sprache wählen"
            />
          </div>
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1">
        <div class="w-full md:w-1/2 border-2">
          <div class="grid grid-cols-1">
            <Groupbox
              bind:group={groupboxValue.allSprache}
              allItem={allSpracheItem}
              disabled
            />
          </div>
        </div>
      </div>
      <details tabindex="-1">
        <summary>JSON</summary>
        <pre>{JSON.stringify(groupboxValue, null, 2)}</pre>
      </details>
    </div>
  </Expand>
  <Expand>
    <span slot="title">Radiobox (Quelle, 2 Spalten)</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="flex flex-col md:flex-row gap-1">
        <div class="w-full md:w-1/2 border-2">
          <div class="grid grid-cols-2">
            <Radiobox
              bind:group={radioboxValue.quelle}
              allItem={allQuelle}
              title="Quelle wählen"
            />
          </div>
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1">
        <div class="w-full md:w-1/2 border-2">
          <div class="grid grid-cols-2">
            <Radiobox
              bind:group={radioboxValue.quelle}
              allItem={allQuelle}
              disabled
            />
          </div>
        </div>
      </div>
      <details tabindex="-1">
        <summary>JSON</summary>
        <pre>{JSON.stringify(radioboxValue, null, 2)}</pre>
      </details>
    </div>
  </Expand>
  <Expand>
    <span slot="title">Radiobox (Sprache, 1 Spalte)</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="flex flex-col md:flex-row gap-1">
        <div class="w-full md:w-1/2 border-2">
          <div class="grid grid-cols-1">
            <Radiobox
              bind:group={radioboxValue.sprache}
              allItem={allSpracheItem}
              title="Sprache wählen"
            />
          </div>
        </div>
      </div>
      <div class="flex flex-col md:flex-row gap-1">
        <div class="w-full md:w-1/2 border-2">
          <div class="grid grid-cols-1">
            <Radiobox
              bind:group={radioboxValue.sprache}
              allItem={allSpracheItem}
              disabled
            />
          </div>
        </div>
      </div>
      <details tabindex="-1">
        <summary>JSON</summary>
        <pre>{JSON.stringify(radioboxValue, null, 2)}</pre>
      </details>
    </div>
  </Expand>
  <Expand>
    <span slot="title">Button</span>
    <div class="flex flex-col gap-1 mx-4">
      <div
        class="grid grid-cols-2 md:grid-cols-8 justify-center items-center justify-items-left gap-1"
      >
        <Button
          title="Klick mich"
          bind:checked={buttonValue.checked}
          bind:clicked={buttonValue.clicked}
          onclick={() => onButtonClicked("Default")}
          >Default
        </Button>
        <Button title="Klick mich" disabled>Default</Button>
      </div>
      <div
        class="grid grid-cols-2 md:grid-cols-8 justify-center items-center justify-items-left gap-1"
      >
        <Button
          title="Klick mich"
          bind:checked={buttonValue.checked}
          bind:clicked={buttonValue.clicked}
          onclick={() => onButtonClicked("Outlined Default")}
          outlined>Default</Button
        >
        <Button title="Klick mich" disabled outlined>Default</Button>
      </div>
    </div>
    <pre
      class="max-w-0 mx-4"
      class:line-through={!buttonValue.checked}>{buttonValue.clicked}x {buttonValue.text}</pre>
  </Expand>
  <Expand>
    <span slot="title">Goto</span>
    <div class="flex flex-col gap-1 mx-4">
      <div
        class="grid grid-cols-2 md:grid-cols-8 justify-center items-center justify-items-left gap-1"
      >
        <Goto
          icon="forward"
          title="Klick mich"
          bind:checked={gotoValue.checked}
          bind:clicked={gotoValue.clicked}
          onclick={() => onGotoClicked("Home")}
          page="/"
          >Home
        </Goto>
        <Goto icon="forward" title="Klick mich" disabled page="/">Home</Goto>
      </div>
      <div
        class="grid grid-cols-2 md:grid-cols-8 justify-center items-center justify-items-left gap-1"
      >
        <Goto
          icon="forward"
          title="Klick mich"
          bind:checked={gotoValue.checked}
          bind:clicked={gotoValue.clicked}
          onclick={() => onGotoClicked("Outlined Home")}
          outlined
          page="/"
          >Home
        </Goto>
        <Goto icon="forward" title="Klick mich" disabled outlined page="/"
          >Home</Goto
        >
      </div>
    </div>
    <pre
      class="max-w-0 mx-4"
      class:line-through={!gotoValue.checked}>{gotoValue.clicked}x {gotoValue.text}</pre>
  </Expand>
  <Expand>
    <span slot="title">Icon</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="flex flex-row w-max gap-1">
        <Icon
          name="help"
          title="Klick mich"
          bind:checked={iconValue.checked}
          bind:clicked={iconValue.clicked}
          onclick={() => onIconClicked("Default Icon")}
        />
        <Icon name="help" title="Klick mich" disabled />
        {#each ["Build", "Delete", "Edit", "Link", "Lock", "Login", "Logout", "Print", "Schedule", "Search"] as name}
          <Icon
            name={name.toLowerCase()}
            title="Klick mich"
            bind:checked={iconValue.checked}
            bind:clicked={iconValue.clicked}
            onclick={() => onIconClicked(name + " Icon")}
          />
        {/each}
      </div>
      <div class="flex flex-row w-max gap-1">
        <Icon
          name="help"
          title="Klick mich"
          bind:checked={iconValue.checked}
          bind:clicked={iconValue.clicked}
          onclick={() => onIconClicked("Outlined Default Icon")}
          outlined
        />
        <Icon name="help" title="Klick mich" disabled outlined />
        {#each ["Build", "Delete", "Edit", "Link", "Lock", "Login", "Logout", "Print", "Schedule", "Search"] as name}
          <Icon
            name={name.toLowerCase()}
            title="Klick mich"
            bind:checked={iconValue.checked}
            bind:clicked={iconValue.clicked}
            onclick={() => onIconClicked("Outlined " + name + " Icon")}
            outlined
          />
        {/each}
      </div>
    </div>
    <pre
      class="max-w-0 mx-4"
      class:line-through={!iconValue.checked}>{iconValue.clicked}x {iconValue.text}</pre>
  </Expand>
  <Expand>
    <span slot="title">Chip</span>
    <div class="flex flex-col gap-1 mx-4">
      <div
        class="grid grid-cols-2 md:grid-cols-8 justify-center items-center justify-items-left gap-1"
      >
        <Chip
          icon="forward"
          title="Klick mich"
          bind:checked={chipValue.checked}
          bind:clicked={chipValue.clicked}
          onclick={() => onChipClicked("Chip")}
          >Chip
        </Chip>
        <Chip icon="forward" title="Klick mich" disabled>Chip</Chip>
      </div>
      <div
        class="grid grid-cols-2 md:grid-cols-8 justify-center items-center justify-items-left gap-1"
      >
        <Chip
          icon="forward"
          title="Klick mich"
          bind:checked={chipValue.checked}
          bind:clicked={chipValue.clicked}
          onclick={() => onChipClicked("Outlined Chip")}
          outlined
          >Chip
        </Chip>
        <Chip icon="forward" title="Klick mich" disabled outlined>Chip</Chip>
      </div>
    </div>
    <pre
      class="max-w-0 mx-4"
      class:line-through={!chipValue.checked}>{chipValue.clicked}x {chipValue.text}</pre>
  </Expand>

  <Expand>
    <span slot="title">Scribble</span>
    <div class="flex flex-col gap-1 mx-4">
      <div class="relative w-[480px] h-[120px]">
        <Scribble bind:svg={scribbleSvg} />
      </div>
    </div>
    <p class="break-all text-xs mx-4">{scribbleSvg}</p>
  </Expand>
</div>
