<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import { toast } from "../components/Toast";
  import { storedNutzer } from "../stores/nutzer.js";
  import { loadAllValue } from "../utils/rest.js";
  import { loadOneValue } from "../utils/rest.js";
  import { createValue } from "../utils/rest.js";
  import { updatePatch } from "../utils/rest.js";
  import { removeValue } from "../utils/rest.js";
  import { Circle } from "svelte-loading-spinners";
  import Button from "../components/Button";
  import Icon from "../components/Icon";
  import Select from "../components/Select";
  import Text from "../components/Text";
  import TextField from "../components/TextField";

  let einsatzNutzerId = undefined;

  import { collectAllYearItem } from "../utils/date.js";
  const allJahrItem = collectAllYearItem(1, 4);
  let einsatzJahr;

  import { collectAllMonthItem } from "../utils/date.js";
  const allMonatItem = collectAllMonthItem();
  let einsatzMonat;

  let allEinsatzByTag = new Map();
  let einsatzSelected = {};
  function onEinsatzClicked(einsatz) {
    einsatzSelected = einsatz;
  }

  function onChangeEchtVon(einsatz) {
    einsatz.echtStart = einsatz.echtVon;
    einsatz.echtDauer = undefined;
    einsatz.echtBis = undefined;
    console.log([einsatz.datum, einsatz]);
    // select modified table row
    einsatzSelected = einsatz;
    // refresh table with new values
    allEinsatzByTag = allEinsatzByTag;
  }

  import { computeDauer } from "../utils/date.js";
  function onChangeEchtBis(einsatz) {
    einsatz.echtDauer = computeDauer(einsatz.echtVon, einsatz.echtBis);
    console.log([einsatz.datum, einsatz]);
    // select modified table row
    einsatzSelected = einsatz;
    // refresh table with new values
    allEinsatzByTag = allEinsatzByTag;
  }

  let loading = true;
  let clicked = false;
  $: disabled = clicked || loading;
  $: console.log(["disabled", disabled]);

  let allNutzerItem = [];
  onMount(async () => {
    try {
      loading = true;
      allNutzerItem = await loadAllValue("/api/nutzer/search/findAllItem");
      console.log(["onMount", allNutzerItem]);
      einsatzNutzerId = $storedNutzer.id;
      einsatzJahr = $storedNutzer.jahr;
      einsatzMonat = $storedNutzer.monat;
      if (einsatzNutzerId) {
        await reloadEinsatzplan();
      }
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  async function onEinsatzFilterClicked() {
    if (einsatzNutzerId) {
      try {
        loading = true;
        $storedNutzer.id = einsatzNutzerId;
        $storedNutzer.jahr = einsatzJahr;
        $storedNutzer.monat = einsatzMonat;
        await tick();
        await reloadEinsatzplan();
      } finally {
        loading = false;
      }
    }
  }

  function reloadEinsatzplan() {
    const allTerminQuery = [];
    if (einsatzNutzerId) {
      allTerminQuery.push("nutzerId=" + einsatzNutzerId);
    }
    if (einsatzJahr) {
      allTerminQuery.push("jahr=" + einsatzJahr);
    }
    if (einsatzMonat) {
      allTerminQuery.push("monat=" + einsatzMonat);
    }
    if (allTerminQuery) {
      const query = "?" + allTerminQuery.join("&");
      return loadOneValue("/api/einsatzplan" + query)
        .then((json) => {
          const msg = import.meta.env.DEV ? json : json.length;
          console.log(["reloadEinsatzplan", query, msg]);
          allEinsatzByTag = new Map(Object.entries(json.allTag));
        })
        .catch((err) => {
          console.log(["reloadEinsatzplan", query, err]);
          allEinsatzByTag = new Map();
          toast.push(err.toString());
        });
    } else {
      allEinsatzByTag = new Map();
    }
  }
  function removeEinsatzplan() {
    allEinsatzByTag = new Map();
  }

  function createEinsatz(einsatz) {
    clicked = true;
    return createValue("/api/einsatz", {
      nutzerId: $storedNutzer.id,
      datum: einsatz.datum,
    })
      .then((json) => {
        console.log(["createEinsatz", einsatz, json]);
        einsatz = { ...einsatz, ...json };
        einsatz.abwesend = false;
        allEinsatzByTag.set(einsatz.datum, einsatz);
        allEinsatzByTag = allEinsatzByTag;
        clicked = false;
      })
      .catch((err) => {
        console.log(["createEinsatz", einsatz, err]);
        clicked = false;
        toast.push(err.toString());
      });
  }

  function updateEinsatz(einsatz) {
    clicked = true;
    return updatePatch("/api/einsatz/" + einsatz.id, einsatz)
      .then((json) => {
        console.log(["updateEinsatz", einsatz, json]);
        einsatz = { ...einsatz, ...json };
        einsatz.abwesend = false;
        allEinsatzByTag.set(einsatz.datum, einsatz);
        allEinsatzByTag = allEinsatzByTag;
        clicked = false;
      })
      .catch((err) => {
        console.log(["updateEinsatz", einsatz, err]);
        clicked = false;
        toast.push(err.toString());
      });
  }

  function removeEinsatz(einsatz) {
    clicked = true;
    const hint = "Einsatz am " + einsatz.datum + " wirklich löschen?";
    if (!confirm(hint)) return;
    return removeValue("/api/einsatz/" + einsatz.id)
      .then((json) => {
        console.log(["removeEinsatz", einsatz, json]);
        einsatz.id = null;
        einsatz.abwesend = false;
        allEinsatzByTag.set(einsatz.datum, einsatz);
        allEinsatzByTag = allEinsatzByTag;
        clicked = false;
      })
      .catch((err) => {
        console.log(["removeEinsatz", einsatz, err]);
        clicked = false;
        toast.push(err.toString());
      });
  }

  function createAbwesenheit(einsatz) {
    clicked = true;
    return createValue("/api/abwesenheit", {
      nutzerId: $storedNutzer.id,
      datum: einsatz.datum,
    })
      .then((json) => {
        console.log(["createAbwesenheit", einsatz, json]);
        einsatz = { ...einsatz, ...json };
        einsatz.abwesend = true;
        allEinsatzByTag.set(einsatz.datum, einsatz);
        allEinsatzByTag = allEinsatzByTag;
        clicked = false;
      })
      .catch((err) => {
        console.log(["createAbwesenheit", einsatz, err]);
        clicked = false;
        toast.push(err.toString());
      });
  }

  function removeAbwesenheit(einsatz) {
    clicked = true;
    const hint = "Abwesenheit  am " + einsatz.datum + " wirklich löschen?";
    if (!confirm(hint)) return;
    return removeValue("/api/abwesenheit/" + einsatz.id)
      .then((json) => {
        console.log(["removeAbwesenheit", einsatz, json]);
        einsatz.id = null;
        einsatz.abwesend = false;
        allEinsatzByTag.set(einsatz.datum, einsatz);
        allEinsatzByTag = allEinsatzByTag;
        clicked = false;
      })
      .catch((err) => {
        console.log(["removeAbwesenheit", einsatz, err]);
        clicked = false;
        toast.push(err.toString());
      });
  }
</script>

<h1>Einsatzplan</h1>
<div class="flex flex-col gap-1 ml-2 mr-2">
  <form on:submit|preventDefault={onEinsatzFilterClicked}>
    <div class="flex flex-row gap-1 items-center sm:items-baseline">
      <div class="flex flex-col sm:flex-row gap-1 w-full">
        <div class="w-full sm:w-2/4">
          <Select
            bind:value={einsatzNutzerId}
            onchange={removeEinsatzplan}
            allItem={allNutzerItem}
            valueGetter={(v) => v?.value}
            nullable
            required
            label="Nutzer"
            placeholder="Bitte einen Nutzer wählen"
          />
        </div>
        <div class="w-full sm:w-1/4">
          <Select
            bind:value={einsatzJahr}
            onchange={removeEinsatzplan}
            valueGetter={(v) => v?.value}
            allItem={allJahrItem}
            required
            label="Jahr"
            placeholder="Bitte ein Jahr wählen"
          />
        </div>
        <div class="w-full sm:w-1/4">
          <Select
            bind:value={einsatzMonat}
            onchange={removeEinsatzplan}
            valueGetter={(v) => v?.value}
            allItem={allMonatItem}
            required
            label="Monat"
            placeholder="Bitte einen Monat wählen"
          />
        </div>
      </div>
      <div class="w-min">
        <Icon type="submit" name="search" outlined />
      </div>
    </div>
  </form>
  {#if loading}
    <div class="h-screen flex justify-center items-center">
      <Circle size="60" unit="px" duration="1s" />
    </div>
  {:else}
    <table class="table-fixed">
      <tbody>
        {#each [...allEinsatzByTag] as [datum, einsatz]}
          <tr
            on:click={(e) => onEinsatzClicked(einsatz)}
            title={einsatz.id}
            class:border-l-2={einsatzSelected === einsatz}
          >
            <td class="px-2 py-3 w-12 align-middle">
              <div class="flex flex-col">
                <span class="text-title-600" title={datum}>
                  <b>{einsatz.wochentag}</b>
                </span>
                <small>{einsatz.tag}.</small>
              </div>
            </td>
            {#if einsatz.id && !einsatz.abwesend}
              <td class="px-2 py-3">
                <div class="flex flex-row gap-1 items-center sm:items-baseline">
                  <div class="flex flex-col sm:flex-row gap-1 w-full sm:w-max">
                    <div class="w-full sm:w-48">
                      <TextField
                        bind:value={einsatz.echtVon}
                        on:change={() => onChangeEchtVon(einsatz)}
                        type="time"
                        label="Geplant {einsatz.planVon || 'k.A.'}"
                      />
                    </div>
                    <div class="w-full sm:w-48">
                      <TextField
                        bind:value={einsatz.echtBis}
                        on:change={() => onChangeEchtBis(einsatz)}
                        type="time"
                        label="Geplant {einsatz.planBis || 'k.A.'}"
                      />
                    </div>
                    <div class="w-full sm:w-36">
                      <Text
                        bind:value={einsatz.echtDauer}
                        label="Geplant {einsatz.planDauer || 'k.A.'}"
                      />
                    </div>
                  </div>
                  <div class="flex flex-col sm:flex-row gap-1">
                    <Icon
                      onclick={() => updateEinsatz(einsatz)}
                      disabled={disabled || einsatzSelected !== einsatz}
                      name="done"
                      outlined
                    />
                    <Icon
                      onclick={() => removeEinsatz(einsatz)}
                      {disabled}
                      name="delete"
                      outlined
                    />
                  </div>
                </div>
              </td>
            {:else if einsatz.id && einsatz.abwesend}
              <td class="px-2 py-3" colspan="4">
                <Button
                  onclick={() => removeAbwesenheit(einsatz)}
                  {disabled}
                  outlined
                  >Abwesenheit entfernen
                </Button>
              </td>
            {:else}
              <td class="px-2 py-3" colspan="4">
                <Button
                  onclick={() => createAbwesenheit(einsatz)}
                  {disabled}
                  outlined
                  >Abwesenheit eintragen
                </Button>
                <span> oder </span>
                <Button
                  onclick={() => createEinsatz(einsatz)}
                  {disabled}
                  outlined
                  >Neuer Einsatz
                </Button>
              </td>
            {/if}
          </tr>
        {:else}
          <tr>
            <td class="px-2 py-3" colspan="4">Keine Einsätze</td>
          </tr>
        {/each}
      </tbody>
    </table>
  {/if}
</div>
