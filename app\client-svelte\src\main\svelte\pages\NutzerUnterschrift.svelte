<script>
  import { onMount } from "svelte";
  import { createEventDispatcher } from "svelte";
  import { toast } from "../components/Toast";
  import { loadSvg } from "../utils/rest.js";
  import { updateSvg } from "../utils/rest.js";
  import Button from "../components/Button";
  import Scribble from "../components/Scribble";

  export let visible = false;
  export let nutzer;

  onMount(async () => {
    console.log(["onMount"]);
  });

  let newUnterschrift;

  let clicked = false;
  $: disabled = clicked || !newUnterschrift;
  $: console.log(["disabled", disabled]);

  $: if (nutzer && nutzer.id) onChangeSvg();
  function onChangeSvg() {
    loadSvg("/api/nutzer/" + nutzer.id + "/unterschrift.svg")
      .then((svg) => {
        console.log(["onChangeSvg", nutzer, svg]);
        newUnterschrift = svg;
      })
      .catch((err) => {
        console.log(["onChangeSvg", nutzer, err]);
        toast.push(err.toString());
      });
  }

  const dispatch = createEventDispatcher();
  function onUpdateSvg() {
    clicked = true;
    updateSvg("/api/nutzer/" + nutzer.id + "/unterschrift.svg", newUnterschrift)
      .then(() => {
        console.log(["onUpdateSvg", newUnterschrift]);
        visible = false;
        dispatch("update", newUnterschrift);
      })
      .catch((err) => {
        console.log(["onUpdateSvg", newUnterschrift, err]);
        clicked = false;
        toast.push(err.toString());
      });
  }
  function onCancel() {
    visible = false;
  }
</script>

<div class="flex flex-col gap-1">
  <div class="relative w-[480px] h-[120px]">
    <Scribble bind:svg={newUnterschrift} />
  </div>
</div>

<div class="py-4 flex flex-row gap-1 items-baseline">
  <div class="flex-initial">
    <slot name="ok">
      <Button onclick={onUpdateSvg} {disabled}>Ok</Button>
    </slot>
  </div>
  <div class="flex-initial">
    <slot name="cancel">
      <Button onclick={onCancel}>Abbrechen</Button>
    </slot>
  </div>
</div>
