:keyword: library,java,spring
:term: spring-hateoas

https://spring.io/projects/spring-hateoas[Spring HATEOAS]
erleichtert die Entwicklung robuster, leicht verständlicher und erweiterbarer REST-APIs.
https://htmx.org/essays/hateoas[HATEOAS] steht für _Hypermedia as the Engine of Application State_.
Es ist ein Konzept, das besagt, dass ein Client die nächsten Schritte, die er in einer Anwendung unternehmen kann, durch Hyperlinks erhalten sollte, die in der Antwort eingebettet sind.
