:keyword: library,json
:term: JWT

_JSON Web Token_ (kurz JWT) ist ein offener Standard (RFC 7519) zur sicheren Übertragung von Informationen zwischen zwei Parteien, z.B. für Authentifizierung und Autorisierung in browser-basierten Anwendungen.
Sie sind kompakte und selbstbeschreibende JSON-Objekte aus drei Teilen: _Header_, _Payload_ und _Signature_.
Sie werden zur Absicherung der Integrität von einer Partei signiert und optional verschlüsselt.
