import { test } from "@playwright/test";
import { expect } from "@playwright/test";

import Chance from "chance";
const chance = new Chance();

test.describe("Regression", () => {
  test.beforeEach(async ({ page }) => {
    await page.goto("/galerie");
  });

  test("Action", async ({ page }) => {
    try {
      await page.getByText("Action (Sprache)").click();
      const select = page.getByRole("button", { name: "unfold_more" }).nth(0);
      await select.click();
      await page
        .locator("ul")
        .locator("span")
        .filter({ hasText: "Deutsch" })
        .click();
      const action = page
        .getByRole("button", { name: "language Deutsch" })
        .first();
      await action.click();
      await expect(action).toContainText("Deutsch");
    } finally {
      await page.getByText("Action (Sprache)").click();
    }
    try {
      await page.getByText("Action (Sprache)").click();
      const select = page.getByRole("button", { name: "unfold_more" }).nth(1);
      await select.click();
      await page
        .locator("ul")
        .locator("span")
        .filter({ hasText: "Englisch" })
        .click();
      const action = page
        .getByRole("button", { name: "settings Englisch" })
        .first();
      await action.click();
      await expect(action).toContainText("Englisch");
    } finally {
      await page.getByText("Action (Sprache)").click();
    }
    try {
      await page.getByText("Action (Sprache)").click();
      const select = page.getByRole("button", { name: "unfold_more" }).nth(2);
      await select.click();
      await page
        .locator("ul")
        .locator("span")
        .filter({ hasText: "Italienisch" })
        .click();
      const action = page
        .getByRole("button", { name: "palette Italienisch" })
        .first();
      await action.click();
      await expect(action).toContainText("Italienisch");
    } finally {
      await page.getByText("Action (Sprache)").click();
    }
  });

  test("Combobox", async ({ page }) => {
    try {
      await page.getByText("Combobox (Quelle)").click();
      const combobox1 = page.getByRole("textbox", { name: "Quelle" }).nth(0);
      const combobox2 = page.getByRole("textbox", { name: "Quelle" }).nth(1);
      await expect(combobox2).toHaveValue("JIRA");
      await combobox1.click();
      await page
        .locator("ul")
        .locator("span")
        .filter({ hasText: "GITHUB" })
        .click();
      await expect(combobox1).toHaveValue("GITHUB");
      await expect(combobox2).toHaveValue("GITHUB");
      await combobox1.click();
      await page.locator("ul").locator("span").nth(2).click();
      await expect(combobox1).toHaveValue("GITLAB");
      await expect(combobox2).toHaveValue("GITLAB");
      await combobox2.click();
      await page.locator("ul").locator("span").nth(0).click();
      await expect(combobox1).toHaveValue("");
      await expect(combobox2).toHaveValue("");
    } finally {
      await page.getByText("Combobox (Quelle)").click();
    }
    try {
      await page.getByText("Combobox (Sprache)").click();
      const combobox1 = page.getByRole("textbox", { name: "Sprache" }).nth(0);
      const combobox2 = page.getByRole("textbox", { name: "Sprache" }).nth(1);
      const combobox3 = page.getByRole("textbox", { name: "Sprache" }).nth(2);
      await expect(combobox1).toHaveValue("");
      await expect(combobox2).toHaveValue("");
      await expect(combobox3).toHaveValue("");
      await combobox1.click();
      await page
        .locator("ul")
        .locator("span")
        .filter({ hasText: "Deutsch" })
        .click();
      await expect(combobox1).toHaveValue("Deutsch");
      await expect(combobox2).toHaveValue("");
      await expect(combobox3).toHaveValue("");
      await combobox2.click();
      await page
        .locator("ul")
        .locator("span")
        .filter({ hasText: "Deutsch" })
        .click();
      await expect(combobox1).toHaveValue("Deutsch");
      await expect(combobox2).toHaveValue("Deutsch");
      await expect(combobox3).toHaveValue("");
      await combobox2.click();
      await page.locator("ul").locator("span").nth(0).click();
      await expect(combobox1).toHaveValue("Deutsch");
      await expect(combobox2).toHaveValue("");
      await expect(combobox3).toHaveValue("");
      await combobox3.click();
      await page
        .locator("ul")
        .locator("span")
        .filter({ hasText: "Deutsch" })
        .click();
      await expect(combobox1).toHaveValue("Deutsch");
      await expect(combobox2).toHaveValue("");
      await expect(combobox3).toHaveValue("Deutsch");
      await combobox3.click();
      await page.locator("ul").locator("span").nth(0).click();
      await expect(combobox1).toHaveValue("Deutsch");
      await expect(combobox2).toHaveValue("");
      await expect(combobox3).toHaveValue("");
    } finally {
      await page.getByText("Combobox (Sprache)").click();
    }
  });

  test("Select", async ({ page }) => {
    try {
      await page.getByText("Select (Quelle)").click();
      const select1 = page.getByRole("combobox", { name: "Quelle" }).nth(0);
      const select2 = page.getByRole("combobox", { name: "Quelle" }).nth(1);
      await expect(select1).toHaveValue("JIRA");
      await expect(select2).toHaveValue("JIRA");
      await select1.selectOption({ label: "GITHUB" });
      await expect(select1).toHaveValue("GITHUB");
      await expect(select2).toHaveValue("GITHUB");
      await select1.selectOption({ index: 2 });
      await expect(select1).toHaveValue("GITLAB");
      await expect(select2).toHaveValue("GITLAB");
      await select2.selectOption({ index: 0 });
      await expect(select1).toHaveValue("");
      await expect(select2).toHaveValue("");
    } finally {
      await page.getByText("Select (Quelle)").click();
    }
    try {
      await page.getByText("Select (Sprache)").click();
      const select1 = page.getByRole("combobox", { name: "Sprache" }).nth(0);
      const select2 = page.getByRole("combobox", { name: "Sprache" }).nth(1);
      const select3 = page.getByRole("combobox", { name: "Sprache" }).nth(2);
      await expect(select1).toHaveValue("");
      await expect(select2).toHaveValue("");
      await expect(select3).toHaveValue("");
      await select1.selectOption({ label: "Deutsch" });
      await expect(select1).toHaveValue("0");
      await expect(select2).toHaveValue("");
      await expect(select3).toHaveValue("");
      await select2.selectOption({ label: "Deutsch" });
      await expect(select1).toHaveValue("0");
      await expect(select2).toHaveValue("0");
      await expect(select3).toHaveValue("");
      await select2.selectOption({ index: 0 });
      await expect(select1).toHaveValue("0");
      await expect(select2).toHaveValue("");
      await expect(select3).toHaveValue("");
      await select3.selectOption({ label: "Deutsch" });
      await expect(select1).toHaveValue("0");
      await expect(select2).toHaveValue("");
      await expect(select3).toHaveValue("0");
      await select3.selectOption({ index: 0 });
      await expect(select1).toHaveValue("0");
      await expect(select2).toHaveValue("");
      await expect(select3).toHaveValue("");
    } finally {
      await page.getByText("Select (Sprache)").click();
    }
  });
});
