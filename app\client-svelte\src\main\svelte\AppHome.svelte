<script>
  import { onMount } from "svelte";
  import { toast } from "./components/Toast";
  import { storedToken } from "./stores/auth.js";
  import { fetchVersion, fetchWho } from "./utils/rest.js";
  import Github from "./Github.svelte";
  import <PERSON>ra from "./Jira.svelte";
  import Login from "./Login.svelte";
  import Logout from "./Logout.svelte";
  import Jwt from "./Jwt.svelte";

  import { apiExplorerUrl } from "./utils/url.js";
  const apiExplorer = apiExplorerUrl();

  import { apiGraphiqlUrl } from "./utils/url.js";
  const apiGraphiql = apiGraphiqlUrl();

  import { docExplorerUrl } from "./utils/url.js";
  const docExplorer = docExplorerUrl();

  const homeShowAll = $derived($storedToken.allRole.includes("VERWALTUNG"));

  onMount(async () => {
    try {
      console.log(["onMount", await fetchVersion()]);
      console.log(["onMount", await fetchWho()]);
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    }
  });
</script>

<div class="flex flex-col gap-1 mx-2">
  {#if $storedToken.version}
    <fieldset class="p-4 border-2">
      <legend class="text-xs">Version</legend>
      <div class="text-2xl">
        <span>{$storedToken.version}</span>
      </div>
      {#if import.meta.env.DEV}
        <details tabindex="-1">
          <summary>JSON</summary>
          <pre>{JSON.stringify($storedToken, null, 2)}</pre>
        </details>
      {/if}
    </fieldset>
    {#if $storedToken.oauth2}
      <fieldset class="p-4 border-2">
        <legend class="text-xs">
          Anmeldung; funktioniert nur mit OAuth2-Login!
        </legend>
        <div class="flex flex-col gap-1 w-max">
          {#if $storedToken.email}
            <Logout />
          {:else}
            <Login provider={$storedToken.oauth2} />
          {/if}
        </div>
      </fieldset>
    {:else}
      <fieldset class="p-4 border-2">
        <legend class="text-xs">
          Anmeldung; funktioniert nur in der Review-App!
        </legend>
        <Jwt bind:userItem={$storedToken} on:login={fetchWho} />
      </fieldset>
    {/if}
    {#if homeShowAll}
      <fieldset class="p-4 border-2">
        <legend class="text-xs">Github</legend>
        <Github />
      </fieldset>
      <fieldset class="p-4 border-2">
        <legend class="text-xs">Jira</legend>
        <Jira />
      </fieldset>
      <fieldset class="p-4 border-2">
        <legend class="text-xs">API-Explorer</legend>
        <div class="text-lg sm:text-2xl">
          <a href={apiExplorer} target="_blank" rel="noreferrer">
            {apiExplorer}
          </a>
        </div>
      </fieldset>
      <fieldset class="p-4 border-2 space-y-2">
        <legend class="text-xs">API-Graphiql</legend>
        <div class="text-lg sm:text-2xl">
          <a href={apiGraphiql} target="_blank" rel="noreferrer">
            {apiGraphiql}
          </a>
        </div>
      </fieldset>
      <fieldset class="p-4 border-2">
        <legend class="text-xs">DOC-Explorer</legend>
        <div class="text-lg sm:text-2xl">
          <a href={docExplorer} target="_blank" rel="noreferrer">
            {docExplorer}
          </a>
        </div>
      </fieldset>
    {/if}
  {:else}
    <fieldset class="p-4 border-2">
      <legend class="text-xs">Version</legend>
      <div class="text-2xl">
        <span class="text-gray-400">Nicht verfügbar</span>
      </div>
    </fieldset>
  {/if}
</div>
