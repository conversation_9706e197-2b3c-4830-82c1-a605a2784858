package esy.api.plan;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import esy.api.team.Nutzer;
import esy.json.JsonJpaUlidEntity;
import esy.json.JsonMapper;
import lombok.Getter;
import lombok.NonNull;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * Entity-Objekt für eine Meldung.
 */
@Entity
@Table(name = "meldung", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"id"}),
        @UniqueConstraint(columnNames = {"titel"})
})
public final class Meldung extends JsonJpaUlidEntity<Meldung> implements VorgangStatusAware<UUID> {

    public static final String DATE_PATTERN = "yyyy-MM-dd";
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DATE_PATTERN);

    /**
     * Meldung ist aktiv?
     */
    // tag::aktiv[]
    @Column(name = "aktiv")
    @Getter
    @JsonProperty
    private boolean aktiv;
    // end::aktiv[]

    /**
     * Kurze Bezeichnung für die Meldung.
     */
    // tag::titel[]
    @Column(name = "titel")
    @Getter
    @JsonProperty
    @NotBlank
    private String titel;
    // end::titel[]

    /**
     * Lange Beschreibung für die Meldung.
     * Kann mehrzeilig sein.
     */
    // tag::text[]
    @Column(name = "text")
    @Getter
    @JsonProperty
    @NotBlank
    private String text;
    // end::text[]

    /**
     * Prüfung ist fällig ab diesem Datum.
     */
    // tag::termin[]
    @Column(name = "termin", columnDefinition = "DATE")
    @Getter
    @JsonProperty
    @NotNull
    @DateTimeFormat(pattern = DATE_PATTERN)
    private LocalDate termin;
    // end::termin[]

    /**
     * Bezug zu einem Nutzer.
     */
    // tag::nutzer[]
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = true)
    @JoinColumn(name = "nutzer_id", referencedColumnName = "id", insertable = false, updatable = false)
    @Getter
    @JsonIgnore
    private Nutzer nutzer;
    // end::nutzer[]

    /**
     * Bezug zu einem Nutzer (nur ID).
     */
    // tag::nutzerId[]
    @Column(name = "nutzer_id")
    @Getter
    @JsonProperty
    private UUID nutzerId;
    // end::nutzerId[]

    Meldung() {
        super();
        this.aktiv = true;
        this.titel = "";
        this.text = "";
        this.termin = LocalDate.parse("2000-01-01");
        this.nutzer = null;
        this.nutzerId = null;
    }

    Meldung(@NonNull final Long version, @NonNull final UUID id) {
        super(version, id);
        this.aktiv = true;
        this.titel = "";
        this.text = "";
        this.termin = LocalDate.parse("2000-01-01");
        this.nutzer = null;
        this.nutzerId = null;
    }

    @Override
    public String toString() {
        return super.toString() + ",titel='" + titel + "'";
    }

    @Override
    public boolean isEqual(final Meldung that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        return this.aktiv == that.aktiv &&
                this.titel.equals(that.titel) &&
                this.text.equals(that.text) &&
                this.termin.equals(that.termin) &&
                Objects.equals(this.nutzerId, that.nutzerId);
    }

    @Override
    public Meldung withId(@NonNull final UUID id) {
        if (Objects.equals(getId(), id)) {
            return this;
        } else {
            return new Meldung(getVersion(), id).merge(this);
        }
    }

    @Override
    public Meldung merge(@NonNull final Meldung that) {
        this.aktiv = that.aktiv;
        this.titel = that.titel;
        this.text = that.text;
        this.termin = that.termin;
        this.nutzer = that.nutzer;
        this.nutzerId = that.nutzerId;
        return this;
    }

    /**
     * Applies a transition for {@link Meldung#aktiv}.
     * Computes a new date for {@link Meldung#termin}.
     *
     * @param status {@link VorgangStatus status}
     */
    @Override
    public void applyStatusTransition(@NonNull final VorgangStatus status) {
        this.aktiv = !VorgangStatus.X.equals(status);
        this.termin = LocalDate.now();
    }

    @JsonAnyGetter
    private Map<String, Object> extraJson() {
        final var allExtra = new HashMap<String, Object>();
        allExtra.put("version", getVersion());
        return allExtra;
    }

    @JsonIgnore
    @Override
    public boolean isFaellig(@NonNull final LocalDate datum) {
        return datum.isAfter(this.termin);
    }

    @JsonIgnore
    @Override
    public boolean isWiedervorlage() {
        return false;
    }

    @JsonIgnore
    @Override
    public VorgangStatus getStatus() {
        return this.aktiv ? VorgangStatus.I : VorgangStatus.X;
    }

    @JsonIgnore
    public boolean isBesitzer(@NonNull final String mail) {
        return nutzer != null && mail.equals(nutzer.getMail());
    }

    @JsonIgnore
    public Meldung setNutzer(final Nutzer nutzer) {
        if (nutzer != null) {
            this.nutzer = nutzer;
            this.nutzerId = nutzer.getId();
        } else {
            this.nutzer = null;
            this.nutzerId = null;
        }
        return this;
    }

    @Override
    public String writeJson() {
        return new JsonMapper().writeJson(this);
    }

    public static Meldung parseJson(@NonNull final String json) {
        return new JsonMapper().parseJson(json, Meldung.class);
    }
}
