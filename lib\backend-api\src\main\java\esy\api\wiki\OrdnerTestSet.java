package esy.api.wiki;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;

import java.time.LocalDate;
import java.util.Map;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.function.Function.identity;

@RequiredArgsConstructor
@SuppressWarnings("java:S1192") // allow duplicating literals
public class OrdnerTestSet {

    private final UnaryOperator<Ordner> saver;

    public Map<String, Ordner> createAllOrdner(@NonNull final LocalDate datum) {
        return Stream.of(
                        Ordner.parseJson("""
                                {
                                    "art":"GITHUB",
                                    "uri":"https://github.com/cardsplus/esyscs-test-public",
                                    "titel":"ESYSCS (public)"
                                }"""),
                        Ordner.parseJson("""
                                {
                                    "art":"GITHUB",
                                    "uri":"https://github.com/cardsplus/esyscs-test-private",
                                    "titel":"ESYSCS (private)"
                                }"""))
                .map(e -> e.addSeite(Seite.parseJson("""
                        {
                            "uri":"README.adoc",
                            "titel":"README",
                            "sprache":"DE",
                            "allOption":["DE","AT"],
                            "aktiv":"false",
                            "termin":"%s"
                        }
                        """.formatted(datum)).setOrdner(e)))
                .map(e -> saver.apply(e.verify()))
                .collect(Collectors.toMap(Ordner::getUri, identity()));
    }
}
