<script>
  import { onMount } from "svelte";
  import { createEventDispatcher } from "svelte";
  import { toast } from "../components/Toast";
  import { createValue } from "../utils/rest.js";
  import { updatePatch } from "../utils/rest.js";
  import Button from "../components/Button";
  import TextArea from "../components/TextArea";
  import TextField from "../components/TextField";

  export let visible = false;
  export let autofocus = true;
  export let autoscroll = true;
  export let gefahr = undefined;

  let focusOn;
  let bottomDiv;
  onMount(async () => {
    console.log(["onMount", autofocus, autoscroll]);
    if (autofocus) focusOn.focus();
    if (autoscroll) bottomDiv.scrollIntoView(false);
  });

  let clicked = false;
  let showUpdate = false;
  let newGefahr = {
    titel: "",
    text: "",
    aktiv: true,
  };

  $: if (gefahr) onChange();
  function onChange() {
    showUpdate = true;
    newGefahr = {
      id: gefahr.id,
      titel: gefahr.titel,
      text: gefahr.text,
      aktiv: gefahr.aktiv,
      version: gefahr.version,
    };
    console.log(["onChange", newGefahr]);
  }

  async function onSubmit() {
    try {
      clicked = true;
      if (showUpdate) {
        await updateGefahr();
      } else {
        await createGefahr();
      }
    } finally {
      clicked = false;
    }
  }
  async function onCancel() {
    visible = false;
  }

  const dispatch = createEventDispatcher();
  function createGefahr() {
    return createValue("/api/gefahr", newGefahr)
      .then((json) => {
        console.log(["createGefahr", newGefahr, json]);
        visible = false;
        dispatch("create", json);
      })
      .catch((err) => {
        console.log(["createGefahr", newGefahr, err]);
        toast.push(err.toString());
      });
  }
  function updateGefahr() {
    return updatePatch("/api/gefahr" + "/" + newGefahr.id, newGefahr)
      .then((json) => {
        console.log(["updateGefahr", newGefahr, json]);
        visible = false;
        dispatch("update", json);
      })
      .catch((err) => {
        console.log(["updateGefahr", newGefahr, err]);
        toast.push(err.toString());
      });
  }
</script>

<form on:submit|preventDefault={onSubmit} on:reset|preventDefault={onCancel}>
  <div class="flex flex-col gap-1">
    <div class="w-full">
      <TextField
        bind:this={focusOn}
        bind:value={newGefahr.titel}
        required
        label="Titel"
        placeholder="Bitte einen Text eingeben"
      />
    </div>
    <div class="w-full">
      <TextArea
        bind:value={newGefahr.text}
        label="Text"
        placeholder="Bitte einen Text eingeben"
      />
    </div>
  </div>
  <div class="py-4 flex flex-row gap-1 items-baseline">
    <div class="flex-initial">
      <Button type="submit">Ok</Button>
    </div>
    <div class="flex-initial">
      <slot name="cancel">
        <Button type="reset">Abbrechen</Button>
      </slot>
    </div>
  </div>
</form>

<div class="h-0" bind:this={bottomDiv}>&nbsp;</div>

{#if import.meta.env.DEV}
  <details tabindex="-1">
    <summary>JSON</summary>
    <pre>{JSON.stringify(newGefahr, null, 2)}</pre>
  </details>
{/if}
