<script>
  import { onMount } from "svelte";
  import { createEventDispatcher } from "svelte";
  import { toast } from "../components/Toast";
  import { updatePatch } from "../utils/rest.js";
  import Button from "../components/Button";
  import Icon from "../components/Icon";
  import Select from "../components/Select";
  import TextField from "../components/TextField";

  export let visible = false;
  export let autofocus = true;
  export let autoscroll = true;
  export let nutzer;
  export let allGruppeItem;

  let clicked = false;
  let bottomDiv;
  onMount(async () => {
    console.log(["onMount", autofocus, autoscroll]);
    if (autofocus) newGruppeFocusOn.focus();
    if (autoscroll) bottomDiv.scrollIntoView(false);
  });

  let newNutzer = {
    name: undefined,
    allGruppeItem: [],
    aktiv: true,
  };
  let newGruppeItem = {};
  let newGruppeFocusOn;

  $: if (nutzer && nutzer.id) onChange();
  async function onChange() {
    newNutzer = { ...newNutzer, ...nutzer };
    console.log(["onChange", newNutzer]);
  }

  async function onSubmit() {
    try {
      clicked = true;
      newNutzer.allGruppe = newNutzer.allGruppeItem.map(
        (e) => "/api/gruppe/" + e.value
      );
      await updateNutzer();
    } finally {
      clicked = false;
    }
  }
  async function onCancel() {
    visible = false;
  }

  function onInsertGruppe() {
    newNutzer.allGruppeItem = [...newNutzer.allGruppeItem, newGruppeItem];
    newGruppeItem = {};
    newGruppeFocusOn.focus();
    console.log(["onInsertGruppe", newNutzer]);
  }
  function onRemoveGruppe(index) {
    newNutzer.allGruppeItem = [
      ...newNutzer.allGruppeItem.slice(0, index),
      ...newNutzer.allGruppeItem.slice(index + 1),
    ];
    newGruppeItem = {};
    newGruppeFocusOn.focus();
    console.log(["onRemoveGruppe", newNutzer]);
  }

  const dispatch = createEventDispatcher();
  function updateNutzer() {
    return updatePatch("/api/nutzer" + "/" + newNutzer.id, newNutzer)
      .then((json) => {
        console.log(["updateNutzer", newNutzer, json]);
        visible = false;
        dispatch("update", json);
      })
      .catch((err) => {
        console.log(["updateNutzer", newNutzer, err]);
        toast.push(err.toString());
      });
  }
</script>

<form on:submit|preventDefault={onSubmit} on:reset|preventDefault={onCancel}>
  <div class="flex flex-col">
    <div class="w-full">
      {#each newNutzer.allGruppeItem as gruppeItem, i}
        <div class="flex flex-row gap-1 items-baseline">
          <div class="w-full">
            <TextField
              bind:value={gruppeItem.text}
              disabled
              title={gruppeItem.value}
              label={i + 1 + ". Gruppe"}
            />
          </div>
          <div class="place-self-center">
            <Icon onclick={() => onRemoveGruppe(i)} name="delete" outlined />
          </div>
        </div>
      {/each}
      <div class="flex flex-row gap-1 items-baseline">
        <div class="w-full">
          <Select
            bind:this={newGruppeFocusOn}
            bind:value={newGruppeItem}
            allItem={allGruppeItem}
            label="Neue Gruppe"
            placeholder="Bitte eine Gruppe wählen"
          />
        </div>
        <div class="place-self-center">
          <Icon
            onclick={() => onInsertGruppe()}
            disabled={!newGruppeItem.value}
            name="add"
            outlined
          />
        </div>
      </div>
    </div>
  </div>
  <div class="py-4 flex flex-row gap-1 items-baseline">
    <div class="flex-initial">
      <Button type="submit">Ok</Button>
    </div>
    <div class="flex-initial">
      <slot name="cancel">
        <Button type="reset">Abbrechen</Button>
      </slot>
    </div>
  </div>
</form>

<div class="h-0" bind:this={bottomDiv}>&nbsp;</div>

{#if import.meta.env.DEV}
  <details tabindex="-1">
    <summary>JSON</summary>
    <pre>{JSON.stringify(newNutzer, null, 2)}</pre>
  </details>
{/if}
