<script>
  import { toast } from "./components/Toast";
  import { fetchInfo } from "./utils/rest.js";
  import { fetchUser } from "./utils/rest.js";
  import Chip from "./components/Chip";

  let disabled = false;
  $: console.log(["disabled", disabled]);

  let githubInfo;
  function onClickGithubInfo() {
    disabled = true;
    fetchInfo("github")
      .then((json) => {
        console.log(["onClickGithubInfo", json]);
        disabled = false;
        githubInfo = json;
      })
      .catch((err) => {
        console.log(["onClickGithubInfo", err]);
        toast.push(err.toString());
        disabled = false;
        githubInfo = undefined;
      });
  }

  let githubUser;
  function onClickGithubUser() {
    disabled = true;
    fetchUser("github")
      .then((json) => {
        console.log(["onClickGithubUser", json]);
        disabled = false;
        githubUser = json;
        githubUser.url = "https://api.github.com/users/" + json.login;
      })
      .catch((err) => {
        console.log(["onClickGithubUser", err]);
        toast.push(err.toString());
        disabled = false;
        githubUser = undefined;
      });
  }
</script>

<div class="flex flex-col gap-1">
  <div class="flex flex-row gap-1 justify-start items-center">
    <div class="w-max">
      <Chip icon="lan" onclick={onClickGithubInfo} {disabled}
        >Server erreichbar?
      </Chip>
    </div>
    <div class="overflow-hidden">
      {#if githubInfo}
        <span>{githubInfo.version}</span>
      {/if}
    </div>
  </div>
  <div class="flex flex-row gap-1 justify-start items-center">
    <div class="w-max">
      <Chip icon="person" onclick={onClickGithubUser} {disabled}
        >Konto vorhanden?
      </Chip>
    </div>
    <div class="overflow-hidden">
      {#if githubUser}
        <a class="truncate" href={githubUser.url} target="_blank"
          >{githubUser.url}
        </a>
      {/if}
    </div>
  </div>
</div>
{#if import.meta.env.DEV}
  <details tabindex="-1">
    <summary>JSON</summary>
    <pre>{JSON.stringify(githubInfo, null, 2)}</pre>
    <pre>{JSON.stringify(githubUser, null, 2)}</pre>
  </details>
{/if}
