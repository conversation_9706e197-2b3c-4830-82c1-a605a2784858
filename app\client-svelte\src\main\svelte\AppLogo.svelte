<script>
  import { storedToken } from "./stores/auth.js";

  let { open = $bindable() } = $props();

  const text = $derived(
    $storedToken.email ? $storedToken.email : "Nicht angemeldet"
  );
</script>

<a
  class="text-title-500 hover:text-title-700 cursor-pointer mr-4 border-none focus:outline-hidden"
  class:open
  href="/home"
  onclick={() => (open = false)}
>
  <svg height="24">
    <text x="0" y="20">{text}</text>
  </svg>
</a>

<style>
  svg {
    min-height: 24px;
  }
  svg text {
    fill: currentColor;
    font-weight: bold;
  }
</style>
