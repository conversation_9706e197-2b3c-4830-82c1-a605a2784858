<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import { toast } from "../components/Toast";
  import { loadAllValue } from "../utils/rest.js";
  import { updatePatch } from "../utils/rest.js";
  import { removeValue } from "../utils/rest.js";
  import { Circle } from "svelte-loading-spinners";
  import Icon from "../components/Icon";
  import Checkbox from "../components/Checkbox";
  import TextField from "../components/TextField";
  import GruppeEditor from "./GruppeEditor.svelte";

  let loading = true;
  onMount(async () => {
    try {
      loading = true;
      await reloadAllGruppe();
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  let gruppeId = undefined;
  async function onGruppeClicked(gruppe) {
    gruppeId = gruppe.id;
  }
  async function onGruppeRemoveClicked(gruppe) {
    gruppeId = gruppe.id;
    await removeGruppe(gruppe);
  }
  let gruppeEditorCreate = false;
  async function onGruppeEditorCreateClicked() {
    gruppeEditorCreate = true;
  }
  let gruppeEditorUpdate = false;
  async function onGruppeEditorUpdateClicked(gruppe) {
    gruppeId = gruppe.id;
    gruppeEditorUpdate = true;
  }
  $: gruppeEditorDisabled = gruppeEditorCreate || gruppeEditorUpdate;

  let gruppeFilter;
  function gruppeFilterParameter() {
    if (!gruppeFilter) return "";
    return "&name=" + encodeURIComponent(gruppeFilter);
  }
  function gruppeSortParameter() {
    return "?sort=name";
  }
  async function onGruppeFilterClicked() {
    try {
      loading = true;
      await tick();
      await reloadAllGruppe();
    } finally {
      loading = false;
    }
  }

  let allGruppe = [];
  function onCreateGruppe(gruppe) {
    allGruppe = allGruppe.toSpliced(0, 0, gruppe);
  }
  function onUpdateGruppe(gruppe) {
    let index = allGruppe.findIndex((e) => e.id === gruppe.id);
    if (index > -1) allGruppe = allGruppe.toSpliced(index, 1, gruppe);
  }
  function onRemoveGruppe(gruppe) {
    let index = allGruppe.findIndex((e) => e.id === gruppe.id);
    if (index > -1) allGruppe = allGruppe.toSpliced(index, 1);
  }
  function reloadAllGruppe() {
    const query = gruppeSortParameter() + gruppeFilterParameter();
    return loadAllValue("/api/gruppe" + query)
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadAllGruppe", query, msg]);
        allGruppe = json;
      })
      .catch((err) => {
        console.log(["reloadAllGruppe", query, err]);
        allGruppe = [];
        toast.push(err.toString());
      });
  }

  function updateGruppe(gruppe) {
    return updatePatch("/api/gruppe/" + gruppe.id, gruppe)
      .then((json) => {
        console.log(["updateGruppe", gruppe, json]);
        onUpdateGruppe(json);
      })
      .catch((err) => {
        console.log(["updateGruppe", gruppe, err]);
        toast.push(err.toString());
      });
  }
  function removeGruppe(gruppe) {
    const text = gruppe.name;
    const hint = text.length > 20 ? text.substring(0, 20) + "..." : text;
    if (!confirm("Gruppe '" + hint + "' wirklich löschen?")) return;
    return removeValue("/api/gruppe/" + gruppe.id)
      .then((json) => {
        console.log(["removeGruppe", gruppe, json]);
        onRemoveGruppe(json);
      })
      .catch((err) => {
        console.log(["removeGruppe", gruppe, err]);
        toast.push(err.toString());
      });
  }
</script>

<h1 title="Liste der Gruppen, ggfs. gefiltert, jedes Element editierbar">
  Gruppe
</h1>
<div class="flex flex-col gap-1 ml-2 mr-2">
  <form on:submit|preventDefault={onGruppeFilterClicked}>
    <div class="flex flex-row gap-1 items-center pr-2">
      <div class="w-full">
        <TextField
          bind:value={gruppeFilter}
          label="Filter"
          placeholder="Bitte Filterkriterien eingeben"
        />
      </div>
      <div class="w-min">
        <Icon type="submit" name="search" outlined />
      </div>
    </div>
  </form>
  {#if loading}
    <div class="h-screen flex justify-center items-center">
      <Circle size="60" unit="px" duration="1s" />
    </div>
  {:else}
    <table class="table-fixed">
      <thead class="justify-between">
        <tr class="bg-title-100">
          <th class="px-2 py-3 table-cell">
            <span class="text-title-600 text-sm">Aktiv</span>
          </th>
          <th class="px-2 py-3 text-left w-full table-cell">
            <span class="text-title-600">Name</span>
          </th>
          <th class="px-2 py-3 text-right w-0 table-cell">
            <Icon
              onclick={() => onGruppeEditorCreateClicked()}
              disabled={gruppeEditorDisabled}
              title="Gruppe hinzufügen"
              name="add"
              outlined
            />
          </th>
        </tr>
      </thead>
      <tbody>
        {#if gruppeEditorCreate}
          <tr>
            <td class="px-2" colspan="3">
              <GruppeEditor
                bind:visible={gruppeEditorCreate}
                on:create={(e) => onCreateGruppe(e.detail)}
              />
            </td>
          </tr>
        {/if}
        {#each allGruppe as gruppe, i}
          <tr
            on:click={(e) => onGruppeClicked(gruppe)}
            title={gruppe.id}
            class:border-l-2={gruppeId === gruppe.id}
            class:bg-gray-100={i % 2 === 1}
          >
            <td class="px-2 py-3 table-cell">
              <Checkbox
                bind:checked={gruppe.aktiv}
                on:change={() => updateGruppe(gruppe)}
              />
            </td>
            <td class="px-2 py-3 text-left table-cell">
              <div class="text-sm underline text-blue-600">
                <a href={"/gruppe/" + gruppe.id}>{gruppe.name}</a>
              </div>
            </td>
            <td class="px-2 py-3 table-cell">
              <div
                class="grid grid-cols-1 md:grid-cols-2 items-center gap-1 w-max"
              >
                <Icon
                  on:click={() => onGruppeRemoveClicked(gruppe)}
                  disabled={gruppeEditorDisabled || gruppe.aktiv}
                  title="Gruppe löschen"
                  name="delete"
                  outlined
                />
                <Icon
                  on:click={() => onGruppeEditorUpdateClicked(gruppe)}
                  disabled={gruppeEditorDisabled}
                  title="Gruppe bearbeiten"
                  name="edit"
                  outlined
                />
              </div>
            </td>
          </tr>
          {#if gruppeEditorUpdate && gruppeId === gruppe.id}
            <tr>
              <td class="border-l-4 px-2" colspan="3">
                <GruppeEditor
                  bind:visible={gruppeEditorUpdate}
                  on:update={(e) => onUpdateGruppe(e.detail)}
                  {gruppe}
                />
              </td>
            </tr>
          {/if}
        {:else}
          <tr>
            <td class="px-2" colspan="3">Keine Gruppen</td>
          </tr>
        {/each}
      </tbody>
    </table>
  {/if}
</div>
