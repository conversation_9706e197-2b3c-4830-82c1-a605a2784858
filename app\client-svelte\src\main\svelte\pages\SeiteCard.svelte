<script>
  import { createEventDispatcher } from "svelte";
  import { toast } from "../components/Toast";
  import { updatePatch } from "../utils/rest.js";
  import { removeValue } from "../utils/rest.js";
  import { printerGet } from "../utils/rest.js";
  import Icon from "../components/Icon";
  import LinkDoc from "../components/LinkDoc";
  import Select from "../components/Select";
  import TextField from "../components/TextField";

  export let seite;
  export let allDateitypItem;
  export let allSpracheItem;

  let clicked = false;
  $: disabled = clicked;
  $: console.log(["disabled", disabled]);

  async function onUpdateSeite() {
    try {
      clicked = true;
      await updateSeite();
    } finally {
      clicked = false;
    }
  }
  async function onRemoveSeite() {
    try {
      clicked = true;
      const text = seite.titel;
      const hint = text.length > 20 ? text.substring(0, 20) + "..." : text;
      if (confirm("Seite '" + hint + "' wirklich löschen?")) {
        await removeSeite();
      }
    } finally {
      clicked = false;
    }
  }
  async function onUpdateAktiv() {
    try {
      clicked = true;
      await updateAktiv();
      await updateSeite();
    } finally {
      clicked = false;
    }
  }

  const dispatch = createEventDispatcher();
  function updateAktiv() {
    return printerGet("/doc/seite/" + seite.id)
      .then((json) => {
        console.log(["onUpdateAktiv", seite, json]);
        seite.aktiv = true;
      })
      .catch((err) => {
        console.log(["onUpdateAktiv", seite, err]);
        seite.aktiv = false;
      });
  }
  function updateSeite() {
    return updatePatch("/api/seite/" + seite.id, seite)
      .then((json) => {
        console.log(["onUpdateSeite", seite, json]);
        seite = { ...seite, ...json };
        dispatch("update", json);
      })
      .catch((err) => {
        console.log(["onUpdateSeite", seite, err]);
        toast.push(err.toString());
      });
  }
  function removeSeite() {
    return removeValue("/api/seite/" + seite.id)
      .then((json) => {
        console.log(["onRemoveSeite", seite, json]);
        seite = { ...seite, ...json };
        dispatch("remove", json);
      })
      .catch((err) => {
        console.log(["onRemoveSeite", seite, err]);
        toast.push(err.toString());
      });
  }
</script>

<div class="block mt-2 p-2 border rounded-lg shadow-lg">
  <div class="flex flex-col">
    <div class="flex flex-row items-center gap-1">
      <div class="flex flex-col grow">
        <div class="w-full flex flex-col sm:flex-row gap-1">
          <div class="w-full sm:w-3/5">
            <TextField
              bind:value={seite.titel}
              on:change={onUpdateSeite}
              required
              {disabled}
              label="Titel"
              title="Bitte hier einen Titel eingeben"
            />
          </div>
          <div class="w-full sm:w-1/5">
            <Select
              bind:value={seite.typ}
              onchange={onUpdateSeite}
              required
              allItem={allDateitypItem.map((e) => e.name)}
              label="Typ"
              placeholder="Bitte einen Typ wählen"
            />
          </div>
          <div class="w-full sm:w-1/5">
            <Select
              bind:value={seite.sprache}
              onchange={onUpdateSeite}
              required
              valueGetter={(v) => v?.value}
              allItem={allSpracheItem}
              label="Sprache"
              placeholder="Bitte eine Sprache wählen"
            />
          </div>
        </div>
      </div>
      <div class="grid grid-cols-1 sm:grid-cols-2 items-top gap-1">
        <Icon onclick={onUpdateAktiv} {disabled} name="sync" outlined />
        <Icon onclick={onRemoveSeite} {disabled} name="delete" outlined />
      </div>
    </div>
    <LinkDoc
      path={"/doc/seite/" + seite.id}
      bind:value={seite.uri}
      on:change={onUpdateSeite}
    />
  </div>

  {#if import.meta.env.DEV}
    <details tabindex="-1">
      <summary tabindex="-1">JSON</summary>
      <pre>{JSON.stringify(seite, null, 2)}</pre>
    </details>
  {/if}
</div>
