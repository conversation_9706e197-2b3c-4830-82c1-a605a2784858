package esy.app.team;

import com.microsoft.playwright.Playwright;
import esy.test.PlaywrightDocAssertionBase;
import esy.test.PlaywrightDocTestEnv;
import lombok.NonNull;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.xssf.extractor.XSSFExcelExtractor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jsoup.Jsoup;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpStatus;

import java.io.ByteArrayInputStream;
import java.io.IOException;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.jupiter.api.Assertions.*;

@SuppressWarnings("java:S1192") // duplicating literals is ok
public class NutzerPlaywrightDocAssertion extends PlaywrightDocAssertionBase {

    public NutzerPlaywrightDocAssertion(@NonNull final Playwright playwright, @NonNull final PlaywrightDocTestEnv env) {
        super(playwright, env);
    }

    public void assertNutzerKatalogAdoc() throws IOException {
        try (final var mock = createMock(new ClassPathResource("adoc/nutzerKatalog.adoc"))) {
            doWithApi(
                    (api) -> api.get("/doc/nutzer/katalog.adoc"),
                    (res) -> {
                        assertEquals("GET", mock.requestMethod());
                        assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                        final var text = res.text();
                        assertTrue(text.startsWith("= Katalog der Nutzer"));
                        return null;
                    });
        }
    }

    public void assertNutzerKatalogHtml() throws IOException {
        try (final var mock = createMock(new ClassPathResource("adoc/nutzerKatalog.adoc"))) {
            doWithApi(
                    (api) -> api.get("/doc/nutzer/katalog.html"),
                    (res) -> {
                        assertEquals("GET", mock.requestMethod());
                        assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                        final var htmlResource = res.text();
                        assertDoesNotThrow(() -> {
                            final var htmlDocument = Jsoup.parse(htmlResource);
                            final var text = htmlDocument.body().text();
                            assertNotNull(text);
                            assertTrue(text.startsWith("Katalog der Nutzer"));
                        });
                        return null;
                    });
        }
    }

    public void assertNutzerKatalogPdf() throws IOException {
        try (final var mock = createMock(new ClassPathResource("adoc/nutzerKatalog.adoc"))) {
            doWithApi(
                    (api) -> api.get("/doc/nutzer/katalog.pdf"),
                    (res) -> {
                        assertEquals("GET", mock.requestMethod());
                        assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                        final var pdfResource = res.body();
                        assertDoesNotThrow(() -> {
                            try (final var pd = Loader.loadPDF(pdfResource)) {
                                final var text = new PDFTextStripper().getText(pd);
                                assertNotNull(text);
                                assertTrue(text.startsWith("Katalog der Nutzer"));
                            }
                        });
                        return null;
                    });
        }
    }

    public void assertNutzerKatalogXls() throws IOException {
        try (final var mock = createMock(new ClassPathResource("graphql/allNutzer.json"))) {
            doWithApi(
                    (api) -> api.get("/doc/nutzer/katalog.xlsx"),
                    (res) -> {
                        assertEquals("POST", mock.requestMethod());
                        assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                        final var xlsResource = res.body();
                        assertDoesNotThrow(() -> {
                            try (final var is = new ByteArrayInputStream(xlsResource)) {
                                try (final var pkg = OPCPackage.open(is)) {
                                    final var text = new XSSFExcelExtractor(new XSSFWorkbook(pkg)).getText();
                                    assertTrue(text.contains("<EMAIL>\tMax Mustermann\tJa\tMäx\n"), text);
                                    assertTrue(text.contains("<EMAIL>\tMia Musterfrau\tJa\tMia\n"), text);
                                }
                            }
                        });
                        return null;
                    });
        }
    }

    public void assertNutzerSteckbriefPdf() throws IOException {
        try (final var mock = createMock(new ClassPathResource("graphql/nutzerById.json"))) {
            doWithApi(
                    (api) -> api.get("/doc/nutzer/deadbeef-dead-beef-dead-beefdeadbeef.pdf"),
                    (res) -> {
                        assertEquals("POST", mock.requestMethod());
                        assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                        final var pdfResource = res.body();
                        assertDoesNotThrow(() -> {
                        try (final var pd = Loader.loadPDF(pdfResource)) {
                                final var pdForm = pd.getDocumentCatalog().getAcroForm();
                                assertEquals("Max Mustermann",
                                        pdForm.getField("['name']").getValueAsString());
                                assertEquals("<EMAIL>",
                                        pdForm.getField("['mail']").getValueAsString());
                                assertEquals("Mäx",
                                        pdForm.getField("['profil']['name']").getValueAsString());
                                assertEquals("Nein",
                                        pdForm.getField("['profil']['chat']").getValueAsString());
                                assertEquals("Ja",
                                        pdForm.getField("['profil']['mail']").getValueAsString());
                                assertEquals("Nein",
                                        pdForm.getField("['profil']['wiki']").getValueAsString());
                                assertEquals("Ops-Team,Dev-Team,Admins",
                                        pdForm.getField("['allGruppe'][*]['name']").getValueAsString());
                            }
                        });
                        return null;
                    });
        }
    }

    public void assertNutzerSteckbriefXls() throws IOException {
        try (final var mock = createMock(new ClassPathResource("graphql/nutzerById.json"))) {
            doWithApi(
                    (api) -> api.get("/doc/nutzer/deadbeef-dead-beef-dead-beefdeadbeef.xlsx"),
                    (res) -> {
                        assertEquals("POST", mock.requestMethod());
                        assertThat(res.status(), equalTo(HttpStatus.OK.value()));
                        final var xlsResource = res.body();
                        assertDoesNotThrow(() -> {
                            try (final var is = new ByteArrayInputStream(xlsResource)) {
                                try (final var pkg = OPCPackage.open(is)) {
                                    final var text = new XSSFExcelExtractor(new XSSFWorkbook(pkg)).getText();
                                    assertTrue(text.contains("Hallo\tMäx\n"), text);
                                    assertTrue(text.contains("E-Mail\<EMAIL>\n"), text);
                                    assertTrue(text.contains("Name\tMax Mustermann\n"), text);
                                    assertTrue(text.contains("Aktiv\tTRUE\n"), text);
                                    assertTrue(text.contains("Chat\tNein\n"), text);
                                    assertTrue(text.contains("Mail\tJa\n"), text);
                                    assertTrue(text.contains("Wiki\tNein\n"), text);
                                    assertTrue(text.contains("Gruppen\tOps-Team,Dev-Team,Admins\n"), text);
                                }
                            }
                        });
                        return null;
                    });
        }
    }
}
