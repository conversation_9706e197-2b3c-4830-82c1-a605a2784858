<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import { toast } from "../components/Toast";
  import { loadAllValue } from "../utils/rest.js";
  import { updatePatch } from "../utils/rest.js";
  import { removeValue } from "../utils/rest.js";
  import { printerPatch } from "../utils/rest.js";
  import { printerDelete } from "../utils/rest.js";
  import { Circle } from "svelte-loading-spinners";
  import Icon from "../components/Icon";
  import Checkbox from "../components/Checkbox";
  import TextField from "../components/TextField";
  import OrdnerEditor from "./OrdnerEditor.svelte";
  import OrdnerViewer from "./OrdnerViewer.svelte";

  let ordnerId = undefined;
  async function onOrdnerClicked(ordner) {
    ordnerId = ordner.id;
  }
  async function onOrdnerRemoveClicked(ordner) {
    ordnerId = ordner.id;
    await removeRepo(ordner);
    await removeOrdner(ordner);
  }
  let ordnerEditorCreate = false;
  async function onOrdnerEditorCreateClicked() {
    ordnerEditorCreate = true;
  }
  let ordnerEditorUpdate = false;
  async function onOrdnerEditorUpdateClicked(ordner) {
    ordnerId = ordner.id;
    ordnerEditorUpdate = true;
  }
  let ordnerListerOpened = false;
  async function onOrdnerListerClicked(ordner) {
    if (ordnerId !== ordner.id) {
      ordnerId = ordner.id;
      ordnerListerOpened = true;
      try {
        loading = true;
        await tick();
        await updateRepo(ordner);
        await reloadAllSeite(ordner);
      } finally {
        loading = false;
      }
    } else {
      ordnerListerOpened = !ordnerListerOpened;
    }
  }
  $: ordnerEditorDisabled =
    ordnerEditorCreate || ordnerEditorUpdate || ordnerListerOpened;

  let ordnerFilter;
  function ordnerFilterParameter() {
    if (!ordnerFilter) return "";
    return "&titel=" + encodeURIComponent(ordnerFilter);
  }
  function ordnerSortParameter() {
    return "?sort=titel";
  }
  async function onOrdnerFilterClicked() {
    try {
      loading = true;
      await tick();
      await reloadAllOrdner();
    } finally {
      loading = false;
    }
  }

  let allDateitypItem = [];
  let allOrdnerartItem = [];
  let loading = true;
  onMount(async () => {
    try {
      loading = true;
      allDateitypItem = await loadAllValue("/api/enum/dateityp");
      console.log(["onMount", allDateitypItem]);
      allOrdnerartItem = await loadAllValue("/api/enum/ordnerart");
      console.log(["onMount", allOrdnerartItem]);
      await reloadAllOrdner();
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  let allOrdner = [];
  function reloadAllOrdner() {
    const query = ordnerSortParameter() + ordnerFilterParameter();
    return loadAllValue("/api/ordner" + query)
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadAllOrdner", query, msg]);
        allOrdner = json;
      })
      .catch((err) => {
        console.log(["reloadAllOrdner", query, err]);
        allOrdner = [];
        toast.push(err.toString());
      });
  }
  function onCreateOrdner(ordner) {
    allOrdner = allOrdner.toSpliced(0, 0, ordner);
  }
  function onUpdateOrdner(ordner) {
    let index = allOrdner.findIndex((e) => e.id === ordner.id);
    if (index > -1) allOrdner = allOrdner.toSpliced(index, 1, ordner);
  }
  function onRemoveOrdner(ordner) {
    let index = allOrdner.findIndex((e) => e.id === ordner.id);
    if (index > -1) allOrdner = allOrdner.toSpliced(index, 1);
  }

  let allSeite = [];
  function reloadAllSeite(ordner) {
    return loadAllValue("/api/ordner/" + ordner.id + "/allSeite")
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadAllSeite", msg]);
        allSeite = json;
      })
      .catch((err) => {
        console.log(["reloadAllSeite", err]);
        allSeite = [];
        toast.push(err.toString());
      });
  }

  function updateRepo(ordner) {
    return printerPatch("/doc/ordner/" + ordner.id)
      .then((json) => {
        console.log(["updateRepo", ordner, json]);
      })
      .catch((err) => {
        console.log(["updateRepo", ordner, err]);
        toast.push(err.toString());
      });
  }
  function removeRepo(ordner) {
    return printerDelete("/doc/ordner/" + ordner.id)
      .then((json) => {
        console.log(["removeRepo", ordner, json]);
      })
      .catch((err) => {
        console.log(["removeRepo", ordner, err]);
        toast.push(err.toString());
      });
  }

  function updateOrdner(ordner) {
    return updatePatch("/api/ordner/" + ordner.id, ordner)
      .then((json) => {
        console.log(["updateOrdner", ordner, json]);
        onUpdateOrdner(json);
      })
      .catch((err) => {
        console.log(["updateOrdner", ordner, err]);
        toast.push(err.toString());
      });
  }
  function removeOrdner(ordner) {
    const text = ordner.titel;
    const hint = text.length > 20 ? text.substring(0, 20) + "..." : text;
    if (!confirm("Ordner '" + hint + "' wirklich löschen?")) return;
    return removeValue("/api/ordner" + "/" + ordner.id)
      .then((json) => {
        console.log(["removeOrdner", ordner, json]);
        onRemoveOrdner(json);
      })
      .catch((err) => {
        console.log(["removeOrdner", ordner, err]);
        toast.push(err.toString());
      });
  }
</script>

<h1 title="Liste der Ordnern, ggfs. gefiltert, jedes Element editierbar">
  Ordner
</h1>
<div class="flex flex-col gap-1 ml-2 mr-2">
  <form on:submit|preventDefault={onOrdnerFilterClicked}>
    <div class="flex flex-row gap-1 items-center pr-2">
      <div class="w-full">
        <TextField
          bind:value={ordnerFilter}
          label="Filter"
          placeholder="Bitte Filterkriterien eingeben"
        />
      </div>
      <div class="w-min">
        <Icon type="submit" name="search" outlined />
      </div>
    </div>
  </form>
  {#if loading}
    <div class="h-screen flex justify-center items-center">
      <Circle size="60" unit="px" duration="1s" />
    </div>
  {:else}
    <table class="table-fixed">
      <thead class="justify-between">
        <tr class="bg-title-100">
          <th class="px-2 py-3 table-cell">
            <span class="text-title-600 text-sm">Aktiv</span>
          </th>
          <th class="px-2 py-3 text-left w-full table-cell">
            <span class="text-title-600">Title</span>
          </th>
          <th class="px-2 py-3 text-right w-0 table-cell">
            <Icon
              on:click={() => onOrdnerEditorCreateClicked()}
              disabled={ordnerEditorDisabled}
              title="Ordner hinzufügen"
              name="add"
              outlined
            />
          </th>
        </tr>
      </thead>
      <tbody>
        {#if ordnerEditorCreate}
          <tr>
            <td class="px-2" colspan="3">
              <OrdnerEditor
                bind:visible={ordnerEditorCreate}
                on:create={(e) => onCreateOrdner(e.detail)}
                {allOrdnerartItem}
              />
            </td>
          </tr>
        {/if}
        {#each allOrdner as ordner, i}
          <tr
            on:click={(e) => onOrdnerClicked(ordner)}
            title={ordner.id}
            class:border-l-2={ordnerId === ordner.id}
            class:bg-gray-100={i % 2 === 1}
          >
            <td class="px-2 py-3 table-cell">
              <Checkbox
                bind:checked={ordner.aktiv}
                on:change={() => updateOrdner(ordner)}
              />
            </td>
            <td class="px-2 py-3 text-left table-cell">
              <div class="text-sm underline text-blue-600">
                <a href={"/ordner/" + ordner.id}>{ordner.titel}</a>
              </div>
            </td>
            <td class="px-2 py-3 table-cell">
              <div
                class="grid grid-cols-1 md:grid-cols-3 items-center gap-1 w-max"
              >
                <Icon
                  on:click={() => onOrdnerListerClicked(ordner)}
                  disabled={ordnerEditorDisabled && !ordnerListerOpened}
                  title="Seiten anzeigen"
                  name="list"
                  outlined
                />
                <Icon
                  on:click={() => onOrdnerRemoveClicked(ordner)}
                  disabled={ordnerEditorDisabled || ordner.aktiv}
                  title="Ordner löschen"
                  name="delete"
                  outlined
                />
                <Icon
                  on:click={() => onOrdnerEditorUpdateClicked(ordner)}
                  disabled={ordnerEditorDisabled}
                  title="Ordner bearbeiten"
                  name="edit"
                  outlined
                />
              </div>
            </td>
          </tr>
          {#if ordnerListerOpened && ordnerId === ordner.id}
            <tr>
              <td class="border-l-4 px-2" colspan="3">
                <OrdnerViewer {allSeite} />
              </td>
            </tr>
          {/if}
          {#if ordnerEditorUpdate && ordnerId === ordner.id}
            <tr>
              <td class="border-l-4 px-2" colspan="3">
                <OrdnerEditor
                  bind:visible={ordnerEditorUpdate}
                  on:update={(e) => onUpdateOrdner(e.detail)}
                  {allOrdnerartItem}
                  {ordner}
                />
              </td>
            </tr>
          {/if}
        {:else}
          <tr>
            <td class="px-2" colspan="3">Keine Ordner</td>
          </tr>
        {/each}
      </tbody>
    </table>
  {/if}
</div>
