<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import { toast } from "../components/Toast";
  import { loadAllValue } from "../utils/rest.js";
  import { loadOneValue } from "../utils/rest.js";
  import Button from "../components/Button";
  import AdresseEditor from "./AdresseEditor.svelte";

  export let adresseId;

  let allLandItem = [];
  let loading = true;
  onMount(async () => {
    try {
      loading = true;
      allLandItem = await loadAllValue("/api/enum/land");
      console.log(["onMount", allLandItem]);
      await reloadOneAdresse();
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  async function onChange() {
    try {
      loading = true;
      await tick();
      await reloadOneAdresse();
    } finally {
      loading = false;
    }
  }
  async function onCancel() {
    await reloadOneAdresse();
  }

  let adresse;
  async function reloadOneAdresse() {
    try {
      adresse = await loadOneValue("/api/adresse/" + adresseId);
      console.log(["reloadOneAdresse", adresseId, adresse]);
    } catch (err) {
      console.log(["reloadOneAdresse", adresseId, err]);
      toast.push(err.toString());
    }
  }
</script>

<div class="flex flex-col gap-1 ml-2 mr-2">
  {#if adresse}{#key adresse}
      <h1>{adresse.anschrift.strasse}</h1>
      <fieldset class="p-4 border-2">
        <legend class="text-xl">Adresse bearbeiten</legend>
        <AdresseEditor
          on:update={onChange}
          visible={true}
          autofocus={true}
          autoscroll={false}
          {adresse}
          {allLandItem}
        >
          <div slot="cancel">
            <Button type="reset" onclick={onCancel}>Verwerfen</Button>
          </div>
        </AdresseEditor>
      </fieldset>
    {/key}{/if}
</div>
