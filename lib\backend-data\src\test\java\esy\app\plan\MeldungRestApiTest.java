package esy.app.plan;

import esy.api.plan.Meldung;
import esy.api.plan.QMeldung;
import esy.api.plan.VorgangStatus;
import esy.app.EsyBackendConfiguration;
import esy.app.team.NutzerRepository;
import esy.auth.JwtRole;
import esy.auth.WithMockJwt;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.restdocs.RestDocumentationContextProvider;
import org.springframework.restdocs.RestDocumentationExtension;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.UUID;

import static esy.app.RestApiAssertions.assertRestApiSpecExists;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.restdocs.mockmvc.MockMvcRestDocumentation.document;
import static org.springframework.restdocs.mockmvc.MockMvcRestDocumentation.documentationConfiguration;
import static org.springframework.restdocs.operation.preprocess.Preprocessors.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@Tag("slow")
@SpringBootTest
@AutoConfigureMockMvc(addFilters = false)
@ContextConfiguration(classes = {EsyBackendConfiguration.class})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith({MockitoExtension.class, RestDocumentationExtension.class})
class MeldungRestApiTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private NutzerRepository nutzerRepository;

    @Autowired
    private MeldungRepository meldungRepository;

    @BeforeEach
    void setUp(final WebApplicationContext webApplicationContext,
               final RestDocumentationContextProvider restDocumentation) {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext)
                .apply(documentationConfiguration(restDocumentation))
                .alwaysDo(document("{method-name}",
                        preprocessRequest(prettyPrint()),
                        preprocessResponse(prettyPrint())))
                .build();
    }

    @Test
    @Order(1)
    void asciidoc() {
        assertRestApiSpecExists(Meldung.class);
    }

    @ParameterizedTest
    @ValueSource(strings = {"GET", "POST", "PUT", "PATCH", "DELETE"})
    @Order(1)
    void preflight(final String method) throws Exception {
        mockMvc.perform(options("/api/meldung")
                        .header("Access-Control-Request-Method", method)
                        .header("Access-Control-Request-Headers", "Content-Type")
                        .header("Origin", "http://localhost:5000")) // UI
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(header()
                        .exists("Access-Control-Allow-Origin"))
                .andExpect(header()
                        .exists("Access-Control-Allow-Methods"))
                .andExpect(header()
                        .exists("Access-Control-Allow-Headers"));
    }

    @Sql("/sql/nutzer.sql")
    @Test
    @Order(10)
    @WithMockJwt
    void getApiMeldungNoElement() throws Exception {
        mockMvc.perform(get("/api/meldung")
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(jsonPath("$.content")
                        .isArray())
                .andExpect(jsonPath("$.content[0]")
                        .doesNotExist());
    }

    @Test
    @Order(20)
    @WithMockJwt
    void postApiMeldung() throws Exception {
        final var titel = "Ausfall der Stromversorgung";
        assertFalse(meldungRepository.findOne(QMeldung.meldung.titel.eq(titel)).isPresent());
        mockMvc.perform(post("/api/meldung")
                        .content("{" +
                                "\"nutzerId\":\"a2222222-6ee8-4335-b12a-ef84794bd27a\"," +
                                "\"titel\":\"" + titel + "\"," +
                                "\"text\":\"Lorem ipsum dolor sit amet.\"," +
                                "\"termin\":\"2019-04-22\"," +
                                "\"aktiv\":\"false\"" +
                                "}")
                        .contentType(MediaType.APPLICATION_JSON)
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isCreated())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .string("ETag", "\"0\""))
                .andExpect(jsonPath("$.id")
                        .isNotEmpty())
                .andExpect(jsonPath("$.nutzerId")
                        .exists())
                .andExpect(jsonPath("$.titel")
                        .value(titel))
                .andExpect(jsonPath("$.text")
                        .isNotEmpty())
                .andExpect(jsonPath("$.termin")
                        .isNotEmpty())
                .andExpect(jsonPath("$.aktiv")
                        .value("false"));
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @Order(21)
    @WithMockJwt
    void postApiMeldungConflict(final boolean aktiv) throws Exception {
        final var titel = "Ausfall der Stromversorgung";
        assertTrue(meldungRepository.findOne(QMeldung.meldung.titel.eq(titel)).isPresent());
        mockMvc.perform(post("/api/meldung")
                        .content("{" +
                                "\"nutzerId\":\"a2222222-6ee8-4335-b12a-ef84794bd27a\"," +
                                "\"titel\":\"" + titel + "\"," +
                                "\"text\":\"Lorem ipsum dolor sit amet.\"," +
                                "\"termin\":\"2019-04-22\"," +
                                "\"aktiv\":\"" + aktiv + "\"" +
                                "}")
                        .contentType(MediaType.APPLICATION_JSON)
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isConflict());
    }

    @Test
    @Order(30)
    @WithMockJwt
    void putApiMeldungCreate() throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var titel = "Infrastruktur nicht sicher";
        assertFalse(meldungRepository.findById(UUID.fromString(uuid)).isPresent());
        mockMvc.perform(put("/api/meldung/" + uuid)
                        .content("{" +
                                "\"titel\":\"" + titel + "\"," +
                                "\"text\":\"At vero eos et accusam.\"," +
                                "\"termin\":\"2019-04-22\"," +
                                "\"aktiv\":\"false\"" +
                                "}")
                        .contentType(MediaType.APPLICATION_JSON)
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isCreated())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .string("ETag", "\"0\""))
                .andExpect(jsonPath("$.id")
                        .value(uuid))
                .andExpect(jsonPath("$.nutzerId")
                        .doesNotExist())
                .andExpect(jsonPath("$.titel")
                        .value(titel))
                .andExpect(jsonPath("$.text")
                        .isNotEmpty())
                .andExpect(jsonPath("$.termin")
                        .isNotEmpty())
                .andExpect(jsonPath("$.aktiv")
                        .value("false"));
    }

    @Test
    @Order(31)
    @WithMockJwt
    void putApiMeldungUpdate() throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var meldung = meldungRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(put("/api/meldung/" + meldung.getId())
                        .content("{" +
                                "\"titel\":\"" + meldung.getTitel() + "\"," +
                                "\"text\":\"" + meldung.getText() + "\"," +
                                "\"termin\":\"2019-04-22\"," +
                                "\"aktiv\":\"true\"" +
                                "}")
                        .contentType(MediaType.APPLICATION_JSON)
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + meldung.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .string("ETag", "\"1\""))
                .andExpect(jsonPath("$.id")
                        .value(meldung.getId().toString()))
                .andExpect(jsonPath("$.nutzerId")
                        .doesNotExist())
                .andExpect(jsonPath("$.titel")
                        .value(meldung.getTitel()))
                .andExpect(jsonPath("$.text")
                        .value(meldung.getText()))
                .andExpect(jsonPath("$.termin")
                        .value(meldung.getTermin().format(Meldung.DATE_FORMATTER)))
                .andExpect(jsonPath("$.aktiv")
                        .value("true"));;
    }

    @Test
    @Order(32)
    @WithMockJwt
    void patchApiMeldungNutzer() throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var meldung = meldungRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(patch("/api/meldung/" + meldung.getId())
                        .content("{" +
                                "\"nutzerId\":\"a2222222-6ee8-4335-b12a-ef84794bd27a\"" +
                                "}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + meldung.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(meldung.getId().toString()))
                .andExpect(jsonPath("$.nutzerId")
                        .exists());
    }

    @Test
    @Order(33)
    @WithMockJwt
    void patchApiMeldungNutzerNull() throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var meldung = meldungRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(patch("/api/meldung/" + meldung.getId())
                        .content("{" +
                                "\"nutzerId\":null" +
                                "}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + meldung.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(meldung.getId().toString()))
                .andExpect(jsonPath("$.nutzerId")
                        .doesNotExist());
    }

    @ParameterizedTest
    @ValueSource(booleans = {false, true})
    @Order(40)
    @WithMockJwt
    void patchApiMeldungAktiv(final boolean aktiv) throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var meldung = meldungRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        assertNotEquals(aktiv, meldung.isAktiv());
        mockMvc.perform(patch("/api/meldung/" + meldung.getId())
                        .content("{" +
                                "\"aktiv\":\"" + aktiv + "\"" +
                                "}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + meldung.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(meldung.getId().toString()))
                .andExpect(jsonPath("$.aktiv")
                        .value(aktiv));
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "Infrastruktur unsicher",
            "Infrastruktur nicht sicher"
    })
    @Order(41)
    @WithMockJwt
    void patchApiMeldungTitel(final String titel) throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var meldung = meldungRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        assertNotEquals(titel, meldung.getTitel());
        mockMvc.perform(patch("/api/meldung/" + meldung.getId())
                        .content("{" +
                                "\"titel\":\"" + titel + "\"" +
                                "}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + meldung.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(meldung.getId().toString()))
                .andExpect(jsonPath("$.titel")
                        .value(titel));
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "Lorem ipsum.\nAt vero eos et accusam.",
            "At vero eos et accusam."
    })
    @Order(41)
    @WithMockJwt
    void patchApiMeldungText(final String text) throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var meldung = meldungRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        assertNotEquals(text, meldung.getText());
        mockMvc.perform(patch("/api/meldung/" + meldung.getId())
                        .content("{" +
                                "\"text\":\"" + text.replace("\n", "\\n") + "\"" +
                                "}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + meldung.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(meldung.getId().toString()))
                .andExpect(jsonPath("$.text")
                        .value(text));
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "2000-01-01",
            "2031-04-22",
            "2019-04-22"
    })
    @Order(43)
    @WithMockJwt
    void patchApiMeldungTermin(final String datum) throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var meldung = meldungRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        assertNotEquals(LocalDate.parse(datum), meldung.getTermin());
        mockMvc.perform(patch("/api/meldung/" + meldung.getId())
                        .content("{" +
                                "\"termin\":\"" + datum + "\"" +
                                "}")
                        .contentType(MediaType.parseMediaType("application/merge-patch+json"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + meldung.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(meldung.getId().toString()))
                .andExpect(jsonPath("$.termin")
                        .value(datum));
    }

    @Test
    @Order(44)
    @WithMockJwt
    void patchApiMeldungStatusTransition() throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var meldung = meldungRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        assertNotEquals(VorgangStatus.X, meldung.getStatus());
        mockMvc.perform(patch("/api/meldung/" + meldung.getId() + "/" + VorgangStatus.X)
                        .contentType(MediaType.parseMediaType("plain/text"))
                        .characterEncoding(StandardCharsets.UTF_8)
                        .accept(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.IF_MATCH, "\"" + meldung.getVersion() + "\""))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .exists("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(meldung.getId().toString()))
                .andExpect(jsonPath("$.aktiv")
                        .value("false"))
                .andExpect(jsonPath("$.termin")
                        .exists());
    }

    @Test
    @Order(50)
    @WithMockJwt
    void getApiMeldungById() throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        final var meldung = meldungRepository.findById(UUID.fromString(uuid))
                .orElseThrow();
        mockMvc.perform(get("/api/meldung/" + meldung.getId())
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .string("ETag", "\"13\""))
                .andExpect(jsonPath("$.id")
                        .value(meldung.getId().toString()))
                .andExpect(jsonPath("$.nutzerId")
                        .doesNotExist())
                .andExpect(jsonPath("$.titel")
                        .value(meldung.getTitel()))
                .andExpect(jsonPath("$.text")
                        .value(meldung.getText()))
                .andExpect(jsonPath("$.aktiv")
                        .value(meldung.isAktiv()));
    }

    @Test
    @Order(51)
    @WithMockJwt
    void getApiMeldungByIdNotFound() throws Exception {
        final var uuid = "deadbeef-dead-beef-dead-beefdeadbeef";
        assertFalse(meldungRepository.findById(UUID.fromString(uuid)).isPresent());
        mockMvc.perform(get("/api/meldung/" + uuid)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isNotFound());
    }

    @Test
    @Order(60)
    @WithMockJwt
    void deleteApiMeldungForbidden() throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        assertTrue(meldungRepository.findById(UUID.fromString(uuid)).isPresent());
        mockMvc.perform(delete("/api/meldung/" + uuid)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isForbidden());
    }

    @Test
    @Order(61)
    @WithMockJwt(roles = {JwtRole.VERWALTUNG})
    void deleteApiMeldung() throws Exception {
        final var uuid = "01901b6a-bead-d5e9-7f52-f0d5f0726aea";
        assertTrue(meldungRepository.findById(UUID.fromString(uuid)).isPresent());
        mockMvc.perform(delete("/api/meldung/" + uuid)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(header()
                        .doesNotExist("ETag"))
                .andExpect(jsonPath("$.id")
                        .value(uuid));
    }

    @Test
    @Order(62)
    @WithMockJwt(roles = {JwtRole.VERWALTUNG})
    void deleteApiMeldungNotFound() throws Exception {
        final var uuid = "deadbeef-dead-beef-dead-beefdeadbeef";
        assertFalse(meldungRepository.findById(UUID.fromString(uuid)).isPresent());
        mockMvc.perform(delete("/api/meldung/" + uuid)
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isNotFound());
    }

    @Test
    @Order(90)
    @WithMockJwt
    void getApiMeldung() throws Exception {
        mockMvc.perform(get("/api/meldung")
                        .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status()
                        .isOk())
                .andExpect(content()
                        .contentType("application/json"))
                .andExpect(header()
                        .exists("Vary"))
                .andExpect(jsonPath("$.content")
                        .isArray())
                .andExpect(jsonPath("$.content[0]")
                        .exists())
                .andExpect(jsonPath("$.content[1]")
                        .doesNotExist());
    }

    @Test
    @Order(99)
    @Transactional
    @Rollback(false)
    void cleanup() {
        meldungRepository.findAll().forEach(meldungRepository::delete);
        nutzerRepository.findAll().forEach(nutzerRepository::delete);
        assertEquals(0, meldungRepository.count());
        assertEquals(0, nutzerRepository.count());
    }
}
