<script>
  import Chip from "./components/Chip";
  import { loginUrl } from "./utils/url.js";
  export let provider;
  let openIn;
  function onClick() {
    try {
      openIn.href = loginUrl(provider);
      openIn.click();
    } finally {
      openIn.href = undefined;
    }
  }
</script>

<Chip icon="login" onclick={onClick}>Login mit {provider}</Chip>
<!-- svelte-ignore a11y-missing-attribute -->
<a aria-label="Login" class="hidden" target="_self" bind:this={openIn}
  >&nbsp;
</a>
