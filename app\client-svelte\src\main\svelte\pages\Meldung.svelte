<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import { toast } from "../components/Toast";
  import { loadAllValue } from "../utils/rest.js";
  import { updatePatch } from "../utils/rest.js";
  import { removeValue } from "../utils/rest.js";
  import { Circle } from "svelte-loading-spinners";
  import Icon from "../components/Icon";
  import Checkbox from "../components/Checkbox";
  import TextField from "../components/TextField";
  import MeldungEditor from "./MeldungEditor.svelte";

  let meldungId = undefined;
  async function onMeldungClicked(meldung) {
    meldungId = meldung.id;
  }
  async function onMeldungRemoveClicked(meldung) {
    meldungId = meldung.id;
    await removeMeldung(meldung);
  }
  let meldungEditorCreate = false;
  async function onMeldungEditorCreateClicked() {
    meldungEditorCreate = true;
  }
  let meldungEditorUpdate = false;
  async function onMeldungEditorUpdateClicked(meldung) {
    meldungId = meldung.id;
    meldungEditorUpdate = true;
  }
  $: meldungEditorDisabled = meldungEditorCreate || meldungEditorUpdate;

  let meldungFilter;
  function meldungFilterParameter() {
    if (!meldungFilter) return "";
    return "&titel=" + encodeURIComponent(meldungFilter);
  }
  function meldungSortParameter() {
    return "?sort=titel";
  }
  async function onMeldungFilterClicked() {
    try {
      loading = true;
      await tick();
      await reloadAllMeldung();
    } finally {
      loading = false;
    }
  }

  let allNutzerItem = [];
  let loading = true;
  onMount(async () => {
    try {
      loading = true;
      allNutzerItem = await loadAllValue("/api/nutzer/search/findAllItem");
      console.log(["onMount", allNutzerItem]);
      await reloadAllMeldung();
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  let allMeldung = [];
  function reloadAllMeldung() {
    const query = meldungSortParameter() + meldungFilterParameter();
    return loadAllValue("/api/meldung" + query)
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadAllMeldung", query, msg]);
        allMeldung = json;
      })
      .catch((err) => {
        console.log(["reloadAllMeldung", query, err]);
        allMeldung = [];
        toast.push(err.toString());
      });
  }
  function onCreateMeldung(meldung) {
    allMeldung = allMeldung.toSpliced(0, 0, meldung);
  }
  function onUpdateMeldung(meldung) {
    let index = allMeldung.findIndex((e) => e.id === meldung.id);
    if (index > -1) allMeldung = allMeldung.toSpliced(index, 1, meldung);
  }
  function onRemoveMeldung(meldung) {
    let index = allMeldung.findIndex((e) => e.id === meldung.id);
    if (index > -1) allMeldung = allMeldung.toSpliced(index, 1);
  }

  function updateMeldung(meldung) {
    return updatePatch("/api/meldung/" + meldung.id, meldung)
      .then((json) => {
        console.log(["updateMeldung", meldung, json]);
        onUpdateMeldung(json);
      })
      .catch((err) => {
        console.log(["updateMeldung", meldung, err]);
        toast.push(err.toString());
      });
  }
  function removeMeldung(meldung) {
    const text = meldung.titel;
    const hint = text.length > 20 ? text.substring(0, 20) + "..." : text;
    if (!confirm("Meldung '" + hint + "' wirklich löschen?")) return;
    return removeValue("/api/meldung" + "/" + meldung.id)
      .then((json) => {
        console.log(["removeMeldung", meldung, json]);
        onRemoveMeldung(json);
      })
      .catch((err) => {
        console.log(["removeMeldung", meldung, err]);
        toast.push(err.toString());
      });
  }
</script>

<h1 title="Liste der Risiken, ggfs. gefiltert, jedes Element editierbar">
  Meldung
</h1>
<div class="flex flex-col gap-1 ml-2 mr-2">
  <form on:submit|preventDefault={onMeldungFilterClicked}>
    <div class="flex flex-row gap-1 items-center pr-2">
      <div class="w-full">
        <TextField
          bind:value={meldungFilter}
          label="Filter"
          placeholder="Bitte Filterkriterien eingeben"
        />
      </div>
      <div class="w-min">
        <Icon type="submit" name="search" outlined />
      </div>
    </div>
  </form>
  {#if loading}
    <div class="h-screen flex justify-center items-center">
      <Circle size="60" unit="px" duration="1s" />
    </div>
  {:else}
    <table class="table-fixed">
      <thead class="justify-between">
        <tr class="bg-title-100">
          <th class="px-2 py-3 table-cell">
            <span class="text-title-600 text-sm">Aktiv</span>
          </th>
          <th class="px-2 py-3 text-left w-full table-cell">
            <span class="text-title-600">Title</span>
          </th>
          <th class="px-2 py-3 text-right w-0 table-cell">
            <Icon
              onclick={() => onMeldungEditorCreateClicked()}
              disabled={meldungEditorDisabled}
              title="Meldung hinzufügen"
              name="add"
              outlined
            />
          </th>
        </tr>
      </thead>
      <tbody>
        {#if meldungEditorCreate}
          <tr>
            <td class="px-2" colspan="3">
              <MeldungEditor
                bind:visible={meldungEditorCreate}
                on:create={(e) => onCreateMeldung(e.detail)}
                {allNutzerItem}
              />
            </td>
          </tr>
        {/if}
        {#each allMeldung as meldung, i}
          <tr
            on:click={(e) => onMeldungClicked(meldung)}
            title={meldung.id}
            class:border-l-2={meldungId === meldung.id}
            class:bg-gray-100={i % 2 === 1}
          >
            <td class="px-2 py-3 table-cell">
              <Checkbox
                bind:checked={meldung.aktiv}
                on:change={() => updateMeldung(meldung)}
              />
            </td>
            <td class="px-2 py-3 text-left table-cell">
              <div class="text-sm underline text-blue-600">
                <a href={"/meldung/" + meldung.id}>{meldung.titel}</a>
              </div>
            </td>
            <td class="px-2 py-3 table-cell">
              <div
                class="grid grid-cols-1 md:grid-cols-2 items-center gap-1 w-max"
              >
                <Icon
                  onclick={() => onMeldungRemoveClicked(meldung)}
                  disabled={meldungEditorDisabled || meldung.aktiv}
                  title="Meldung löschen"
                  name="delete"
                  outlined
                />
                <Icon
                  onclick={() => onMeldungEditorUpdateClicked(meldung)}
                  disabled={meldungEditorDisabled}
                  title="Meldung bearbeiten"
                  name="edit"
                  outlined
                />
              </div>
            </td>
          </tr>
          {#if meldungEditorUpdate && meldungId === meldung.id}
            <tr>
              <td class="border-l-4 px-2" colspan="3">
                <MeldungEditor
                  bind:visible={meldungEditorUpdate}
                  on:update={(e) => onUpdateMeldung(e.detail)}
                  {allNutzerItem}
                  {meldung}
                />
              </td>
            </tr>
          {/if}
        {:else}
          <tr>
            <td class="px-2" colspan="3">Keine Risiken</td>
          </tr>
        {/each}
      </tbody>
    </table>
  {/if}
</div>
