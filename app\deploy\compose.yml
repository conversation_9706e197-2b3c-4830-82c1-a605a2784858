services:
  client:
    hostname: client
    image: ${COMPOSE_PROJECT_NAME}/client-svelte
    ports:
      - 5000:5000
  spooler:
    hostname: spooler
    image: ${COMPOSE_PROJECT_NAME}/spooler
    ports:
      - 8631:631
      - 8082:8082
    environment:
      - SERVER_PORT=8082
      - BACKEND_URL=http://backend:8080
      - PRINTER_URL=http://printer:8081
      - CUPS_PASSWORD=P@ssw0rd
      - SMTP_HOST=mailhog
      - SMTP_PORT=1025
      - SMTP_USERNAME=sa
      - SMTP_PASSWORD=P@ssw0rd
  mailhog:
    hostname: mailhog
    image: mailhog/mailhog@sha256:8d76a3d4ffa32a3661311944007a415332c4bb855657f4f6c57996405c009bea
    ports:
      - 8025:8025
      - 1025:1025
  printer:
    hostname: printer
    image: ${COMPOSE_PROJECT_NAME}/printer
    ports:
      - 5006:5006
      - 8081:8081
    environment:
      - _JAVA_OPTIONS=-Xdebug -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5006
      - SERVER_PORT=8081
      - BACKEND_URL=http://backend:8080
      - SPRING_PROFILES_ACTIVE=dev
      - SCHEDULE_STORAGERATEMS=60000
      - SCHEDULE_STORAGETIMEOUTMS=300000
      - GITHUB_TOKEN=${GITHUB_TOKEN-}
      - GITLAB_TOKEN=${GITLAB_TOKEN-}
  backend:
    hostname: backend
    image: ${COMPOSE_PROJECT_NAME}/backend
    ports:
      - 5005:5005
      - 8080:8080
    environment:
      - _JAVA_OPTIONS=-Xdebug -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005
      - SERVER_PORT=8080
      - BACKEND_EMAIL=${<EMAIL>}
      - BACKEND_LOGIN=${BACKEND_LOGIN-bruckbauer}
      - BACKEND_TITLE=${BACKEND_TITLE-Robert Bruckbauer}
      - SPRING_LIQUIBASE_DEFAULTSCHEMA=${COMPOSE_PROJECT_NAME}
      - SPRING_DATASOURCE_URL=********************************************************=${COMPOSE_PROJECT_NAME}
      - SPRING_DATASOURCE_USERNAME=sa
      - SPRING_DATASOURCE_PASSWORD=P@ssw0rd
      - SPRING_PROFILES_ACTIVE=dev
      - OAUTH2_LOGIN=${OAUTH2_LOGIN-}
      - GITHUB_TOKEN=${GITHUB_TOKEN-}
      - GITLAB_TOKEN=${GITLAB_TOKEN-}
      - JIRA_TOKEN=${JIRA_TOKEN-}
    depends_on:
      - postgres17
  postgres17:
    hostname: postgres17
    image: postgres:17@sha256:29e0bb09c8e7e7fc265ea9f4367de9622e55bae6b0b97e7cce740c2d63c2ebc0
    ports:
      - 5432:5432
    volumes:
      - postgres17:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=postgres
      - POSTGRES_USER=sa
      - POSTGRES_PASSWORD=P@ssw0rd
volumes:
  postgres17:
