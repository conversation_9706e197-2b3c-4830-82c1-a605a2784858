<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import Button from "../components/Button";
  import { toast } from "../components/Toast";
  import { loadOneValue } from "../utils/rest.js";
  import GefahrEditor from "./GefahrEditor.svelte";

  export let gefahrId;

  let loading = true;
  onMount(async () => {
    try {
      loading = true;
      await reloadOneGefahr();
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  async function onChange() {
    try {
      loading = true;
      await tick();
      await reloadOneGefahr();
    } finally {
      loading = false;
    }
  }
  async function onCancel() {
    await reloadOneGefahr();
  }

  let gefahr;
  function reloadOneGefahr() {
    return loadOneValue("/api/gefahr/" + gefahrId)
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadOneGefahr", msg]);
        gefahr = json;
      })
      .catch((err) => {
        console.log(["reloadOneGefahr", err]);
        gefahr = {};
        toast.push(err.toString());
      });
  }
</script>

<div class="flex flex-col gap-1 ml-2 mr-2">
  {#if gefahr}{#key gefahr}
      <h1>{gefahr.name}</h1>
      <fieldset class="p-4 border-2">
        <legend class="text-xl">Gefahr bearbeiten</legend>
        <GefahrEditor
          on:update={onChange}
          visible={true}
          autofocus={true}
          autoscroll={false}
          {gefahr}
        >
          <div slot="cancel">
            <Button type="reset" onclick={onCancel}>Verwerfen</Button>
          </div>
        </GefahrEditor>
      </fieldset>
    {/key}{/if}
</div>
