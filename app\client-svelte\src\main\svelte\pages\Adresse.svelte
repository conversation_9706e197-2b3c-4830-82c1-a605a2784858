<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import { toast } from "../components/Toast";
  import { loadAllValue } from "../utils/rest.js";
  import { updatePatch } from "../utils/rest.js";
  import { removeValue } from "../utils/rest.js";
  import { Circle } from "svelte-loading-spinners";
  import Icon from "../components/Icon";
  import Checkbox from "../components/Checkbox";
  import TextField from "../components/TextField";
  import AdresseEditor from "./AdresseEditor.svelte";

  let allLandItem = [];
  let loading = true;
  onMount(async () => {
    try {
      loading = true;
      allLandItem = await loadAllValue("/api/enum/land");
      console.log(["onMount", allLandItem]);
      await reloadAllAdresse();
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  let adresseId = undefined;
  function onAdresseClicked(adresse) {
    adresseId = adresse.id;
  }
  async function onAdresseRemoveClicked(adresse) {
    adresseId = adresse.id;
    await removeAdresse(adresse);
  }
  let adresseEditorCreate = false;
  async function onAdresseEditorCreateClicked() {
    adresseEditorCreate = true;
  }
  let adresseEditorUpdate = false;
  async function onAdresseEditorUpdateClicked(adresse) {
    adresseId = adresse.id;
    adresseEditorUpdate = true;
  }
  $: adresseEditorDisabled = adresseEditorCreate || adresseEditorUpdate;

  let anschriftFilter;
  function anschriftFilterParameter() {
    if (!anschriftFilter) return "";
    return "&anschrift=" + encodeURIComponent(anschriftFilter);
  }
  function anschriftSortParameter() {
    return "?sort=anschrift.strasse,anschrift.ort";
  }
  async function onAdresseFilterClicked() {
    try {
      loading = true;
      await tick();
      await reloadAllAdresse();
    } finally {
      loading = false;
    }
  }

  let allAdresse = [];
  function onCreateAdresse(adresse) {
    allAdresse = allAdresse.toSpliced(0, 0, adresse);
  }
  function onUpdateAdresse(adresse) {
    let index = allAdresse.findIndex((e) => e.id === adresse.id);
    if (index > -1) allAdresse = allAdresse.toSpliced(index, 1, adresse);
  }
  function onRemoveAdresse(adresse) {
    let index = allAdresse.findIndex((e) => e.id === adresse.id);
    if (index > -1) allAdresse = allAdresse.toSpliced(index, 1);
  }
  function reloadAllAdresse() {
    const query = anschriftSortParameter() + anschriftFilterParameter();
    return loadAllValue("/api/adresse" + query)
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadAllAdresse", query, msg]);
        allAdresse = json;
      })
      .catch((err) => {
        console.log(["reloadAllAdresse", query, err]);
        allAdresse = [];
        toast.push(err.toString());
      });
  }

  function updateAdresse(adresse) {
    return updatePatch("/api/adresse/" + adresse.id, adresse)
      .then((json) => {
        console.log(["updateAdresse", adresse, json]);
        onUpdateAdresse(json);
      })
      .catch((err) => {
        console.log(["updateAdresse", adresse, err]);
        toast.push(err.toString());
      });
  }
  function removeAdresse(adresse) {
    const text =
      adresse.anschrift.strasse +
      ", " +
      adresse.anschrift.plz +
      " " +
      adresse.anschrift.ort +
      ", " +
      adresse.anschrift.land;
    const hint = text.length > 20 ? text.substring(0, 20) + "..." : text;
    if (!confirm("Adresse '" + hint + "' wirklich löschen?")) return;
    return removeValue("/api/adresse/" + adresse.id)
      .then((json) => {
        console.log(["removeAdresse", adresse, json]);
        onRemoveAdresse(json);
      })
      .catch((err) => {
        console.log(["removeAdresse", adresse, err]);
        toast.push(err.toString());
      });
  }
</script>

<h1 title="Liste der Adressen, ggfs. gefiltert, jedes Element editierbar">
  Adresse
</h1>
<div class="flex flex-col gap-1 ml-2 mr-2">
  <form on:submit|preventDefault={onAdresseFilterClicked}>
    <div class="flex flex-row gap-1 items-center pr-2">
      <div class="w-full">
        <TextField
          bind:value={anschriftFilter}
          label="Filter"
          placeholder="Bitte Filterkriterien eingeben"
          disabled={adresseEditorDisabled}
        />
      </div>
      <div class="w-min">
        <Icon type="submit" name="search" outlined />
      </div>
    </div>
  </form>
  {#if loading}
    <div class="h-screen flex justify-center items-center">
      <Circle size="60" unit="px" duration="1s" />
    </div>
  {:else}
    <table class="table-fixed">
      <thead class="justify-between">
        <tr class="bg-title-100">
          <th class="px-2 py-3 table-cell">
            <span class="text-title-600 text-sm">Aktiv</span>
          </th>
          <th class="px-2 py-3 text-left w-full table-cell">
            <span class="text-title-600">Text</span>
          </th>
          <th class="px-2 py-3 text-right w-0 table-cell">
            <Icon
              on:click={() => onAdresseEditorCreateClicked()}
              disabled={adresseEditorDisabled}
              title="Adresse hinzufügen"
              name="add"
              outlined
            />
          </th>
        </tr>
      </thead>
      <tbody>
        {#if adresseEditorCreate}
          <tr>
            <td class="px-2" colspan="3">
              <AdresseEditor
                bind:visible={adresseEditorCreate}
                on:create={(e) => onCreateAdresse(e.detail)}
                {allLandItem}
              />
            </td>
          </tr>
        {/if}
        {#each allAdresse as adresse, i}
          <tr
            on:click={(e) => onAdresseClicked(adresse)}
            title={adresse.id}
            class:border-l-2={adresseId === adresse.id}
            class:bg-gray-100={i % 2 === 1}
          >
            <td class="px-2 py-3 table-cell">
              <Checkbox
                bind:checked={adresse.aktiv}
                on:change={() => updateAdresse(adresse)}
              />
            </td>
            <td class="px-2 py-3 text-left table-cell">
              <div class="text-sm underline text-blue-600">
                <a href={"/adresse/" + adresse.id}>{adresse.text}</a>
              </div>
            </td>
            <td class="px-2 py-3 table-cell">
              <div
                class="grid grid-cols-1 md:grid-cols-2 items-center gap-1 w-max"
              >
                <Icon
                  on:click={() => onAdresseRemoveClicked(adresse)}
                  disabled={adresseEditorDisabled || adresse.aktiv}
                  title="Adresse löschen"
                  name="delete"
                  outlined
                />
                <Icon
                  on:click={() => onAdresseEditorUpdateClicked(adresse)}
                  disabled={adresseEditorDisabled}
                  title="Adresse bearbeiten"
                  name="edit"
                  outlined
                />
              </div>
            </td>
          </tr>
          {#if adresseEditorUpdate && adresseId === adresse.id}
            <tr>
              <td class="border-l-4 px-2" colspan="3">
                <AdresseEditor
                  bind:visible={adresseEditorUpdate}
                  on:update={(e) => onUpdateAdresse(e.detail)}
                  {adresse}
                  {allLandItem}
                />
              </td>
            </tr>
          {/if}
        {:else}
          <tr>
            <td class="px-2" colspan="3">Keine Adressen</td>
          </tr>
        {/each}
      </tbody>
    </table>
  {/if}
</div>
