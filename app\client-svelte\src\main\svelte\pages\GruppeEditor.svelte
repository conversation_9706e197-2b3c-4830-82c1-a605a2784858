<script>
  import { onMount } from "svelte";
  import { createEventDispatcher } from "svelte";
  import { toast } from "../components/Toast";
  import { createValue } from "../utils/rest.js";
  import { updatePatch } from "../utils/rest.js";
  import Button from "../components/Button";
  import Checkbox from "../components/Checkbox";
  import TextField from "../components/TextField";

  export let visible = false;
  export let autofocus = true;
  export let autoscroll = true;
  export let gruppe = undefined;

  let focusOn;
  let bottomDiv;
  onMount(async () => {
    console.log(["onMount", autofocus, autoscroll]);
    if (autofocus) focusOn.focus();
    if (autoscroll) bottomDiv.scrollIntoView(false);
  });

  let clicked = false;
  let showUpdate;
  let newGruppe = {
    name: undefined,
    verwaltung: false,
    aktiv: true,
  };

  $: if (gruppe) onChange();
  function onChange() {
    showUpdate = gruppe.id;
    newGruppe = {
      id: gruppe.id,
      name: gruppe.name,
      verwaltung: gruppe.verwaltung,
      aktiv: gruppe.aktiv,
      version: gruppe.version,
    };
    console.log(["onChange", newGruppe]);
  }

  async function onSubmit() {
    try {
      clicked = true;
      if (showUpdate) {
        await updateGruppe();
      } else {
        await createGruppe();
      }
    } finally {
      clicked = false;
    }
  }
  async function onCancel() {
    visible = false;
  }

  const dispatch = createEventDispatcher();
  function createGruppe() {
    return createValue("/api/gruppe", newGruppe)
      .then((json) => {
        console.log(["createGruppe", newGruppe, json]);
        visible = false;
        dispatch("create", json);
      })
      .catch((err) => {
        console.log(["createGruppe", newGruppe, err]);
        toast.push(err.toString());
      });
  }
  function updateGruppe() {
    return updatePatch("/api/gruppe" + "/" + newGruppe.id, newGruppe)
      .then((json) => {
        console.log(["updateGruppe", newGruppe, json]);
        visible = false;
        dispatch("update", json);
      })
      .catch((err) => {
        console.log(["updateGruppe", newGruppe, err]);
        toast.push(err.toString());
      });
  }
</script>

<form on:submit|preventDefault={onSubmit} on:reset|preventDefault={onCancel}>
  <div class="flex flex-col gap-1">
    <div class="grow">
      <TextField
        bind:this={focusOn}
        bind:value={newGruppe.name}
        required
        label="Name"
        placeholder="Bitte einen Namen eingeben"
      />
    </div>
    <div class="shrink">
      <div class="pl-4 flex flex-row gap-1">
        <span>Mit erweiterten Rechten</span>
        <Checkbox bind:checked={newGruppe.verwaltung} />
      </div>
    </div>
  </div>
  <div class="py-4 flex flex-row gap-1 items-baseline">
    <div class="flex-initial">
      <Button type="submit">Ok</Button>
    </div>
    <div class="flex-initial">
      <slot name="cancel">
        <Button type="reset">Abbrechen</Button>
      </slot>
    </div>
  </div>
</form>

<div class="h-0" bind:this={bottomDiv}>&nbsp;</div>

{#if import.meta.env.DEV}
  <details tabindex="-1">
    <summary>JSON</summary>
    <pre>{JSON.stringify(newGruppe, null, 2)}</pre>
  </details>
{/if}
