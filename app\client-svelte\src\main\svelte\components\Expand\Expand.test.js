import { describe, it, expect } from 'vitest';
import { render } from '@testing-library/svelte';
import Expand from './Expand.svelte';

describe('Expand Component with Render Snippets', () => {
  it('renders with title and children snippets', () => {
    const { getByText, container } = render(Expand, {
      props: {
        title: () => 'Test Title',
        children: () => 'Test Content'
      }
    });

    // Check that the details element exists
    const details = container.querySelector('details');
    expect(details).toBeTruthy();

    // Check that the summary contains the title
    const summary = container.querySelector('summary');
    expect(summary).toBeTruthy();
    expect(getByText('Test Title')).toBeTruthy();

    // Check that the content is rendered
    expect(getByText('Test Content')).toBeTruthy();
  });

  it('applies correct CSS classes to summary', () => {
    const { container } = render(Expand, {
      props: {
        title: () => 'Title',
        children: () => 'Content'
      }
    });

    const summary = container.querySelector('summary');
    expect(summary?.className).toBe('pl-4 pt-2 text-xs text-label-600 uppercase');
  });

  it('can be expanded and collapsed', () => {
    const { container } = render(Expand, {
      props: {
        title: () => 'Expandable Title',
        children: () => 'Hidden Content'
      }
    });

    const details = container.querySelector('details');
    expect(details).toBeTruthy();
    
    // Initially should be closed
    expect(details?.open).toBe(false);
    
    // The content should still be in the DOM (details element behavior)
    expect(container.textContent).toContain('Hidden Content');
  });

  it('works with complex snippet content', () => {
    const { container } = render(Expand, {
      props: {
        title: () => {
          const span = document.createElement('span');
          span.textContent = 'Complex Title';
          span.className = 'custom-class';
          return span;
        },
        children: () => {
          const div = document.createElement('div');
          div.innerHTML = '<p>Complex <strong>content</strong></p>';
          return div;
        }
      }
    });

    expect(container.textContent).toContain('Complex Title');
    expect(container.textContent).toContain('Complex content');
    expect(container.querySelector('strong')).toBeTruthy();
  });
});
