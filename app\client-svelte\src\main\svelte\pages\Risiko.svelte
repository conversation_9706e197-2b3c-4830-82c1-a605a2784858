<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import { toast } from "../components/Toast";
  import { loadAllValue } from "../utils/rest.js";
  import { updatePatch } from "../utils/rest.js";
  import { removeValue } from "../utils/rest.js";
  import { Circle } from "svelte-loading-spinners";
  import Icon from "../components/Icon";
  import Checkbox from "../components/Checkbox";
  import TextField from "../components/TextField";
  import RisikoEditor from "./RisikoEditor.svelte";
  import RisikoGefahr from "./RisikoGefahr.svelte";
  import RisikoProzess from "./RisikoProzess.svelte";

  let risikoId = undefined;
  async function onRisikoClicked(risiko) {
    risikoId = risiko.id;
  }
  async function onRisikoRemoveClicked(risiko) {
    risikoId = risiko.id;
    await removeRisiko(risiko);
  }
  let risikoEditorCreate = false;
  async function onRisikoEditorCreateClicked() {
    risikoEditorCreate = true;
  }
  let risikoEditorUpdate = false;
  async function onRisikoEditorUpdateClicked(risiko) {
    risikoId = risiko.id;
    risikoEditorUpdate = true;
    risikoGefahrUpdate = false;
    risikoProzessUpdate = false;
  }
  let risikoGefahrUpdate = false;
  async function onRisikoGefahrUpdateClicked(risiko) {
    risikoId = risiko.id;
    risikoEditorUpdate = false;
    risikoGefahrUpdate = true;
    risikoProzessUpdate = false;
  }
  let risikoProzessUpdate = false;
  async function onRisikoProzessUpdateClicked(risiko) {
    risikoId = risiko.id;
    risikoEditorUpdate = false;
    risikoGefahrUpdate = false;
    risikoProzessUpdate = true;
  }
  $: risikoEditorDisabled =
    risikoEditorCreate ||
    risikoEditorUpdate ||
    risikoGefahrUpdate ||
    risikoProzessUpdate;

  let risikoFilter;
  function risikoFilterParameter() {
    if (!risikoFilter) return "";
    return "&titel=" + encodeURIComponent(risikoFilter);
  }
  function risikoSortParameter() {
    return "?sort=titel";
  }
  async function onRisikoFilterClicked() {
    try {
      loading = true;
      await tick();
      await reloadAllRisiko();
    } finally {
      loading = false;
    }
  }

  let allNutzerItem = [];
  let allGefahrItem = [];
  let allProzessItem = [];
  let allTerminserieItem = [];
  let loading = true;
  onMount(async () => {
    try {
      loading = true;
      allNutzerItem = await loadAllValue("/api/nutzer/search/findAllItem");
      console.log(["onMount", allNutzerItem]);
      allGefahrItem = await loadAllValue("/api/gefahr/search/findAllItem");
      console.log(["onMount", allGefahrItem]);
      allProzessItem = await loadAllValue("/api/prozess/search/findAllItem");
      console.log(["onMount", allProzessItem]);
      allTerminserieItem = await loadAllValue("/api/enum/terminserie");
      console.log(["onMount", allTerminserieItem]);
      await reloadAllRisiko();
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  let allRisiko = [];
  function reloadAllRisiko() {
    const query = risikoSortParameter() + risikoFilterParameter();
    return loadAllValue("/api/risiko" + query)
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadAllRisiko", query, msg]);
        allRisiko = json;
      })
      .catch((err) => {
        console.log(["reloadAllRisiko", query, err]);
        allRisiko = [];
        toast.push(err.toString());
      });
  }
  function onCreateRisiko(risiko) {
    allRisiko = allRisiko.toSpliced(0, 0, risiko);
  }
  function onUpdateRisiko(risiko) {
    let index = allRisiko.findIndex((e) => e.id === risiko.id);
    if (index > -1) allRisiko = allRisiko.toSpliced(index, 1, risiko);
  }
  function onRemoveRisiko(risiko) {
    let index = allRisiko.findIndex((e) => e.id === risiko.id);
    if (index > -1) allRisiko = allRisiko.toSpliced(index, 1);
  }

  function updateRisiko(risiko) {
    return updatePatch("/api/risiko/" + risiko.id, risiko)
      .then((json) => {
        console.log(["updateRisiko", risiko, json]);
        onUpdateRisiko(json);
      })
      .catch((err) => {
        console.log(["updateRisiko", risiko, err]);
        toast.push(err.toString());
      });
  }
  function removeRisiko(risiko) {
    const text = risiko.titel;
    const hint = text.length > 20 ? text.substring(0, 20) + "..." : text;
    if (!confirm("Risiko '" + hint + "' wirklich löschen?")) return;
    return removeValue("/api/risiko" + "/" + risiko.id)
      .then((json) => {
        console.log(["removeRisiko", risiko, json]);
        onRemoveRisiko(json);
      })
      .catch((err) => {
        console.log(["removeRisiko", risiko, err]);
        toast.push(err.toString());
      });
  }
</script>

<h1 title="Liste der Risiken, ggfs. gefiltert, jedes Element editierbar">
  Risiko
</h1>
<div class="flex flex-col gap-1 ml-2 mr-2">
  <form on:submit|preventDefault={onRisikoFilterClicked}>
    <div class="flex flex-row gap-1 items-center pr-2">
      <div class="w-full">
        <TextField
          bind:value={risikoFilter}
          label="Filter"
          placeholder="Bitte Filterkriterien eingeben"
        />
      </div>
      <div class="w-min">
        <Icon type="submit" name="search" outlined />
      </div>
    </div>
  </form>
  {#if loading}
    <div class="h-screen flex justify-center items-center">
      <Circle size="60" unit="px" duration="1s" />
    </div>
  {:else}
    <table class="table-fixed">
      <thead class="justify-between">
        <tr class="bg-title-100">
          <th class="px-2 py-3 table-cell">
            <span class="text-title-600 text-sm">Aktiv</span>
          </th>
          <th class="px-2 py-3 text-left w-full table-cell">
            <span class="text-title-600">Title</span>
          </th>
          <th class="px-2 py-3 text-right w-0 table-cell">
            <Icon
              on:click={() => onRisikoEditorCreateClicked()}
              disabled={risikoEditorDisabled}
              title="Risiko hinzufügen"
              name="add"
              outlined
            />
          </th>
        </tr>
      </thead>
      <tbody>
        {#if risikoEditorCreate}
          <tr>
            <td class="px-2" colspan="3">
              <RisikoEditor
                bind:visible={risikoEditorCreate}
                on:create={(e) => onCreateRisiko(e.detail)}
                {allNutzerItem}
                {allTerminserieItem}
              />
            </td>
          </tr>
        {/if}
        {#each allRisiko as risiko, i}
          <tr
            on:click={(e) => onRisikoClicked(risiko)}
            title={risiko.id}
            class:border-l-2={risikoId === risiko.id}
            class:bg-gray-100={i % 2 === 1}
          >
            <td class="px-2 py-3 table-cell">
              <Checkbox bind:checked={risiko.aktiv} disabled />
            </td>
            <td class="px-2 py-3 text-left table-cell">
              <div class="text-sm underline text-blue-600">
                <a href={"/risiko/" + risiko.id}>{risiko.titel}</a>
              </div>
            </td>
            <td class="px-2 py-3 table-cell">
              <div
                class="grid grid-cols-1 md:grid-cols-4 items-center gap-1 w-max"
              >
                <Icon
                  on:click={() => onRisikoGefahrUpdateClicked(risiko)}
                  disabled={risikoEditorDisabled}
                  title="Risiko bearbeiten"
                  name="warning"
                  outlined
                />
                <Icon
                  on:click={() => onRisikoProzessUpdateClicked(risiko)}
                  disabled={risikoEditorDisabled}
                  title="Risiko bearbeiten"
                  name="schema"
                  outlined
                />
                <Icon
                  on:click={() => onRisikoRemoveClicked(risiko)}
                  disabled={risikoEditorDisabled}
                  title="Risiko löschen"
                  name="delete"
                  outlined
                />
                <Icon
                  on:click={() => onRisikoEditorUpdateClicked(risiko)}
                  disabled={risikoEditorDisabled}
                  title="Risiko bearbeiten"
                  name="edit"
                  outlined
                />
              </div>
            </td>
          </tr>
          {#if risikoEditorUpdate && risikoId === risiko.id}
            <tr>
              <td class="border-l-4 px-2" colspan="3">
                <RisikoEditor
                  bind:visible={risikoEditorUpdate}
                  on:update={(e) => onUpdateRisiko(e.detail)}
                  {allNutzerItem}
                  {allTerminserieItem}
                  {risiko}
                />
              </td>
            </tr>
          {/if}
          {#if risikoGefahrUpdate && risikoId === risiko.id}
            <tr>
              <td class="border-l-4 px-2" colspan="5">
                <RisikoGefahr
                  bind:visible={risikoGefahrUpdate}
                  on:update={(e) => onUpdateRisiko(e.detail)}
                  {allGefahrItem}
                  {risiko}
                />
              </td>
            </tr>
          {/if}
          {#if risikoProzessUpdate && risikoId === risiko.id}
            <tr>
              <td class="border-l-4 px-2" colspan="5">
                <RisikoProzess
                  bind:visible={risikoProzessUpdate}
                  on:update={(e) => onUpdateRisiko(e.detail)}
                  {allProzessItem}
                  {risiko}
                />
              </td>
            </tr>
          {/if}
        {:else}
          <tr>
            <td class="px-2" colspan="3">Keine Risiken</td>
          </tr>
        {/each}
      </tbody>
    </table>
  {/if}
</div>
