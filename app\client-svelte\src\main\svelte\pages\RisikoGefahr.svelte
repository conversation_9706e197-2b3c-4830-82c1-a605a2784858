<script>
  import { onMount } from "svelte";
  import { createEventDispatcher } from "svelte";
  import { toast } from "../components/Toast";
  import { updatePatch } from "../utils/rest.js";
  import Button from "../components/Button";
  import Icon from "../components/Icon";
  import Select from "../components/Select";
  import TextField from "../components/TextField";

  export let visible = false;
  export let autofocus = true;
  export let autoscroll = true;
  export let risiko;
  export let allGefahrItem;

  let clicked = false;
  let bottomDiv;
  onMount(async () => {
    console.log(["onMount", autofocus, autoscroll]);
    if (autofocus) newGefahrFocusOn.focus();
    if (autoscroll) bottomDiv.scrollIntoView(false);
  });

  let newRisiko = {
    titel: undefined,
    allGefahrItem: [],
    aktiv: true,
  };
  let newGefahrItem = {};
  let newGefahrFocusOn;

  $: if (risiko && risiko.id) onChange();
  async function onChange() {
    newRisiko = { ...newRisiko, ...risiko };
    console.log(["onChange", newRisiko]);
  }

  async function onSubmit() {
    try {
      clicked = true;
      newRisiko.allGefahr = newRisiko.allGefahrItem.map(
        (e) => "/api/gefahr/" + e.value
      );
      await updateRisiko();
    } finally {
      clicked = false;
    }
  }
  async function onCancel() {
    visible = false;
  }

  function onInsertGefahr() {
    newRisiko.allGefahrItem = [...newRisiko.allGefahrItem, newGefahrItem];
    newGefahrItem = {};
    newGefahrFocusOn.focus();
    console.log(["onInsertGefahr", newRisiko]);
  }
  function onRemoveGefahr(index) {
    newRisiko.allGefahrItem = [
      ...newRisiko.allGefahrItem.slice(0, index),
      ...newRisiko.allGefahrItem.slice(index + 1),
    ];
    newGefahrItem = {};
    newGefahrFocusOn.focus();
    console.log(["onRemoveGefahr", newRisiko]);
  }

  const dispatch = createEventDispatcher();
  function updateRisiko() {
    return updatePatch("/api/risiko" + "/" + newRisiko.id, newRisiko)
      .then((json) => {
        console.log(["updateRisiko", newRisiko, json]);
        visible = false;
        dispatch("update", json);
      })
      .catch((err) => {
        console.log(["updateRisiko", newRisiko, err]);
        toast.push(err.toString());
      });
  }
</script>

<form on:submit|preventDefault={onSubmit} on:reset|preventDefault={onCancel}>
  <div class="flex flex-col">
    <div class="w-full">
      {#each newRisiko.allGefahrItem as gefahrItem, i}
        <div class="flex flex-row gap-1 items-baseline">
          <div class="w-full">
            <TextField
              bind:value={gefahrItem.text}
              disabled
              title={gefahrItem.value}
              label={i + 1 + ". Gefahr"}
            />
          </div>
          <div class="place-self-center">
            <Icon onclick={() => onRemoveGefahr(i)} name="delete" outlined />
          </div>
        </div>
      {/each}
      <div class="flex flex-row gap-1 items-baseline">
        <div class="w-full">
          <Select
            bind:this={newGefahrFocusOn}
            bind:value={newGefahrItem}
            allItem={allGefahrItem}
            label="Neue Gefahr"
            placeholder="Bitte eine Gefahr wählen"
          />
        </div>
        <div class="place-self-center">
          <Icon
            onclick={() => onInsertGefahr()}
            disabled={!newGefahrItem.value}
            name="add"
            outlined
          />
        </div>
      </div>
    </div>
  </div>
  <div class="py-4 flex flex-row gap-1 items-baseline">
    <div class="flex-initial">
      <Button type="submit">Ok</Button>
    </div>
    <div class="flex-initial">
      <slot name="cancel">
        <Button type="reset">Abbrechen</Button>
      </slot>
    </div>
  </div>
</form>

<div class="h-0" bind:this={bottomDiv}>&nbsp;</div>

{#if import.meta.env.DEV}
  <details tabindex="-1">
    <summary>JSON</summary>
    <pre>{JSON.stringify(newRisiko, null, 2)}</pre>
  </details>
{/if}
