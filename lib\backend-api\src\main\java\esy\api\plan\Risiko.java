package esy.api.plan;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import esy.api.team.Nutzer;
import esy.json.JsonJpaUlidEntity;
import esy.json.JsonMapper;
import lombok.Getter;
import lombok.NonNull;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Entity-Objekt für ein Risiko.
 */
@Audited
@Entity
@Table(name = "risiko", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"id"}),
        @UniqueConstraint(columnNames = {"titel"})
})
public final class Risiko extends JsonJpaUlidEntity<Risiko> implements VorgangStatusAware<UUID> {

    public static final String DATE_PATTERN = "yyyy-MM-dd";
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DATE_PATTERN);

    /**
     * Kurze Bezeichnung für das Risiko.
     */
    // tag::titel[]
    @Column(name = "titel")
    @Getter
    @JsonProperty
    @NotBlank
    private String titel;
    // end::titel[]

    /**
     * Lange Beschreibung für das Risiko.
     * Kann mehrzeilig sein.
     */
    // tag::text[]
    @Column(name = "text")
    @Getter
    @JsonProperty
    @NotBlank
    private String text;
    // end::text[]

    /**
     * Prüfung des Risikos ist fällig ab diesem Datum.
     */
    // tag::termin[]
    @Column(name = "termin", columnDefinition = "DATE")
    @Getter
    @JsonProperty
    @NotNull
    @DateTimeFormat(pattern = DATE_PATTERN)
    private LocalDate termin;
    // end::termin[]

    /**
     * Prüfung des Risikos wird regelmäßig wiederholt.
     */
    // tag::terminserie[]
    @NotAudited
    @Column(name = "terminserie")
    @Enumerated(EnumType.STRING)
    @Getter
    @JsonProperty
    private Terminserie terminserie;
    // end::terminserie[]

    /**
     * Status für das Risiko.
     */
    // tag::status[]
    @Column(name = "status")
    // No @Getter
    // No @JsonProperty
    @NotEmpty
    private String status;
    // end::status[]

    /**
     * Bezug des Risikos zu einem Nutzer.
     */
    // tag::nutzer[]
    @NotAudited
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = true)
    @JoinColumn(name = "nutzer_id", referencedColumnName = "id", insertable = false, updatable = false)
    @Getter
    @JsonIgnore
    private Nutzer nutzer;
    // end::nutzer[]

    /**
     * Bezug des Risikos zu einem Nutzer (nur ID).
     */
    // tag::nutzerId[]
    @Column(name = "nutzer_id")
    @Getter
    @JsonProperty
    private UUID nutzerId;
    // end::nutzerId[]

    /**
     * Gefahren, die dieses Risiko ausgelöst haben.
     */
    // tag::allGefahr[]
    @NotAudited
    @ManyToMany(
            fetch = FetchType.EAGER)
    @JoinTable(
            name = "risiko_gefahr",
            joinColumns = @JoinColumn(name = "risiko_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "gefahr_id", referencedColumnName = "id"))
    @Getter
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Set<Gefahr> allGefahr;
    // end::allGefahr[]

    /**
     * Prozesse, die für das Risiko relevant sind.
     */
    // tag::allProzess[]
    @NotAudited
    @ManyToMany(
            fetch = FetchType.EAGER)
    @JoinTable(
            name = "risiko_prozess",
            joinColumns = @JoinColumn(name = "risiko_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "prozess_id", referencedColumnName = "id"))
    @Getter
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Set<Prozess> allProzess;
    // end::allProzess[]

    Risiko() {
        super();
        this.titel = "";
        this.text = "";
        this.termin = LocalDate.parse("2000-01-01");
        this.terminserie = Terminserie.X;
        this.status = VorgangStatus.I.name();
        this.nutzer = null;
        this.nutzerId = null;
        this.allGefahr = new LinkedHashSet<>();
        this.allProzess = new LinkedHashSet<>();
    }

    Risiko(@NonNull final Long version, @NonNull final UUID id) {
        super(version, id);
        this.titel = "";
        this.text = "";
        this.termin = LocalDate.parse("2000-01-01");
        this.terminserie = Terminserie.X;
        this.status = VorgangStatus.I.name();
        this.nutzer = null;
        this.nutzerId = null;
        this.allGefahr = new LinkedHashSet<>();
        this.allProzess = new LinkedHashSet<>();
    }

    @Override
    public String toString() {
        return super.toString() + ",titel='" + titel + "',status=" + status;
    }

    @Override
    public boolean isEqual(final Risiko that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        return this.titel.equals(that.titel) &&
                this.text.equals(that.text) &&
                this.termin.equals(that.termin) &&
                this.terminserie.equals(that.terminserie) &&
                this.status.equals(that.status) &&
                Objects.equals(this.nutzerId, that.nutzerId) &&
                Objects.equals(this.allGefahr, that.allGefahr) &&
                Objects.equals(this.allProzess, that.allProzess);
    }

    @Override
    public Risiko withId(@NonNull final UUID id) {
        if (Objects.equals(getId(), id)) {
            return this;
        } else {
            return new Risiko(getVersion(), id).merge(this);
        }
    }

    @Override
    public Risiko merge(@NonNull final Risiko that) {
        this.titel = that.titel;
        this.text = that.text;
        this.termin = that.termin;
        this.terminserie = that.terminserie;
        this.status = that.status;
        this.nutzer = that.nutzer;
        this.nutzerId = that.nutzerId;
        this.allGefahr = that.allGefahr;
        this.allProzess = that.allProzess;
        return this;
    }

    /**
     * Applies a transition to a new {@link Risiko#status}.
     * Computes a new date for {@link Risiko#termin}.
     *
     * @param status {@link VorgangStatus status}
     */
    @Override
    public void applyStatusTransition(@NonNull final VorgangStatus status) {
        this.status = status.name();
        this.termin = this.terminserie.apply(this.termin);
    }

    @JsonAnyGetter
    private Map<String, Object> extraJson() {
        final var allExtra = new HashMap<String, Object>();
        allExtra.put("version", getVersion());
        // provide relation properties
        allExtra.put("allGefahrItem", allGefahr
                .stream()
                .map(GefahrItem::fromValue)
                .collect(Collectors.toSet()));
        allExtra.put("allProzessItem", allProzess
                .stream()
                .map(ProzessItem::fromValue)
                .collect(Collectors.toSet()));
        // provide transition status
        allExtra.put("allStatusTransition", VorgangStatus
                .forUpdate(getStatus()));
        return allExtra;
    }

    @JsonIgnore
    public boolean isFaellig(@NonNull final LocalDate datum) {
        return datum.isAfter(this.termin);
    }

    @JsonIgnore
    public boolean isWiedervorlage() {
        return !Terminserie.X.equals(this.terminserie);
    }

    @JsonProperty
    public boolean isAktiv() {
        return !VorgangStatus.X.name().equals(status);
    }

    @JsonProperty
    public VorgangStatus getStatus() {
        try {
            return VorgangStatus.valueOf(status);
        } catch (IllegalArgumentException e) {
            return VorgangStatus.X;
        }
    }

    @JsonProperty
    public Risiko setStatus(@NonNull final VorgangStatus status) {
        this.status = status.name();
        return this;
    }

    @JsonIgnore
    public boolean isBesitzer(@NonNull final String mail) {
        return nutzer != null && mail.equals(nutzer.getMail());
    }

    @JsonIgnore
    public Risiko setNutzer(final Nutzer nutzer) {
        if (nutzer != null) {
            this.nutzer = nutzer;
            this.nutzerId = nutzer.getId();
        } else {
            this.nutzer = null;
            this.nutzerId = null;
        }
        return this;
    }

    @JsonIgnore
    public Risiko addGefahr(@NonNull final Gefahr gefahr) {
        this.allGefahr.add(gefahr);
        return this;
    }

    @JsonIgnore
    public Risiko addProzess(@NonNull final Prozess prozess) {
        this.allProzess.add(prozess);
        return this;
    }

    @Override
    public String writeJson() {
        return new JsonMapper().writeJson(this);
    }

    public static Risiko parseJson(@NonNull final String json) {
        return new JsonMapper().parseJson(json, Risiko.class);
    }
}
