:keyword: practice,testing
:term: TDD

_Test-Driven Design_ (kurz TDD) ist ein bewährtes Konzept, dass sehr gut zu einem inkrementellen Vorgehen bei der Entwicklung passt.
Die Software wird Schritt für Schritt erweitert.
Für jede Erweiterung wird mindestens ein Test geschrieben.
Die Programmierung ist erst abgeschlossen, wenn alle Tests (auch die, die zuvor schon existierten) erfolgreich durchlaufen werden.
