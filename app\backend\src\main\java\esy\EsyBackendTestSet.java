package esy;

import esy.api.info.EnumTestSet;
import esy.api.ort.AdresseTestSet;
import esy.api.plan.*;
import esy.api.team.GruppeTestSet;
import esy.api.team.NutzerTestSet;
import esy.api.wiki.OrdnerTestSet;
import esy.api.zeit.AbwesenheitTestSet;
import esy.api.zeit.AnwesenheitTestSet;
import esy.api.zeit.EinsatzTestSet;
import esy.app.info.EnumRepository;
import esy.app.ort.AdresseRepository;
import esy.app.plan.*;
import esy.app.team.GruppeRepository;
import esy.app.team.NutzerRepository;
import esy.app.wiki.OrdnerRepository;
import esy.app.zeit.AbwesenheitRepository;
import esy.app.zeit.AnwesenheitRepository;
import esy.app.zeit.EinsatzRepository;
import esy.auth.JwtUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.DayOfWeek;
import java.time.LocalDate;

import static java.time.temporal.TemporalAdjusters.firstInMonth;
import static java.time.temporal.TemporalAdjusters.lastInMonth;

@Slf4j
@Component
@RequiredArgsConstructor
@SuppressWarnings("java:S1192") // allow duplicating literals
class EsyBackendTestSet implements CommandLineRunner {

    static final String ENUM_ANTWORT = "antwort";
    static final String ENUM_KANAL = "kanal";
    static final String ENUM_QUELLE = "quelle";

    @Value("${backend.email:" + JwtUser.DEFAULT_EMAIL + "}")
    private String backendEmail;

    @Value("${backend.login:" + JwtUser.DEFAULT_LOGIN + "}")
    private String backendLogin;

    @Value("${backend.title:" + JwtUser.DEFAULT_TITLE + "}")
    private String backendTitle;

    private final AbwesenheitRepository abwesenheitRepository;

    private final AnwesenheitRepository anwesenheitRepository;

    private final AdresseRepository adresseRepository;

    private final AufgabeRepository aufgabeRepository;

    private final EinsatzRepository einsatzRepository;

    private final EnumRepository enumRepository;

    private final GefahrRepository gefahrRepository;

    private final GruppeRepository gruppeRepository;

    private final MeldungRepository meldungRepository;

    private final NutzerRepository nutzerRepository;

    private final OrdnerRepository ordnerRepository;

    private final ProjektRepository projektRepository;

    private final ProzessRepository prozessRepository;

    private final RisikoRepository risikoRepository;

    @Override
    @Transactional
    public void run(final String... args) {
        final var enumTestSet = new EnumTestSet(enumRepository::save);
        if (enumRepository.count(ENUM_ANTWORT) == 0) {
            final var allEnum = enumTestSet.createAllEnumAntwort(ENUM_ANTWORT);
            allEnum.values().forEach(this::logCreated);
        }
        if (enumRepository.count(ENUM_KANAL) == 0) {
            final var allEnum = enumTestSet.createAllEnumKanal(ENUM_KANAL);
            allEnum.values().forEach(this::logCreated);
        }
        if (enumRepository.count(ENUM_QUELLE) == 0) {
            final var allEnum = enumTestSet.createAllEnumQuelle(ENUM_QUELLE);
            allEnum.values().forEach(this::logCreated);
        }
        if (adresseRepository.count() == 0) {
            final var adresseTestSet = new AdresseTestSet(adresseRepository::save);
            final var allAdresse = adresseTestSet.createAllAdresse();
            allAdresse.values().forEach(this::logCreated);
        }
        if (gruppeRepository.count() == 0) {
            final var gruppeTestSet = new GruppeTestSet(gruppeRepository::save);
            final var allGruppe = gruppeTestSet.createAllGruppe();
            allGruppe.values().forEach(this::logCreated);

            final var nutzerTestSet = new NutzerTestSet(nutzerRepository::save);
            final var allNutzer = nutzerTestSet.createAllNutzer(allGruppe);
            allNutzer.values().forEach(this::logCreated);
            final var verwalter = nutzerTestSet.createVerwalter(allGruppe, backendEmail, backendLogin, backendTitle);
            logCreated(verwalter);

            final var projektTestSet = new ProjektTestSet(projektRepository::save);
            final var allProjekt = projektTestSet.createAllProjekt(verwalter, allNutzer);
            allProjekt.values().forEach(this::logCreated);

            // Erster Montag des aktuellen Monats
            final var datum = LocalDate.now().with(firstInMonth(DayOfWeek.MONDAY));

            final var aufgabeTestSet = new AufgabeTestSet(aufgabeRepository::save);
            final var allAufgabe = aufgabeTestSet.createAllAufgabe(allProjekt, datum);
            allAufgabe.forEach(this::logCreated);

            final var anwesenheitTestSet = new AnwesenheitTestSet(anwesenheitRepository::save);
            final var allAnwesenheit = anwesenheitTestSet.createAllAnwesenheit(verwalter);
            allAnwesenheit.forEach(this::logCreated);

            final var einsatzTestSet = new EinsatzTestSet(einsatzRepository::save);
            final var allEinsatz = einsatzTestSet.createAllEinsatz(verwalter, datum);
            allEinsatz.forEach(this::logCreated);

            final var abwesenheitCreator = new AbwesenheitTestSet(abwesenheitRepository::save);
            final var allAbwesenheit = abwesenheitCreator.createAllAbwesenheit(verwalter, datum);
            allAbwesenheit.forEach(this::logCreated);
        }
        if (ordnerRepository.count() == 0) {
            final var datum = LocalDate.now();
            final var ordnerTestSet = new OrdnerTestSet(ordnerRepository::save);
            final var allOrdner = ordnerTestSet.createAllOrdner(datum);
            allOrdner.values().forEach(this::logCreated);
        }
        if (risikoRepository.count() == 0) {
            final var gefahrTestSet = new GefahrTestSet(gefahrRepository::save);
            final var allGefahr = gefahrTestSet.createAllGefahr();
            allGefahr.values().forEach(this::logCreated);

            final var prozessTestSet = new ProzessTestSet(prozessRepository::save);
            final var allProzess = prozessTestSet.createAllProzess();
            allProzess.values().forEach(this::logCreated);

            // Letzter Freitag des aktuellen Monats
            final var datum = LocalDate.now().with(lastInMonth(DayOfWeek.FRIDAY));

            final var risikoTestSet = new RisikoTestSet(risikoRepository::save);
            final var allRisiko = risikoTestSet.createAllRisiko(datum);
            risikoTestSet.updateRisikoGefahr(allRisiko, allGefahr);
            risikoTestSet.updateRisikoProzess(allRisiko, allProzess);
            allRisiko.values().forEach(this::logCreated);

            final var meldungTestSet = new MeldungTestSet(meldungRepository::save);
            final var allMeldung = meldungTestSet.createAllMeldung(datum);
            allMeldung.values().forEach(this::logCreated);
        }
    }

    private void logCreated(final Object value) {
        log.info("CREATED [{}]", value);
    }
}
