<script>
  import Chip from "./components/Chip";
  import { logoutUrl } from "./utils/url.js";
  let openIn;
  function onClick() {
    try {
      openIn.href = logoutUrl();
      openIn.click();
    } finally {
      openIn.href = undefined;
    }
  }
</script>

<Chip icon="logout" onclick={onClick}>Logout</Chip>
<!-- svelte-ignore a11y-missing-attribute -->
<a aria-label="Logout" class="hidden" target="_self" bind:this={openIn}
  >&nbsp;</a
>
