<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import Button from "../components/Button";
  import { toast } from "../components/Toast";
  import { loadAllValue } from "../utils/rest.js";
  import { loadOneValue } from "../utils/rest.js";
  import { printerGetAll } from "../utils/rest.js";
  import { printerPatch } from "../utils/rest.js";
  import { Circle } from "svelte-loading-spinners";
  import Combobox from "../components/Combobox";
  import Icon from "../components/Icon";
  import OrdnerEditor from "./OrdnerEditor.svelte";
  import SeiteCard from "./SeiteCard.svelte";
  import SeiteEditor from "./SeiteEditor.svelte";

  export let ordnerId;

  let seiteEditorItem = {
    value: undefined,
    text: "",
  };
  let seiteEditorCreate = false;
  async function onSeiteEditorCreateClicked() {
    seiteEditorCreate = true;
  }

  async function onChange() {
    try {
      loading = true;
      await tick();
      await updateRepo();
      await reloadOneOrdner();
      await reloadAllSeite();
      await reloadAllAdoc();
    } finally {
      loading = false;
    }
  }
  async function onCancel() {
    await reloadOneOrdner();
  }

  let allDateitypItem = [];
  let allLandItem = [];
  let allNutzerItem = [];
  let allOrdnerartItem = [];
  let allSpracheItem = [];
  let allTerminserieItem = [];
  let loading = true;
  onMount(async () => {
    try {
      loading = true;
      allDateitypItem = await loadAllValue("/api/enum/dateityp");
      console.log(["onMount", allDateitypItem]);
      allLandItem = await loadAllValue("/api/enum/land");
      console.log(["onMount", allLandItem]);
      allNutzerItem = await loadAllValue("/api/nutzer/search/findAllItem");
      console.log(["onMount", allNutzerItem]);
      allOrdnerartItem = await loadAllValue("/api/enum/ordnerart");
      console.log(["onMount", allOrdnerartItem]);
      allSpracheItem = await loadAllValue("/api/enum/sprache");
      console.log(["onMount", allSpracheItem]);
      allTerminserieItem = await loadAllValue("/api/enum/terminserie");
      console.log(["onMount", allTerminserieItem]);
      await updateRepo();
      await reloadOneOrdner();
      await reloadAllSeite();
      await reloadAllAdoc();
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  let ordner;
  function reloadOneOrdner() {
    return loadOneValue("/api/ordner/" + ordnerId)
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadOneOrdner", msg]);
        ordner = json;
      })
      .catch((err) => {
        console.log(["reloadOneOrdner", err]);
        ordner = {};
        toast.push(err.toString());
      });
  }

  let allSeite = [];
  function reloadAllSeite() {
    return loadAllValue("/api/ordner/" + ordner.id + "/allSeite")
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadAllSeite", msg]);
        allSeite = json;
      })
      .catch((err) => {
        console.log(["reloadAllSeite", err]);
        allSeite = [];
        toast.push(err.toString());
      });
  }
  function onCreateSeite(seite) {
    allSeite = allSeite.toSpliced(0, 0, seite);
  }
  function onUpdateSeite(seite) {
    let index = allSeite.findIndex((e) => e.id === seite.id);
    if (index > -1) allSeite = allSeite.toSpliced(index, 1, seite);
  }
  function onRemoveSeite(seite) {
    let index = allSeite.findIndex((e) => e.id === seite.id);
    if (index > -1) allSeite = allSeite.toSpliced(index, 1);
  }

  let allAdocItem = [];
  function reloadAllAdoc() {
    return printerGetAll("/doc/ordner/" + ordner.id + "/adoc")
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadAllAdoc", msg]);
        allAdocItem = json.map((e) => {
          return {
            value: e,
            text: e.uri,
          };
        });
      })
      .catch((err) => {
        console.log(["reloadAllAdoc", err]);
        allAdocItem = [];
        toast.push(err.toString());
      });
  }

  function updateRepo() {
    return printerPatch("/doc/ordner/" + ordnerId)
      .then((json) => {
        console.log(["updateRepo", json]);
      })
      .catch((err) => {
        console.log(["updateRepo", err]);
        toast.push(err.toString());
      });
  }
</script>

{#if loading}
  <div class="h-screen flex justify-center items-center">
    <Circle size="60" unit="px" duration="1s" />
  </div>
{:else}
  <div class="flex flex-col gap-1 ml-2 mr-2">
    {#if ordner}{#key ordner}
        <h1>{ordner.titel}</h1>
        <fieldset class="p-4 border-2">
          <legend class="text-xl">Ordner bearbeiten</legend>
          <OrdnerEditor
            on:update={onChange}
            visible={true}
            autofocus={true}
            autoscroll={false}
            {allOrdnerartItem}
            {ordner}
          >
            <div slot="cancel">
              <Button type="reset" on:click={onCancel}>Verwerfen</Button>
            </div>
          </OrdnerEditor>
        </fieldset>
        <fieldset class="p-4 border-2">
          <legend class="text-xl">Seiten bearbeiten</legend>
          <div class="block mt-2 p-2 border rounded-lg shadow-lg">
            <div class="flex flex-row items-center gap-1">
              <div class="w-full">
                <Combobox
                  bind:value={seiteEditorItem}
                  allItem={allAdocItem}
                  nullable
                  disabled={seiteEditorCreate}
                  label="Datei"
                  placeholder="Bitte eine Datei wählen"
                />
              </div>
              <div class="grid grid-cols-1 items-top gap-1">
                <Icon
                  on:click={onSeiteEditorCreateClicked}
                  disabled={seiteEditorCreate || !seiteEditorItem?.value}
                  name="save"
                  outlined
                />
              </div>
            </div>
            {#if seiteEditorCreate}
              <SeiteEditor
                bind:visible={seiteEditorCreate}
                on:update={(e) => onCreateSeite(e.detail)}
                {allDateitypItem}
                {allLandItem}
                {allNutzerItem}
                {allSpracheItem}
                {allTerminserieItem}
                ordnerId={ordner.id}
                seite={seiteEditorItem.value}
              />
            {/if}
          </div>
          <div class="grid grid-cols-1">
            {#each allSeite as seite, i}
              {#key seite.id}
                <SeiteCard
                  on:update={(e) => onUpdateSeite(e.detail)}
                  on:remove={(e) => onRemoveSeite(e.detail)}
                  {allDateitypItem}
                  {allSpracheItem}
                  {seite}
                />
              {/key}
            {:else}
              <span class="px-2 py-3">Keine Seiten</span>
            {/each}
          </div>
        </fieldset>
      {/key}{/if}
  </div>
{/if}
