<script>
  import Button from "./components/Button";
  let total = 0;
  let value = "";
  let state = null;
  function resolveState() {
    switch (state) {
      case "add":
        total += parseFloat(value);
        break;
      case "substract":
        total -= parseFloat(value);
        break;
      case "multiply":
        total *= parseFloat(value);
        break;
      case "divide":
        total /= parseFloat(value);
        break;
      default:
        total = parseFloat(value);
        break;
    }
    value = "0";
  }
  function setOperation(_v) {
    resolveState();
    state = _v;
  }
  function setValue(_v) {
    if (value == "0" || state == "equal") {
      value = "";
    }
    if (state == "equal") {
      state = null;
    }
    if (_v == "C") {
      total = 0;
      state = null;
      value = "";
    } else {
      value = value + _v;
    }
  }
  function equal() {
    resolveState();
    value = total.toString();
    state = "equal";
  }
</script>

<h1>Rechner</h1>
<div class="w-96 ml-2 mr-2 gap-1">
  <pre class="text-2xl text-right">
		{value ? value : "_"}
	</pre>
  <div class="grid grid-cols-4 gap-1">
    <Button
      outlined
      disabled={value === "."}
      onclick={() => {
        setOperation("add");
      }}
      >+
    </Button>
    <Button
      outlined
      disabled={value === "."}
      onclick={() => {
        setOperation("substract");
      }}
      >-
    </Button>
    <Button
      outlined
      disabled={value === "."}
      onclick={() => {
        setOperation("multiply");
      }}
      >&times;
    </Button>
    <Button
      outlined
      disabled={value === "."}
      onclick={() => {
        setOperation("divide");
      }}
      >&divide;
    </Button>
    <Button
      onclick={() => {
        setValue(7);
      }}
      >7
    </Button>
    <Button
      onclick={() => {
        setValue(8);
      }}
    >
      8
    </Button>
    <Button
      onclick={() => {
        setValue(9);
      }}
      >9
    </Button>
    <Button
      onclick={() => {
        setValue(4);
      }}
      >4
    </Button>
    <Button
      onclick={() => {
        setValue(5);
      }}
      >5
    </Button>
    <Button
      onclick={() => {
        setValue(6);
      }}
      >6
    </Button>
    <Button
      onclick={() => {
        setValue(1);
      }}
      >1
    </Button>
    <Button
      onclick={() => {
        setValue(2);
      }}
      >2
    </Button>
    <Button
      onclick={() => {
        setValue(3);
      }}
      >3
    </Button>
    <Button
      onclick={() => {
        setValue(0);
      }}
      >0
    </Button>
    <Button
      disabled={value.includes(".")}
      onclick={() => {
        setValue(".");
      }}
      >.
    </Button>
    <Button
      outlined
      onclick={() => {
        setValue("C");
      }}
      >C
    </Button>
    <div class="calculator-eq">
      <Button outlined disabled={value === "."} onclick={equal}>=</Button>
    </div>
  </div>
</div>

<style>
  .calculator-eq {
    display: grid;
    grid-area: 2 / 4 / 6 / 5;
  }
</style>
