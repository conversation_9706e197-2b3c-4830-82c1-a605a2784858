<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import { toast } from "../components/Toast";
  import { loadAllValue } from "../utils/rest.js";
  import { loadOneValue } from "../utils/rest.js";
  import { storedNutzer } from "../stores/nutzer.js";
  import { Circle } from "svelte-loading-spinners";
  import Icon from "../components/Icon";
  import Select from "../components/Select";
  import TerminplanCard from "./TerminplanCard.svelte";

  let vorgangNutzerId = undefined;

  import { collectAllYearItem } from "../utils/date.js";
  const allJahrItem = collectAllYearItem(1, 4);
  let vorgangJahr;

  import { collectAllMonthItem } from "../utils/date.js";
  const allMonatItem = collectAllMonthItem();
  let vorgangMonat;

  let allNutzerItem = [];
  let allTerminserieItem = [];
  let loading = true;
  onMount(async () => {
    try {
      loading = true;
      allNutzerItem = await loadAllValue("/api/nutzer/search/findAllItem");
      console.log(["onMount", allNutzerItem]);
      allTerminserieItem = await loadAllValue("/api/enum/terminserie");
      console.log(["onMount", allTerminserieItem]);
      vorgangNutzerId = $storedNutzer.id;
      vorgangJahr = $storedNutzer.jahr;
      vorgangMonat = $storedNutzer.monat;
      await reloadTerminplan();
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  async function onVorgangFilterClicked() {
    try {
      loading = true;
      $storedNutzer.id = vorgangNutzerId;
      $storedNutzer.jahr = vorgangJahr;
      $storedNutzer.monat = vorgangMonat;
      await tick();
      await reloadTerminplan();
    } finally {
      loading = false;
    }
  }

  let terminSelected = {};
  function onTerminClicked(termin) {
    terminSelected = termin;
  }

  let allTerminByDatum = new Map();
  function reloadTerminplan() {
    const allTerminQuery = [];
    if (vorgangNutzerId) {
      allTerminQuery.push("nutzerId=" + vorgangNutzerId);
    }
    if (vorgangJahr) {
      allTerminQuery.push("jahr=" + vorgangJahr);
    }
    if (vorgangMonat) {
      allTerminQuery.push("monat=" + vorgangMonat);
    }
    if (allTerminQuery) {
      const query = "?" + allTerminQuery.join("&");
      return loadOneValue("/api/terminplan" + query)
        .then((json) => {
          const msg = import.meta.env.DEV ? json : json.length;
          console.log(["reloadTerminplan", query, msg]);
          allTerminByDatum = new Map(Object.entries(json.allTag));
        })
        .catch((err) => {
          console.log(["reloadTerminplan", query, err]);
          allTerminByDatum = new Map();
          toast.push(err.toString());
        });
    } else {
      allTerminByDatum = new Map();
    }
  }
  function removeTerminplan() {
    allTerminByDatum = new Map();
  }
</script>

<h1>Terminplan</h1>
<div class="flex flex-col gap-1 ml-2 mr-2">
  <form on:submit|preventDefault={onVorgangFilterClicked}>
    <div class="flex flex-row gap-1 items-center sm:items-baseline">
      <div class="flex flex-col sm:flex-row gap-1 w-full">
        <div class="w-full sm:w-2/4">
          <Select
            bind:value={vorgangNutzerId}
            onchange={removeTerminplan}
            allItem={allNutzerItem}
            valueGetter={(v) => v?.value}
            nullable
            label="Nutzer"
            placeholder="Bitte einen Nutzer wählen"
          />
        </div>
        <div class="w-full sm:w-1/4">
          <Select
            bind:value={vorgangJahr}
            onchange={removeTerminplan}
            valueGetter={(v) => v?.value}
            allItem={allJahrItem}
            required
            label="Jahr"
            placeholder="Bitte ein Jahr wählen"
          />
        </div>
        <div class="w-full sm:w-1/4">
          <Select
            bind:value={vorgangMonat}
            onchange={removeTerminplan}
            valueGetter={(v) => v?.value}
            allItem={allMonatItem}
            required
            label="Monat"
            placeholder="Bitte einen Monat wählen"
          />
        </div>
      </div>
      <div class="w-min">
        <Icon type="submit" name="search" outlined />
      </div>
    </div>
  </form>
  {#if loading}
    <div class="h-screen flex justify-center items-center">
      <Circle size="60" unit="px" duration="1s" />
    </div>
  {:else}
    <table class="table-fixed">
      <tbody>
        {#each [...allTerminByDatum] as [datum, termin]}
          <tr
            on:click={(e) => onTerminClicked(termin)}
            title={termin.id}
            class:border-l-2={terminSelected === termin}
          >
            <td class="px-2 py-3 w-12 align-top">
              <div class="flex flex-col">
                <span class="text-title-600" title={datum}>
                  <b>{termin.wochentag}</b>
                </span>
                <small>{termin.tag}.</small>
              </div>
            </td>
            <td class="px-2 py-3">
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-1">
                {#each termin.allVorgang as vorgang}
                  {#key vorgang.id}
                    <TerminplanCard
                      on:update={reloadTerminplan}
                      {vorgang}
                      {allTerminserieItem}
                    />
                  {/key}
                {:else}
                  <span class="px-2 py-3">Keine Vorgänge</span>
                {/each}
              </div>
            </td>
          </tr>
        {:else}
          <tr>
            <td class="px-2 py-3" colspan="4">Keine Termine</td>
          </tr>
        {/each}
      </tbody>
    </table>
  {/if}
</div>
