<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import { toast } from "../components/Toast";
  import { loadAllValue } from "../utils/rest.js";
  import { updatePatch } from "../utils/rest.js";
  import { removeValue } from "../utils/rest.js";
  import { Circle } from "svelte-loading-spinners";
  import Icon from "../components/Icon";
  import Checkbox from "../components/Checkbox";
  import AufgabeEditor from "./AufgabeEditor.svelte";

  export let projektId;
  export let allNutzerItem;
  export let allTerminserieItem;

  let loading = true;
  onMount(async () => {
    try {
      loading = true;
      await tick();
      await reloadAllAufgabe();
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  let aufgabeId = undefined;
  function onAufgabeClicked(aufgabe) {
    aufgabeId = aufgabe.id;
  }
  let aufgabeEditorUpdate = false;
  function onAufgabeEditorUpdateClicked(aufgabe) {
    aufgabeId = aufgabe.id;
    aufgabeEditorUpdate = true;
  }
  $: aufgabeEditorDisabled = aufgabeEditorUpdate;

  let allAufgabe = [];
  function onCreateAufgabe(aufgabe) {
    allAufgabe = allAufgabe.toSpliced(0, 0, aufgabe);
  }
  function onUpdateAufgabe(aufgabe) {
    let index = allAufgabe.findIndex((e) => e.id === aufgabe.id);
    if (index > -1) allAufgabe = allAufgabe.toSpliced(index, 1, aufgabe);
  }
  function onRemoveAufgabe(aufgabe) {
    let index = allAufgabe.findIndex((e) => e.id === aufgabe.id);
    if (index > -1) allAufgabe = allAufgabe.toSpliced(index, 1);
  }
  function reloadAllAufgabe() {
    return loadAllValue("/api/aufgabe?sort=termin,text&projekt.id=" + projektId)
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadAllAufgabe", msg]);
        allAufgabe = json;
      })
      .catch((err) => {
        console.log(["reloadAllAufgabe", err]);
        toast.push(err.toString());
      });
  }

  function updateAufgabe(aufgabe) {
    return updatePatch("/api/aufgabe/" + aufgabe.id, aufgabe)
      .then((json) => {
        console.log(["updateAufgabe", aufgabe, json]);
        onUpdateAufgabe(json);
      })
      .catch((err) => {
        console.log(["updateAufgabe", aufgabe, err]);
        toast.push(err.toString());
      });
  }
  function removeAufgabe(aufgabe) {
    const text = aufgabe.text;
    const hint = text.length > 20 ? text.substring(0, 20) + "..." : text;
    if (!confirm("Aufgabe '" + hint + "' wirklich löschen?")) return;
    return removeValue("/api/aufgabe" + "/" + aufgabe.id)
      .then((json) => {
        console.log(["removeAufgabe", aufgabe, json]);
        onRemoveAufgabe(json);
      })
      .catch((err) => {
        console.log(["removeAufgabe", aufgabe, err]);
        toast.push(err.toString());
      });
  }
</script>

<div class="flex flex-col">
  {#if loading}
    <div class="h-screen flex justify-center items-center">
      <Circle size="60" unit="px" duration="1s" />
    </div>
  {:else}
    <table class="table-fixed mt-1">
      <tbody>
        {#each allAufgabe as aufgabe}
          <tr
            on:click={(e) => onAufgabeClicked(aufgabe)}
            title={aufgabe.id}
            class:border-l-2={aufgabeId === aufgabe.id}
          >
            <td class="px-2 py-3">
              <Checkbox bind:checked={aufgabe.aktiv} disabled />
            </td>
            <td class="px-2 py-3 text-left w-full">
              <div class="flex flex-col gap-1 pt-2">
                <div class="w-full">
                  <p class="font-semibold">{aufgabe.titel}</p>
                </div>
                <div class="w-full">
                  <p class="text-sm whitespace-pre-line">{aufgabe.text}</p>
                </div>
              </div>
            </td>
            <td class="px-2 py-3 w-0">
              <div
                class="grid grid-cols-1 md:grid-cols-2 items-center gap-1 w-max"
              >
                <Icon
                  on:click={() => removeAufgabe(aufgabe)}
                  disabled={aufgabeEditorDisabled}
                  title="Aufgabe löschen"
                  name="delete"
                  outlined
                />
                <Icon
                  on:click={() => onAufgabeEditorUpdateClicked(aufgabe)}
                  disabled={aufgabeEditorDisabled}
                  title="Aufgabe bearbeiten"
                  name="edit"
                  outlined
                />
              </div>
            </td>
          </tr>
          {#if aufgabeEditorUpdate && aufgabeId === aufgabe.id}
            <tr>
              <td class="border-l-4 px-2" colspan="3">
                <AufgabeEditor
                  bind:visible={aufgabeEditorUpdate}
                  on:update={(e) => onUpdateAufgabe(e.detail)}
                  {aufgabe}
                  {projektId}
                  {allNutzerItem}
                  {allTerminserieItem}
                />
              </td>
            </tr>
          {/if}
        {:else}
          <tr>
            <td class="px-2 py-3" colspan="3">Keine Aufgaben</td>
          </tr>
        {/each}
      </tbody>
    </table>
  {/if}
</div>
