<script>
  import { onMount } from "svelte";
  import { createEventDispatcher } from "svelte";
  import { toast } from "../components/Toast";
  import { createValue } from "../utils/rest.js";
  import { updatePatch } from "../utils/rest.js";
  import Button from "../components/Button";
  import TextArea from "../components/TextArea";
  import TextField from "../components/TextField";

  export let visible = false;
  export let autofocus = true;
  export let autoscroll = true;
  export let prozess = undefined;

  let focusOn;
  let bottomDiv;
  onMount(async () => {
    console.log(["onMount", autofocus, autoscroll]);
    if (autofocus) focusOn.focus();
    if (autoscroll) bottomDiv.scrollIntoView(false);
  });

  let clicked = false;
  let showUpdate = false;
  let newProzess = {
    titel: "",
    text: "",
    aktiv: true,
  };

  $: if (prozess) onChange();
  function onChange() {
    showUpdate = true;
    newProzess = {
      id: prozess.id,
      titel: prozess.titel,
      text: prozess.text,
      aktiv: prozess.aktiv,
      version: prozess.version,
    };
    console.log(["onChange", newProzess]);
  }

  async function onSubmit() {
    try {
      clicked = true;
      if (showUpdate) {
        await updateProzess();
      } else {
        await createProzess();
      }
    } finally {
      clicked = false;
    }
  }
  async function onCancel() {
    visible = false;
  }

  const dispatch = createEventDispatcher();
  function createProzess() {
    return createValue("/api/prozess", newProzess)
      .then((json) => {
        console.log(["createProzess", newProzess, json]);
        visible = false;
        dispatch("create", json);
      })
      .catch((err) => {
        console.log(["createProzess", newProzess, err]);
        toast.push(err.toString());
      });
  }
  function updateProzess() {
    return updatePatch("/api/prozess" + "/" + newProzess.id, newProzess)
      .then((json) => {
        console.log(["updateProzess", newProzess, json]);
        visible = false;
        dispatch("update", json);
      })
      .catch((err) => {
        console.log(["updateProzess", newProzess, err]);
        toast.push(err.toString());
      });
  }
</script>

<form on:submit|preventDefault={onSubmit} on:reset|preventDefault={onCancel}>
  <div class="flex flex-col gap-1">
    <div class="w-full">
      <TextField
        bind:this={focusOn}
        bind:value={newProzess.titel}
        required
        label="Titel"
        placeholder="Bitte einen Text eingeben"
      />
    </div>
    <div class="w-full">
      <TextArea
        bind:value={newProzess.text}
        label="Text"
        placeholder="Bitte einen Text eingeben"
      />
    </div>
  </div>
  <div class="py-4 flex flex-row gap-1 items-baseline">
    <div class="flex-initial">
      <Button type="submit">Ok</Button>
    </div>
    <div class="flex-initial">
      <slot name="cancel">
        <Button type="reset">Abbrechen</Button>
      </slot>
    </div>
  </div>
</form>

<div class="h-0" bind:this={bottomDiv}>&nbsp;</div>

{#if import.meta.env.DEV}
  <details tabindex="-1">
    <summary>JSON</summary>
    <pre>{JSON.stringify(newProzess, null, 2)}</pre>
  </details>
{/if}
