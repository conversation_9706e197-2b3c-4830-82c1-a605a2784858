:keyword: library,java,spring
:term: spring-data-jpa

https://spring.io/projects/spring-data-jpa[Spring Data JPA]
erleichtert die Erstellung von Spring-Boot-Anwendungen mit einem Zugriff auf eine relationale Datenbank mit dem Industriestandard
https://jakarta.ee/specifications/persistence/3.0/jakarta-persistence-spec-3.0.html[Jakarta Persistence][JPA].
Die eigentliche Implementierung von JPA übernimmt dann Spring Data JPA mit
https://hibernate.org/[Hibernate]
als JPA-Provider.
