<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import { toast } from "../components/Toast";
  import { loadAllValue } from "../utils/rest.js";
  import { loadOneValue } from "../utils/rest.js";
  import Button from "../components/Button";
  import IconMail from "../components/IconMail";
  import IconOpenInTab from "../components/IconOpenInTab";
  import IconPrint from "../components/IconPrint";
  import IconSaveAsZip from "../components/IconSaveAsZip";
  import NutzerEditor from "./NutzerEditor.svelte";
  import NutzerGruppe from "./NutzerGruppe.svelte";
  import NutzerProfil from "./NutzerProfil.svelte";
  import NutzerUnterschrift from "./NutzerUnterschrift.svelte";

  export let nutzerId;

  let allAdresseItem = [];
  let allGruppeItem = [];
  let allKanalItem = [];
  let allSpracheItem = [];
  let loading = true;
  onMount(async () => {
    try {
      loading = true;
      allAdresseItem = await loadAllValue("/api/adresse/search/findAllItem");
      console.log(["onMount", allAdresseItem]);
      allGruppeItem = await loadAllValue("/api/gruppe/search/findAllItem");
      console.log(["onMount", allGruppeItem]);
      allKanalItem = await loadAllValue("/api/enum/kanal");
      console.log(["onMount", allKanalItem]);
      allSpracheItem = await loadAllValue("/api/enum/sprache");
      console.log(["onMount", allSpracheItem]);
      await reloadOneNutzer();
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  async function onChange() {
    try {
      loading = true;
      await tick();
      await reloadOneNutzer();
    } finally {
      loading = false;
    }
  }
  async function onCancel() {
    await reloadOneNutzer();
  }

  let nutzer;
  function reloadOneNutzer() {
    return loadOneValue("/api/nutzer/" + nutzerId)
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadOneNutzer", msg]);
        nutzer = json;
      })
      .catch((err) => {
        console.log(["reloadOneNutzer", err]);
        nutzer = {};
        toast.push(err.toString());
      });
  }

  function steckbriefMailJob() {
    const text = [
      "Im Anhang dieser E-Mail befindet sich",
      "dein persönlicher Steckbrief.",
    ].join("\n");
    return {
      to: nutzer.mail,
      bcc: "<EMAIL>",
      subject: "Steckbrief von " + nutzer.name,
      html: "<p>" + text + "</p>",
      text: text,
      attachments: [
        {
          filename: "steckbrief.pdf",
          path: "/doc/nutzer/" + nutzerId,
        },
      ],
    };
  }

  function steckbriefPrintJob() {
    return {
      printer: "nowhere",
      files: [
        {
          path: "/doc/nutzer/" + nutzerId,
          options: ["media=a4"],
        },
      ],
    };
  }

  function steckbriefSaveJob() {
    return {
      archive: "steckbrief.zip",
      files: [
        {
          filename: nutzerId + ".pdf",
          path: "/doc/nutzer/" + nutzerId,
        },
      ],
    };
  }
</script>

<div class="flex flex-col gap-1 ml-2 mr-2">
  {#if nutzer}{#key nutzer}
      <h1>{nutzer.name}</h1>
      <fieldset class="p-4 border-2">
        <legend class="text-xl">Nutzer bearbeiten</legend>
        <NutzerEditor
          on:update={onChange}
          visible={true}
          autofocus={true}
          autoscroll={false}
          {nutzer}
          {allAdresseItem}
          {allSpracheItem}
        >
          <div slot="cancel">
            <Button type="reset" onclick={onCancel}>Verwerfen</Button>
          </div>
        </NutzerEditor>
      </fieldset>
      <fieldset class="p-4 border-2">
        <legend class="text-xl">Unterschrift bearbeiten</legend>
        <NutzerUnterschrift on:update={onChange} visible={true} {nutzer}>
          <div slot="cancel">
            <Button type="reset" onclick={onCancel}>Verwerfen</Button>
          </div>
        </NutzerUnterschrift>
      </fieldset>
      <fieldset class="p-4 border-2">
        <legend class="text-xl">Profil bearbeiten</legend>
        <NutzerProfil
          on:update={onChange}
          visible={true}
          autofocus={false}
          autoscroll={false}
          {nutzer}
          {allKanalItem}
        >
          <div slot="cancel">
            <Button type="reset" onclick={onCancel}>Verwerfen</Button>
          </div>
        </NutzerProfil>
      </fieldset>
      <fieldset class="p-4 border-2">
        <legend class="text-xl">Gruppen bearbeiten</legend>
        <NutzerGruppe
          on:update={onChange}
          visible={true}
          autofocus={false}
          autoscroll={false}
          {nutzer}
          {allGruppeItem}
        >
          <div slot="cancel">
            <Button type="reset" onclick={onCancel}>Verwerfen</Button>
          </div>
        </NutzerGruppe>
      </fieldset>
      <fieldset class="p-4 border-2">
        <legend class="text-xl">Steckbrief anzeigen</legend>
        <div>
          <IconOpenInTab
            path={"/doc/nutzer/" + nutzerId + ".xlsx"}
            title="Steckbrief des Nutzers im Format Excel anzeigen"
            name="table_rows"
            outlined
          />
          <IconOpenInTab
            path={"/doc/nutzer/" + nutzerId + ".pdf"}
            title="Steckbrief des Nutzers im Format PDF anzeigen"
            name="picture_as_pdf"
            outlined
          />
          <IconSaveAsZip
            job={() => steckbriefSaveJob()}
            title="Steckbrief des Nutzers lokal speichern"
            name="download"
            outlined
          />
          <IconMail
            job={() => steckbriefMailJob()}
            title="Steckbrief des Nutzers als E-Mail schicken"
            outlined
          />
          <IconPrint
            job={() => steckbriefPrintJob()}
            title="Steckbrief des Nutzers drucken"
            outlined
          />
        </div>
      </fieldset>
    {/key}{/if}
</div>
