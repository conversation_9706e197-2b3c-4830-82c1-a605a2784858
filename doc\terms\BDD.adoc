:keyword: practice,testing
:term: BDD

https://dannorth.net/introducing-bdd[_Behavior Driven Development_ (kurz BDD)]
ist eine Herangehensweise an die Entwicklung komplexer Software.
Sie hilft da<PERSON>, Missverständnisse zu vermeiden.
Mit BDD wird die Kommunikation zwischen Entwicklern, Testern und Domänenexperten verbessert.
Sie fördert die Erstellung von Tests, die in einer für alle verständlichen Sprache beschrieben werden.
Diese Tests werden als Szenarien formuliert, die den gewünschten Ablauf einer Fähigkeit oder einer Funktion der Software beschreiben.
Ein typisches Format für solche Tests folgt einem Muster mit den Schlüsselworten _Given_, _When_ und _Then_.
