<script>
  import { toast } from "./components/Toast";
  import { fetchInfo } from "./utils/rest.js";
  import { fetchUser } from "./utils/rest.js";
  import Chip from "./components/Chip";

  let disabled = false;
  $: console.log(["disabled", disabled]);

  let jiraInfo;
  function onClickJiraInfo() {
    disabled = true;
    fetchInfo("jira")
      .then((json) => {
        console.log(["onClickJiraInfo", json]);
        disabled = false;
        jiraInfo = json;
      })
      .catch((err) => {
        console.log(["onClickJiraInfo", err]);
        toast.push(err.toString());
        disabled = false;
        jiraInfo = undefined;
      });
  }

  let jiraUser;
  function onClickJiraUser() {
    disabled = true;
    fetchUser("jira")
      .then((json) => {
        console.log(["onClickJiraUser", json]);
        disabled = false;
        jiraUser = json;
        jiraUser.url =
          "https://cardsplus.atlassian.net/jira/people/" + json.key;
      })
      .catch((err) => {
        console.log(["onClickJiraUser", err]);
        toast.push(err.toString());
        disabled = false;
        jiraUser = undefined;
      });
  }
</script>

<div class="flex flex-col gap-1">
  <div class="flex flex-row gap-1 justify-start items-center">
    <div class="w-max">
      <Chip icon="lan" onclick={onClickJiraInfo} {disabled}
        >Server erreichbar?
      </Chip>
    </div>
    <div class="overflow-hidden">
      {#if jiraInfo}
        <span>{jiraInfo.version}</span>
      {/if}
    </div>
  </div>
  <div class="flex flex-row gap-1 justify-start items-center">
    <div class="w-max">
      <Chip icon="person" onclick={onClickJiraUser} {disabled}
        >Konto vorhanden?
      </Chip>
    </div>
    <div class="overflow-hidden">
      {#if jiraUser}
        <a class="truncate" href={jiraUser.url} target="_blank"
          >{jiraUser.url}
        </a>
      {/if}
    </div>
  </div>
</div>
{#if import.meta.env.DEV}
  <details tabindex="-1">
    <summary>JSON</summary>
    <pre>{JSON.stringify(jiraInfo, null, 2)}</pre>
    <pre>{JSON.stringify(jiraUser, null, 2)}</pre>
  </details>
{/if}
