<script>
  import { onMount } from "svelte";
  import { tick } from "svelte";
  import { toast } from "../components/Toast";
  import { loadAllValue } from "../utils/rest.js";
  import { updatePatch } from "../utils/rest.js";
  import { removeValue } from "../utils/rest.js";
  import { Circle } from "svelte-loading-spinners";
  import Icon from "../components/Icon";
  import Checkbox from "../components/Checkbox";
  import TextField from "../components/TextField";
  import ProzessEditor from "./ProzessEditor.svelte";

  let loading = true;
  onMount(async () => {
    try {
      loading = true;
      await reloadAllProzess();
    } catch (err) {
      console.log(["onMount", err]);
      toast.push(err.toString());
    } finally {
      loading = false;
    }
  });

  let prozessId = undefined;
  async function onProzessClicked(prozess) {
    prozessId = prozess.id;
  }
  async function onProzessRemoveClicked(prozess) {
    prozessId = prozess.id;
    await removeProzess(prozess);
  }
  let prozessEditorCreate = false;
  async function onProzessEditorCreateClicked() {
    prozessEditorCreate = true;
  }
  let prozessEditorUpdate = false;
  async function onProzessEditorUpdateClicked(prozess) {
    prozessId = prozess.id;
    prozessEditorUpdate = true;
  }
  $: prozessEditorDisabled = prozessEditorCreate || prozessEditorUpdate;

  let prozessFilter;
  function prozessFilterParameter() {
    if (!prozessFilter) return "";
    return "&titel=" + encodeURIComponent(prozessFilter);
  }
  function prozessSortParameter() {
    return "?sort=titel";
  }
  async function onProzessFilterClicked() {
    try {
      loading = true;
      await tick();
      await reloadAllProzess();
    } finally {
      loading = false;
    }
  }

  let allProzess = [];
  function onCreateProzess(prozess) {
    allProzess = allProzess.toSpliced(0, 0, prozess);
  }
  function onUpdateProzess(prozess) {
    let index = allProzess.findIndex((e) => e.id === prozess.id);
    if (index > -1) allProzess = allProzess.toSpliced(index, 1, prozess);
  }
  function onRemoveProzess(prozess) {
    let index = allProzess.findIndex((e) => e.id === prozess.id);
    if (index > -1) allProzess = allProzess.toSpliced(index, 1);
  }
  function reloadAllProzess() {
    const query = prozessSortParameter() + prozessFilterParameter();
    return loadAllValue("/api/prozess" + query)
      .then((json) => {
        const msg = import.meta.env.DEV ? json : json.length;
        console.log(["reloadAllProzess", query, msg]);
        allProzess = json;
      })
      .catch((err) => {
        console.log(["reloadAllProzess", query, err]);
        allProzess = [];
        toast.push(err.toString());
      });
  }

  function updateProzess(prozess) {
    return updatePatch("/api/prozess/" + prozess.id, prozess)
      .then((json) => {
        console.log(["updateProzess", prozess, json]);
        onUpdateProzess(json);
      })
      .catch((err) => {
        console.log(["updateProzess", prozess, err]);
        toast.push(err.toString());
      });
  }
  function removeProzess(prozess) {
    const text = prozess.titel;
    const hint = text.length > 20 ? text.substring(0, 20) + "..." : text;
    if (!confirm("Prozess '" + hint + "' wirklich löschen?")) return;
    return removeValue("/api/prozess/" + prozess.id)
      .then((json) => {
        console.log(["removeProzess", prozess, json]);
        onRemoveProzess(json);
      })
      .catch((err) => {
        console.log(["removeProzess", prozess, err]);
        toast.push(err.toString());
      });
  }
</script>

<h1 title="Liste der Prozessen, ggfs. gefiltert, jedes Element editierbar">
  Prozess
</h1>
<div class="flex flex-col gap-1 ml-2 mr-2">
  <form on:submit|preventDefault={onProzessFilterClicked}>
    <div class="flex flex-row gap-1 items-center pr-2">
      <div class="w-full">
        <TextField
          bind:value={prozessFilter}
          label="Filter"
          placeholder="Bitte Filterkriterien eingeben"
        />
      </div>
      <div class="w-min">
        <Icon type="submit" name="search" outlined />
      </div>
    </div>
  </form>
  {#if loading}
    <div class="h-screen flex justify-center items-center">
      <Circle size="60" unit="px" duration="1s" />
    </div>
  {:else}
    <table class="table-fixed">
      <thead class="justify-between">
        <tr class="bg-title-100">
          <th class="px-2 py-3 table-cell">
            <span class="text-title-600 text-sm">Aktiv</span>
          </th>
          <th class="px-2 py-3 text-left w-full table-cell">
            <span class="text-title-600">Titel</span>
          </th>
          <th class="px-2 py-3 text-right w-0 table-cell">
            <Icon
              on:click={() => onProzessEditorCreateClicked()}
              disabled={prozessEditorDisabled}
              title="Prozess hinzufügen"
              name="add"
              outlined
            />
          </th>
        </tr>
      </thead>
      <tbody>
        {#if prozessEditorCreate}
          <tr>
            <td class="px-2" colspan="3">
              <ProzessEditor
                bind:visible={prozessEditorCreate}
                on:create={(e) => onCreateProzess(e.detail)}
              />
            </td>
          </tr>
        {/if}
        {#each allProzess as prozess, i}
          <tr
            on:click={(e) => onProzessClicked(prozess)}
            title={prozess.id}
            class:border-l-2={prozessId === prozess.id}
            class:bg-gray-100={i % 2 === 1}
          >
            <td class="px-2 py-3 table-cell">
              <Checkbox
                bind:checked={prozess.aktiv}
                on:change={() => updateProzess(prozess)}
              />
            </td>
            <td class="px-2 py-3 text-left table-cell">
              <div class="text-sm underline text-blue-600">
                <a href={"/prozess/" + prozess.id}>{prozess.titel}</a>
              </div>
            </td>
            <td class="px-2 py-3 table-cell">
              <div
                class="grid grid-cols-1 md:grid-cols-2 items-center gap-1 w-max"
              >
                <Icon
                  on:click={() => onProzessRemoveClicked(prozess)}
                  disabled={prozessEditorDisabled || prozess.aktiv}
                  title="Prozess löschen"
                  name="delete"
                  outlined
                />
                <Icon
                  on:click={() => onProzessEditorUpdateClicked(prozess)}
                  disabled={prozessEditorDisabled}
                  title="Prozess bearbeiten"
                  name="edit"
                  outlined
                />
              </div>
            </td>
          </tr>
          {#if prozessEditorUpdate && prozessId === prozess.id}
            <tr>
              <td class="border-l-4 px-2" colspan="3">
                <ProzessEditor
                  bind:visible={prozessEditorUpdate}
                  on:update={(e) => onUpdateProzess(e.detail)}
                  {prozess}
                />
              </td>
            </tr>
          {/if}
        {:else}
          <tr>
            <td class="px-2" colspan="3">Keine Prozessen</td>
          </tr>
        {/each}
      </tbody>
    </table>
  {/if}
</div>
