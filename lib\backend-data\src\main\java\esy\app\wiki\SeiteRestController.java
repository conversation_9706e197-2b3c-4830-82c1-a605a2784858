package esy.app.wiki;

import esy.api.plan.VorgangStatus;
import esy.api.wiki.Seite;
import esy.auth.JwtRole;
import esy.rest.JsonJpaRestControllerBase;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.rest.core.annotation.RepositoryEventHandler;
import org.springframework.data.rest.webmvc.BasePathAwareController;
import org.springframework.hateoas.EntityModel;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.UUID;

@RepositoryEventHandler
@BasePathAwareController
public class SeiteRestController extends JsonJpaRestControllerBase<Seite, UUID> {

    private final SeiteRepository seiteRepository;

    @Autowired
    public SeiteRestController(
            @NonNull final ApplicationEventPublisher eventPublisher,
            @NonNull final TransactionTemplate transactionTemplate,
            @NonNull final SeiteRepository seiteRepository) {
        super(eventPublisher, transactionTemplate);
        this.seiteRepository = seiteRepository;
    }

    // tag::allowDelete[]
    @Override
    protected boolean allowDelete(@NonNull final Seite value, @NonNull Authentication auth) {
        return hasRole(auth, JwtRole.VERWALTUNG);
    }
    // end::allowDelete[]

    Seite updateStatusTransition(@NonNull final Seite value, @NonNull final VorgangStatus status) {
        return updateInTransaction(value, tx -> {
            value.applyStatusTransition(status);
            return seiteRepository.save(value);
        });
    }

    // tag::patchSeiteStatusTransition[]
    @PatchMapping("/seite/{id}/{status}")
    public ResponseEntity<EntityModel<Seite>> patchSeiteStatusTransition(@PathVariable("id") final UUID id, @PathVariable("status") final VorgangStatus statusNew) {
        final var valueOld = seiteRepository.getReferenceById(id);
        final var valueNew = updateStatusTransition(valueOld, statusNew);
        return ResponseEntity
                .status(HttpStatus.OK)
                .eTag(Long.toString(valueNew.getVersion()))
                .body(EntityModel.of(valueNew));
    }
    // end::patchSeiteStatusTransition[]
}
