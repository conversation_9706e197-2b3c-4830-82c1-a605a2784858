<script>
  import pageRouter from "page";
  import filterProps from "../filterProps.js";
  const props = filterProps(
    ["checked", "clicked", "disabled", "icon", "outlined", "page", "title"],
    $$props
  );
  export let checked = false;
  export let clicked = 0;
  export let disabled = false;
  export let icon;
  export let outlined = false;
  export let page;
  export let title = undefined;
  let element;
  export function focus() {
    element.focus();
  }
  function onClick() {
    checked = !checked;
    clicked++;
  }
</script>

<button
  type="button"
  bind:this={element}
  {...props}
  {title}
  {disabled}
  class:disabled
  class="w-full h-full text-sm text-white rounded uppercase py-2 px-4 disabled:opacity-50 hover:opacity-90 focus:ring bg-primary-500 overflow-hidden"
  class:outlined
  on:click={onClick}
  on:click={pageRouter(page)}
  on:mouseover
  on:focus
  on:blur
>
  <div class="flex flex-row content-center justify-center items-center gap-1">
    <i
      {title}
      aria-hidden="true"
      class="material-icons icon text-xl select-none duration-200 ease-in"
    >
      {icon}
    </i>
    <slot />
  </div>
</button>
