<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.9.xsd">
    <changeSet id="1" author="robert">
        <preConditions onFail="MARK_RAN">
            <not><tableExists tableName="risiko" /></not>
        </preConditions>
        <createTable tableName="risiko">
            <column name="version" type="BIGINT" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="id" type="UUID">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="nutzer_id" type="UUID">
                <constraints nullable="true"/>
            </column>
            <column name="aktiv" type="BOOLEAN" defaultValue="true">
                <constraints nullable="false"/>
            </column>
            <column name="titel" type="VARCHAR(512)">
                <constraints nullable="false"/>
            </column>
            <column name="text" type="VARCHAR(8192)">
                <constraints nullable="false"/>
            </column>
            <column name="termin" type="DATE">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint
                constraintName="uk_risiko"
                tableName="risiko"
                columnNames="titel"
        />
        <addForeignKeyConstraint
                constraintName="fk_risiko_nutzer_id"
                baseTableName="risiko"
                baseColumnNames="nutzer_id"
                referencedTableName="nutzer"
                referencedColumnNames="id"
        />
    </changeSet>
    <changeSet id="2" author="robert">
        <addColumn tableName="risiko">
            <column name="status" type="VARCHAR(64)" defaultValue="I">
                <constraints nullable="false"/>
            </column>
        </addColumn>        
        <sql>
            UPDATE risiko SET status = 'B' WHERE aktiv = true;
            UPDATE risiko SET status = 'X' WHERE aktiv = false;
        </sql>        
        <dropColumn tableName="risiko" columnName="aktiv"/>
    </changeSet>
    <changeSet id="3" author="robert">
        <addColumn tableName="risiko">
            <column name="terminserie" type="VARCHAR(16)" defaultValue="X">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
</databaseChangeLog>
