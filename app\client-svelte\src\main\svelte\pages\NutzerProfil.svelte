<script>
  import { onMount } from "svelte";
  import { createEventDispatcher } from "svelte";
  import { toast } from "../components/Toast";
  import { updatePatch } from "../utils/rest.js";
  import Button from "../components/Button";
  import Checkbox from "../components/Checkbox";
  import Fieldset from "../components/Fieldset";
  import Icon from "../components/Icon";
  import Select from "../components/Select";
  import TextField from "../components/TextField";

  export let visible = false;
  export let autofocus = true;
  export let autoscroll = true;
  export let nutzer;
  export let allKanalItem;

  let clicked = false;
  let focusOn;
  let bottomDiv;
  onMount(async () => {
    console.log(["onMount", autofocus, autoscroll]);
    if (autofocus) focusOn.focus();
    if (autoscroll) bottomDiv.scrollIntoView(false);
  });

  let newNutzer = {
    name: undefined,
    profil: {
      name: undefined,
      chat: false,
      mail: true,
      wiki: false,
    },
    allKanal: [],
  };

  $: if (nutzer && nutzer.id) onChange();
  async function onChange() {
    newNutzer = { ...newNutzer, ...nutzer };
    console.log(["onChange", newNutzer]);
  }

  async function onSubmit() {
    try {
      clicked = true;
      await updateNutzer();
    } finally {
      clicked = false;
    }
  }
  async function onCancel() {
    visible = false;
  }

  function onInsertKanal() {
    newNutzer.allKanal = [
      ...newNutzer.allKanal,
      {
        typ: null,
        wert: "",
        text: "",
      },
    ];
    console.log(["onInsertKanal", newNutzer]);
  }
  function onRemoveKanal(index) {
    newNutzer.allKanal = [
      ...newNutzer.allKanal.slice(0, index),
      ...newNutzer.allKanal.slice(index + 1),
    ];
    console.log(["onRemoveKanal", newNutzer]);
  }

  const dispatch = createEventDispatcher();
  function updateNutzer() {
    return updatePatch("/api/nutzer" + "/" + newNutzer.id, newNutzer)
      .then((json) => {
        console.log(["updateNutzer", json]);
        visible = false;
        dispatch("update", json);
      })
      .catch((err) => {
        console.log(["updateNutzer", newNutzer, err]);
        toast.push(err.toString());
      });
  }
</script>

<form on:submit|preventDefault={onSubmit} on:reset|preventDefault={onCancel}>
  <div class="flex flex-col gap-1">
    <div class="w-full">
      <div class="flex flex-row gap-1 items-baseline">
        <TextField
          bind:this={focusOn}
          bind:value={newNutzer.profil.name}
          label="Kürzel"
          placeholder="Bitte ein Kürzel eingeben"
        />
        <Checkbox bind:checked={newNutzer.profil.chat} label="Chat?" />
        <Checkbox bind:checked={newNutzer.profil.mail} label="Mail?" />
        <Checkbox bind:checked={newNutzer.profil.wiki} label="Wiki?" />
      </div>
    </div>
    {#each newNutzer.allKanal as kanal, i}
      <Fieldset>
        <span slot="title">{i + 1 + ". Kanal"}</span>
        <div class="flex flex-row gap-1 items-baseline">
          <div class="w-1/5">
            <Select
              bind:value={kanal.typ}
              valueGetter={(v) => v?.value}
              allItem={allKanalItem}
              required
              label="Typ"
            />
          </div>
          <div class="w-2/5">
            <TextField bind:value={kanal.wert} required label="Wert" />
          </div>
          <div class="w-2/5">
            <TextField bind:value={kanal.text} required label="Text" />
          </div>
          <div class="place-self-center">
            <Icon onclick={() => onRemoveKanal(i)} name="delete" outlined />
          </div>
        </div>
      </Fieldset>
    {/each}
    <div class="place-self-end">
      <Icon onclick={() => onInsertKanal()} name="add" outlined />
    </div>
  </div>
  <div class="py-4 flex flex-row gap-1 items-baseline">
    <div class="flex-initial">
      <Button type="submit">Ok</Button>
    </div>
    <div class="flex-initial">
      <slot name="cancel">
        <Button type="reset">Abbrechen</Button>
      </slot>
    </div>
  </div>
</form>

<div class="h-0" bind:this={bottomDiv}>&nbsp;</div>

{#if import.meta.env.DEV}
  <details tabindex="-1">
    <summary>JSON</summary>
    <pre>{JSON.stringify(newNutzer, null, 2)}</pre>
  </details>
{/if}
