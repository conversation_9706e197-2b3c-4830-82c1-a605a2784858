<script>
  import { createEventDispatcher } from "svelte";
  import { print } from "../../utils/rest.js";
  import { toast } from "../Toast";
  import filterProps from "../filterProps.js";
  const props = filterProps(
    ["disabled", "name", "outlined", "title", "job"],
    $$props
  );
  export let disabled = false;
  export let name;
  export let outlined = false;
  export let title;
  export let job;
  const dispatch = createEventDispatcher();
  function onClick() {
    const toastId = toast.push(title, { duration: 60000 });
    const json = job instanceof Function ? job() : job;
    dispatch("sending", json);
    print(json)
      .then((log) => {
        console.log(["print", json, log]);
        toast.pop(toastId);
        dispatch("success", json);
      })
      .catch((err) => {
        console.log(["print", json, err]);
        toast.pop(toastId);
        toast.push(err.toString());
        dispatch("failure", json);
      });
  }
  let element;
  export function focus() {
    element.focus();
  }
</script>

<button
  type="button"
  bind:this={element}
  {...props}
  {title}
  {disabled}
  aria-label="Print"
  class:disabled
  class="text-xl text-white w-12 h-12 rounded-full p-2 disabled:opacity-50 hover:opacity-90 focus:ring bg-primary-500"
  class:outlined
  on:click={onClick}
  on:click
  on:mouseover
  on:focus
  on:blur
>
  <div class="flex justify-center items-center">
    <i {title} class="material-icons icon select-none">
      <span class="text-xl">{name}</span>
    </i>
  </div>
</button>
